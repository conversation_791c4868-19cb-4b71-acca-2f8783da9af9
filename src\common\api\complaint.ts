import request from '@/utils/request'

// 请求体接口
export interface ComplaintPayload {
  [key: string]: any
}

// 通用响应结构
interface BaseResponse<T> {
  success: boolean
  msg?: string
  data?: T
  pageInfo?: {
    total?: number
  }
}

// 投诉事件总览响应数据接口
export interface ComplaintOverviewResponse extends BaseResponse<Array<{
  ztsl?: number // 总投诉量
  byljtsl?: number // 本月累计条数
  slsl?: number // 受理数量
  sll?: string | number // 受理率
  bjsl?: number // 办结数量
  wcl?: string | number // 完成率
  zbsl?: number // 转办数量
  hfsl?: number // 回访数量
  gdsl?: number // 归档数量
}>> {}

// 投诉渠道分析响应数据接口
export interface ChannelAnalysisResponse extends BaseResponse<Array<{
  source?: string // 渠道来源（如"电话投诉"、"网络投诉"等）
  num?: number // 数量
  ratio?: string | number // 比例（如 "26.00%" 或 0.26）
  total?: number // 总数
}>> {}

// 投诉趋势分析响应数据接口
export interface TrendAnalysisResponse extends BaseResponse<Array<{
  number_value?: number // 数值
  stat_date?: string // 统计日期或日期范围（如 "2025/10/19-2025/10/25"）
  type?: string // 类型（week/month/day）
}>> {}

// 区域办结率排名响应数据接口
export interface RegionCompletionRankingResponse extends BaseResponse<Array<{
  area?: string // 区域
  ratio?: string | number // 办结率（可能是 "50.00%" 或 0.5）
}>> {}

// 投诉类型分布响应数据接口
export interface TypeDistributionResponse extends BaseResponse<Array<{
  type?: string // 类型
  num?: number // 数量
  total?: number // 总数
  ratio?: string | number // 比例（可能是 "50.00%" 或 0.5）
}>> {}

// 任务督办列表响应数据接口
export interface TaskSupervisionListResponse extends BaseResponse<Array<{
  code?: string // 督办编号
  content?: string // 投诉内容
  person?: string // 督办人
  urgency?: string // 紧急程度
}>> {}

/**
 * 投诉监管-投诉事件总览
 */
export function tsjsc_tssjzl(
  payload: ComplaintPayload = {},
): Promise<ComplaintOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/tsjsc/tssjzl/AC9F9DAD8563D4E86C281CF8B3B52C68',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 投诉监管-投诉渠道分析
 */
export function tsjsc_tsqdfx(
  payload: ComplaintPayload = {},
): Promise<ChannelAnalysisResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/tsjsc/tsqdfx/AC9F9DAD8563D4E831B00F95B441EEB6',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 投诉监管-投诉趋势分析
 * @param payload - 入参：{ type: 'month' | 'day' | 'week' }
 * @description 注意：参数映射关系为 day->周数据, week->月数据, month->年数据
 */
export function tsjsc_tsqsfx(
  payload: { type: 'month' | 'day' | 'week' },
): Promise<TrendAnalysisResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/tsjsc/tsqsfx/AC9F9DAD8563D4E87ABB2F9FDA2728DB',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 投诉监管-区域办结率排名
 * @param payload - 入参：{ type: 'desc' | 'asc' }
 */
export function tsjsc_qybjlpm(
  payload: { type: 'desc' | 'asc' },
): Promise<RegionCompletionRankingResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/tsjsc/qybjlpm/FAA124118194F439E70441E12F698B00',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 投诉监管-投诉类型分布
 * @param payload - 入参：{ classify: 'tslx' | 'wtlx' } (投诉类型 | 问题类型)
 */
export function tsjsc_tslxfb(
  payload: { classify: 'tslx' | 'wtlx' },
): Promise<TypeDistributionResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/tsjsc/tslxfb/AC9F9DAD8563D4E84742F99CB6D9C408',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 投诉监管-任务督办列表
 * @param payload - 入参：{ urgency: '紧急' | '正常' | '超期' }
 */
export function tsjsc_rwdblb(
  payload: { urgency: '紧急' | '正常' | '超期' },
): Promise<TaskSupervisionListResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/tsjsc/rwdblb/CD3C4A86915119645DD062FD58627768',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

