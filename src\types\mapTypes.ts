// Define basic types for AMap and Loca objects based on usage
export interface AMapMap {
  // on(event: 'complete' | 'click', handler: (e: { lnglat: { lng: number; lat: number } }) => void): void
  on(event: string, handler: (e: any) => void): void
  remove(overlay: any): void
  add(overlay: any): void
  setFitView(overlays: any[], animate?: boolean, padding?: number[]): void
  setBounds(bounds: any, animate?: boolean, padding?: number[]): void
  setZoomAndCenter(zoom: number, center: [number, number], animate?: boolean, duration?: number): void
  getCenter(): any
  destroy(): void
}

export interface AMapMarker {
  on(event: 'mouseover' | 'mouseout' | 'click' | string, handler: (e: any) => void): void
  setTop(isTop: boolean): void
  setContent(content: string | HTMLElement): void
  setOffset(offset: any): void
  getExtData(): any
  dom: HTMLElement
}

export interface AMapInfoWindow {
  open(map: AMapMap, position: [number, number]): void
  close(): void
  setContent(content: string | HTMLElement): void
  setPosition(position: [number, number]): void
  // ...其他 AMapInfoWindow 方法
}

export interface AMapPolygon {
  path: any
  strokeColor: string
  strokeWeight: number
  strokeOpacity: number
  fillColor: string
  fillOpacity: number
  zIndex: number
}

export interface AMapPolyline {
  on(eventName: string, handler: (event: any) => void): void
  setOptions(options: any): void
  path: any
  strokeColor: string
  strokeWeight: number
  strokeOpacity: number
  extData: any
  getExtData(): any
}

export interface AMapInstance {
  Map: new (container: string, options: any) => AMapMap
  Polygon: new (options: any) => AMapPolygon
  Marker: new (options: any) => AMapMarker
  Polyline: new (options: any) => AMapPolyline
  Pixel: new (x: number, y: number) => any
  InfoWindow: new (opts?: any) => AMapInfoWindow
  Bounds: new (southWest: any, northEast: any) => any
  GeoJSON: any
  plugin(name: string | string[], callback: () => void): void
}

export interface LocaContainerInstance {
  pointLight: { intensity: number }
  ambLight: { intensity: number }
  animate: { start: () => void }
  add(layer: any): void
  remove(layer: any): void
  destroy(): void
}

export interface LocaHeatMapLayer {
  setSource(source: any, options: any): void
}

interface LocaGeoJSONSource {
  new (options: any): any
}

export interface LocaLoadedInstance {
  Container: new (options: { map: AMapMap | null }) => LocaContainerInstance
  HeatMapLayer: new (options: any) => LocaHeatMapLayer
  GeoJSONSource: new (options: any) => LocaGeoJSONSource
  PolygonLayer: new (options: any) => any
}

// Combined type for the object returned by AMapLoader.load
export interface AMapLoaderResult {
  AMap: AMapInstance
  Loca: LocaLoadedInstance
}
// Define interface for heatData
export interface HeatDataItem {
  num: number
  lnglat: [number, number]
}

export interface Position {
  lng: number
  lat: number
}

export interface GasCompanyItem {
  geometry: {
    coordinates: [string, string]
  }
  properties: {
    qymc?: string
    fddbrxm?: string
    zzzs?: string
    jyzt?: string
  }
}

export interface StationItem {
  geometry: {
    coordinates: [string, string]
  }
  properties: {
    czmc?: string
    qymc?: string
    fzrmc?: string
    wz?: string
    fzrlxdh?: string
  }
}

export interface PipelineItem {
  geometry: string // JSON string
  properties: any
}

// 定义通用的配置接口
export interface GenericMarkerConfig {
  id: string | number
  position: Position
  iconUrl?: string // 自定义图标URL
  content?: string // 自定义标记的HTML内容
  extData?: any
  layerType?: string // 图层类型，如 'alarm', 'yhsb', 'gwsb'
  getInfoWindowContent?: (data: any) => string // 用于生成信息窗口内容的函数
}

export interface GenericPolylineConfig {
  id: string | number
  path: Position[]
  strokeColor?: string
  strokeWeight?: number
  strokeOpacity?: number
  extData?: any
  getInfoWindowContent?: (data: any) => string
}
