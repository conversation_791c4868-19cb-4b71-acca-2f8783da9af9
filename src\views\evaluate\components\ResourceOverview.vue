<template>
  <div class="panel-container">
    <div class="panel-header">量化评估概况</div>
    <div class="p-4 panel-content">
      <div class="status-indicators">
        <div class="status-item animate-pulse">
          <div class="status-value">{{ count }}</div>
          <div class="status-label">评价企业数</div>
        </div>
        <div class="status-item animate-pulse">
          <div class="status-value">{{ standardCount }}</div>
          <div class="status-label">达标企业数</div>
        </div>
        <div class="status-item animate-pulse">
          <div class="status-value">{{ toBeRectifiedCount }}</div>
          <div class="status-label">待整改企业数</div>
        </div>
      </div>
      <div class="chart-section">
        <div class="rate-icon"></div>
        <div ref="chartRef" class="donut-chart"></div>
        <div class="triangle"></div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import * as echarts from 'echarts'
import { getSafetyAssessmentIndicator } from '@/common/api/safetyAssessment'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

interface IndicatorResponse {
 count: number
 standardCount: number
 unStandardCount: number
 standardRate?: number | string
 toBeRectifiedCount: number
 addToBeRectifiedCount: number
 underRectificationCount: number
 progressRate?: number | string
 acceptedCount: number
 thisMonthAcceptedCount: number
 notRectificationCount: number
}


const indicator = ref<IndicatorResponse | null>(null)
const yAxisLabel = ref<string>('整体达标率')
const percent = ref<number>(0)

const count = computed(() => indicator.value?.count ?? 0)
const standardCount = computed(() => indicator.value?.standardCount ?? 0)
const toBeRectifiedCount = computed(() => indicator.value?.toBeRectifiedCount ?? 0)

const buildOption = (label: string, value: number) => {
 return {
   backgroundColor: 'transparent',
   grid: {
     left: '20px',
     right: '20px',
     top: 0,
     height: 80,
     bottom: 0,
     containLabel: true,
   },
   tooltip: {
     show: false,
   },
   xAxis: {
     type: 'value',
     max: 100,
     axisTick: {
       show: false,
     },
     axisLine: {
       show: false,
     },
     splitLine: {
       show: false,
     },
     axisLabel: {
       show: false,
     },
   },
   yAxis: [
     {
       type: 'category',
       inverse: true,
       axisLine: {
         show: false,
       },
       axisTick: {
         show: false,
       },
       axisLabel: {
         show: false,
       },
       data: [label],
     },
     {
       inverse: true,
       offset: 0,
       axisLabel: {
         show: false,
       },
       axisLine: {
         show: false,
       },
       axisTick: {
         show: false,
       },
       data: [{ value: 0, key: value }],
     },
   ],
   series: [
     {
       type: 'bar',
       barWidth: 20,
       data: [value],
       showBackground: true,
       backgroundStyle: {
         color: 'rgba(140, 255, 255, 0.15)',
       },
       itemStyle: {
         color: {
           type: 'linear',
           x: 0,
           y: 0,
           x2: 1,
           y2: 0,
           colorStops: [
             {
               offset: 0,
               color: 'rgba(64, 159, 255, 1)',
             },
             {
               offset: 1,
               color: 'rgba(71, 235, 235, 1)',
             },
           ],
         },
       },
       z: 2,
     },
     // 分隔
     {
       type: 'pictorialBar',
       itemStyle: {
         normal: {
           color: '#0E3169',
         },
       },
       symbolRepeat: 'fixed',
       symbolMargin: 8,
       symbol: 'rect',
       symbolClip: true,
       symbolSize: [2, 20],
       symbolPosition: 'start',
       symbolOffset: [-2, 0],
       data: [100],
       label: {
         show: true,
         position: 'right',
         offset: [20, 0],
         fontSize: 16,
         color: '#0E3169',
       },
       z: 4,
       zlevel: 1,
     },
     // 左侧标签
     {
       type: 'scatter',
       symbolSize: 0,
       data: [[0, 0]],
       label: {
         show: true,
         position: [0, -15],
         color: '#fff',
         fontSize: 14,
         align: 'left',
         verticalAlign: 'bottom',
         formatter: () => label,
       },
       z: 4,
     },
     // 右侧数值标签
     {
       type: 'scatter',
       symbolSize: 0,
       data: [[100, 0]],
       label: {
         show: true,
         position: [0, -15],
         color: '#66FFFF',
         fontSize: 20,
         align: 'right',
         verticalAlign: 'bottom',
         formatter: () => `${value}%`,
       },
       z: 4,
     },
   ],
 }
}

const fetchIndicator = async () => {
  try {
    const res: any = await getSafetyAssessmentIndicator()

    // 兼容返回为 { msg, success, data: [ {..} ], pageInfo } 或 { data: {..} } 或直接对象
    let item: any = null
    if (res && Array.isArray(res.data)) {
      item = res.data[0] ?? null
    } else if (res && res.data && typeof res.data === 'object') {
      item = res.data
    } else if (res && typeof res === 'object' && !Array.isArray(res)) {
      item = res
    }

    if (!item) {
      return
    }

    indicator.value = item as IndicatorResponse

    // 轴标签（优先使用后端返回的文案）
    yAxisLabel.value = typeof item.standardRate === 'string' ? (item.standardRate as string) : '整体达标率'

    // 百分比（优先使用后端返回的数值；若为字符串尝试提取；否则用达标数/总数）
    let rateNum = 0
    const sr = item.standardRate
    if (typeof sr === 'number') {
      rateNum = Number(sr)
    } else if (typeof sr === 'string') {
      const m = sr.match(/[\d.]+/)
      if (m) rateNum = Number(m[0])
    }
    if ((!rateNum || Number.isNaN(rateNum)) && item.count && item.standardCount) {
      rateNum = Math.round((Number(item.standardCount) / Number(item.count)) * 100)
    }
    percent.value = rateNum

    chart?.setOption(buildOption(yAxisLabel.value, percent.value), true)
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error('获取量化评估概况失败:', e)
  }
}
const initchart = () => {
 if (!chartRef.value) return

 chart = echarts.init(chartRef.value)

 chart.setOption(buildOption(yAxisLabel.value, percent.value))
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
 initchart()
 fetchIndicator()
 window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
@import '@/styles/index.css';

.panel-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-indicators {
  width: 100%;
  height: 128px;
  display: flex;
  justify-content: space-between;
}

.status-item {
  display: flex;
  width: 32%;
  background: url('@/assets/evaluate/resource-ind-bg.png') no-repeat center center;
  background-size: 120px 52px;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(64, 159, 255, 0) 0%, #409fff 49%, rgba(64, 159, 255, 0) 100%) 1;
}

.status-value {
  font-family: YouSheBiaoTiHei;
  font-size: 24px;
  font-weight: bold;
  line-height: 28px;
  text-align: center;
  color: #fff;
  white-space: nowrap;
}

.status-label {
  font-family: NotoSansSC;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  text-align: center;
  letter-spacing: normal;
  color: #ffffff;
}

.chart-section {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  height: 80px;
  background: linear-gradient(
    90deg,
    rgba(64, 159, 255, 0) 0%,
    rgba(64, 159, 255, 0.15) 50%,
    rgba(64, 159, 255, 0) 100%
  );
  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(64, 159, 255, 0) 0%, #409fff 49%, rgba(64, 159, 255, 0) 100%) 1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    border-top-width: 1px;
    border-left-width: 1px;
    border-style: solid;
    border-color: #66ffff;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 8px;
    border-top-width: 1px;
    border-right-width: 1px;
    border-style: solid;
    border-color: #66ffff;
  }
}

.triangle {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 8px;

  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 8px;
    height: 8px;
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-style: solid;
    border-color: #66ffff;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 8px;
    height: 8px;
    border-bottom-width: 1px;
    border-right-width: 1px;
    border-style: solid;
    border-color: #66ffff;
  }
}

.rate-icon {
  margin-left: 20px;
  width: 48px;
  height: 48px;
  background: url('@/assets/evaluate/resource-rate-icon.png') no-repeat center center;
  background-size: contain;
}

.donut-chart {
  flex: 1;
  height: 60px;
}
</style>
