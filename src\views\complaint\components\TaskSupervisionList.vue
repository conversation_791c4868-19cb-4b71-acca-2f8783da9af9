<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">任务督办列表</div>
      <div class="header-dropdown">
        <div class="btn-group">
          <button class="period-btn" :class="{ active: selectedFilter === 'timeout' }" @click="handleFilterChange('timeout')">
            超期
          </button>
          <button class="period-btn" :class="{ active: selectedFilter === 'urgent' }" @click="handleFilterChange('urgent')">
            紧急
          </button>
        </div>
      </div>
    </div>
    <div class="panel-content p-4">
      <div class="task-list">
        <div
          v-for="item in filteredList"
          :key="item.id"
          class="task-item"
        >
          <!-- 上部分：图标、编号、标题、状态 -->
          <div class="task-upper">
            <!-- 左侧图标 -->
            <div class="task-icon">
              <img :src="item.icon" alt="" />
            </div>
            
            <!-- 中间内容区 -->
            <div class="task-main">
              <div class="task-code">督办编号：{{ item.code }}</div>
              <div class="task-title">{{ item.title }}</div>
            </div>
            
            <!-- 右侧状态标签 -->
            <div class="task-status" :class="item.type">
              {{ item.type === 'timeout' ? '超时' : '紧急' }}
            </div>
          </div>
          
          <!-- 下部分：督办人和剩余天数 -->
          <div class="task-lower">
            <div class="task-info-item">
              <i class="info-icon">👤</i>
              <span class="info-label">督办人：</span>
              <span class="info-value">{{ item.follower }}</span>
            </div>
            <div class="task-info-item">
              <i class="info-icon">📅</i>
              <span class="info-label">剩余：</span>
              <span class="info-value">{{ item.left }}天</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import urgent from '@/assets/complaint/urgent-alarm.png'
import timeout from '@/assets/complaint/timeout-alarm.png'
import { tsjsc_rwdblb } from '@/common/api/complaint'

const selectedFilter = ref<string>('timeout')

interface TaskItem {
  id: number | string
  type: string
  icon: string
  statusIcon: string
  code: string
  title: string
  follower: string
  left: number
}

// 完整的任务列表数据
const allList = ref<TaskItem[]>([])

// 加载数据
const loadData = async (urgency: '紧急' | '正常' | '超期') => {
  try {
    const response = await tsjsc_rwdblb({ urgency })
    if (response.success && response.data && response.data.length > 0) {
      allList.value = response.data.map((item, index) => {
        // 根据接口返回的紧急程度判断类型
        const urgencyLevel = item.urgency || urgency
        const isUrgent = urgencyLevel.includes('紧急')
        const type = isUrgent ? 'urgent' : 'timeout'
        
        return {
          id: index + 1,
          type: type,
          icon: type === 'urgent' ? urgent : timeout,
          statusIcon: type === 'urgent' ? urgent : timeout,
          code: item.code || '',
          title: item.content || '',
          follower: item.person || '',
          left: Math.floor(Math.random() * 10) + 1 // 剩余天数暂时随机生成，需要根据实际接口调整
        }
      })
    }
  } catch (error) {
    console.error('加载任务督办列表数据失败:', error)
  }
}

// 由于接口已经根据 urgency 参数过滤，直接显示所有数据
const filteredList = computed(() => {
  return allList.value
})

const handleFilterChange = (filter: string) => {
  selectedFilter.value = filter
  // 映射 UI 过滤条件到接口参数
  const urgencyMap: { [key: string]: '紧急' | '正常' | '超期' } = {
    'urgent': '紧急',
    'timeout': '超期'
  }
  loadData(urgencyMap[filter] || '超期')
}

onMounted(() => {
  // 默认加载超期数据
  loadData('超期')
})
</script>

<style scoped lang="scss">
@import '@/styles/index.css';

.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}

.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}

// 任务列表样式
.task-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 100%;
  overflow-y: auto;
  padding-right: 4px;
  
  // 滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(102, 255, 255, 0.3);
    border-radius: 3px;
    
    &:hover {
      background: rgba(102, 255, 255, 0.5);
    }
  }
}

.task-item {
  display: flex;
  flex-direction: column;
  padding: 12px;
  background: linear-gradient(135deg, rgba(18, 60, 128, 0.4), rgba(11, 46, 115, 0.4));
  border-radius: 6px;
  border: 1px solid rgba(64, 159, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    background: linear-gradient(135deg, rgba(18, 60, 128, 0.5), rgba(11, 46, 115, 0.5));
    border-color: rgba(64, 159, 255, 0.4);
    transform: translateX(2px);
  }
}

// 上部分布局
.task-upper {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(64, 159, 255, 0.15);
}

.task-icon {
  flex-shrink: 0;
  
  img {
    width: 48px;
    height: 48px;
    object-fit: contain;
  }
}

.task-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.task-code {
  color: #fff;
  font-size: 14px;
  font-weight: 400;
}

.task-title {
  color: rgba(255, 255, 255, 0.85);
  font-size: 13px;
  font-weight: 300;
  line-height: 1.4;
}

.task-status {
  flex-shrink: 0;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 400;
  align-self: flex-start;
  
  &.timeout {
    background: rgba(255, 68, 68, 0.15);
    color: #FF6B6B;
    border: 1px solid rgba(255, 68, 68, 0.4);
  }
  
  &.urgent {
    background: rgba(255, 198, 26, 0.15);
    color: #FFC61A;
    border: 1px solid rgba(255, 198, 26, 0.4);
  }
}

// 下部分布局
.task-lower {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
  padding-top: 10px;
}

.task-info-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
}

.info-icon {
  font-style: normal;
  font-size: 14px;
  opacity: 0.8;
}

.info-label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 300;
}

.info-value {
  color: #fff;
  font-weight: 400;
}
</style>
