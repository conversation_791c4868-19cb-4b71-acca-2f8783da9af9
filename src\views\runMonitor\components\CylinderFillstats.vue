<template>
  <div class="panel-container-col">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">气瓶充装统计</div>
      <div class="header-actions">
        <button class="range-btn" :class="{ active: selectedRange === 'week' }" @click="setRange('week')">
          近一周
        </button>
        <button class="range-btn" :class="{ active: selectedRange === 'month' }" @click="setRange('month')">
          近一月
        </button>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div class="chart-section">
        <div ref="chartRef" class="donut-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { qyyx_qpcztj_7day, qyyx_qpcztj_4week } from '@/common/api/runMonitor' // 新增：导入充装统计接口
import { createSignedPayload } from '@/common/utils/auth' // 新增：导入签名工具

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
let currentIndex = -1

let color = ['#47EBEB', '#6E30E8', '#C0FFB3']

// 修改：默认 option 不再依赖本地 mock 数据，xAxis/series 初始为空，tooltip 更稳健
const option = {
  color,
  grid: {
    top: 40,
    left: 24,
    bottom: 30,
    right: 8,
  },
  legend: {
    top: 0,
    inactiveBorderWidth: 0,
    textStyle: {
      color: '#fff',
    },
  },
  tooltip: {
    show: true,
    trigger: 'axis',
    axisPointer: {
      lineStyle: {
        color: '#47EBEB',
      },
    },
    backgroundColor: 'rgba(11, 46, 115, 0.6)',
    borderColor: '#409FFF',
    backdropFilter: 'blur(4px)',
    textStyle: {
      color: '#fff',
    },
    formatter: (p: any) => {
      if (!p || !p.length) return ''
      const axisVal = p[0].axisValue ?? ''
      return (
        axisVal +
        p
          .map((i: any) => {
            const name = i.seriesName ?? i.name ?? ''
            return `<br/><div style="display:inline-block;margin-right:4px;width:12px;height:12px;background:${i.color};border-radius: 50%;"></div><div style="display:inline-block;">${name}: ${i.value}</div>`
          })
          .join('')
      )
    },
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#fff',
        type: 'solid',
        opacity: 0.3,
      },
    },
    boundaryGap: false,
    data: [], // 不使用本地 mock，运行时由接口填充
  },
  yAxis: {
    type: 'value',
    name: '单位：Mpa',
    nameTextStyle: {
      color: '#fff',
      align: 'left',
    },
    splitLine: {
      lineStyle: {
        color: '#fff',
        opacity: 0.3,
        type: 'dashed',
      },
    },
    axisLabel: {
      color: '#fff',
    },
  },
  series: [], // 初始无数据，后端数据到达时填充
}

// 请根据实际项目把 key 放到安全的配置文件或环境变量中，这里为演示使用占位符
const API_KEY = '9900J7T2J91992'

// 新增：选择范围（'week' | 'month'）
const selectedRange = ref<'week' | 'month'>('week')

// 新增：切换范围并更新图表（当前仅触发图表刷新，占位 update 实现）
const setRange = (range: 'week' | 'month') => {
  if (selectedRange.value === range) return
  selectedRange.value = range
  updateChartForRange(range)
}

// 新增：根据传入的数据动态构建 option（复用现有 option 的结构并替换 xAxis 与 series）
const buildOptionFromData = (d: any[]) => {
  // 深拷贝基础 option，避免修改原始 option
  const newOpt: any = JSON.parse(JSON.stringify(option))
  newOpt.xAxis = newOpt.xAxis || {}
  newOpt.xAxis.data = ['', ...d.map((i: any) => i.label), '']

  // 构建 series（与原来生成方式保持一致）
  newOpt.series = (d[0]?.value ?? []).map((item: any, index: number) => {
    return {
      name: item.name,
      type: 'line',
      smooth: 0.5,
      symbol: 'circle',
      symbolSize: 8,
      lineStyle: {
        color: color[index],
      },
      itemStyle: {
        color: (p: any) => {
          if (p.dataIndex !== 0 && p.dataIndex !== d.length + 1) return color[index]
        },
        borderColor: '#fff',
        borderWidth: 1,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: color[index] + '4D' },
            { offset: 1, color: color[index] + '00' },
          ],
        },
      },
      data: [
        d.reduce((a: any, b: any) => a + b.value[index].value, 0) / d.length,
        ...d.map((i: any) => i.value[index]),
        d.reduce((a: any, b: any) => a + b.value[index].value, 0) / d.length,
      ],
    }
  })

  return newOpt
}

// 新增：处理后端 { date, enterprise, num } 格式并返回用于 setOption 的 newOpt
const processEnterpriseData = (list: any[]) => {
  // 规范字段名：支持 date/enterprise/num 或 date/enterprise/value 等
  const getDate = (it: any) => String(it.date ?? it.day ?? it.datetime ?? '')
  const getEnterprise = (it: any) => String(it.enterprise ?? it.name ?? it.company ?? '').trim()
  const getNum = (it: any) => Number(it.num ?? it.value ?? 0)

  // 提取并排序日期
  const dates = Array.from(new Set(list.map(getDate))).filter(Boolean)
  dates.sort((a, b) => new Date(a.replace(/\//g, '-')).getTime() - new Date(b.replace(/\//g, '-')).getTime())

  // 提取企业类型
  const enterprises = Array.from(new Set(list.map(getEnterprise))).filter(Boolean)

  // 为每个企业构建按日期排列的数值数组（同 date/enterprise 多条求和）
  const series = enterprises.map((ent, idx) => {
    const values = dates.map(d =>
      list
        .filter((it: any) => getDate(it).trim() === d && getEnterprise(it) === ent)
        .reduce((s: number, it: any) => s + getNum(it), 0),
    )
    return {
      name: ent,
      type: 'line',
      smooth: 0.5,
      symbol: 'circle',
      symbolSize: 8,
      lineStyle: { color: color[idx % color.length] },
      itemStyle: { color: color[idx % color.length], borderColor: '#fff', borderWidth: 1 },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: color[idx % color.length] + '4D' },
            { offset: 1, color: color[idx % color.length] + '00' },
          ],
        },
      },
      data: values,
    }
  })

  // 构建 new option（直接用 dates 作为 xAxis，不再填充额外点）
  const newOpt: any = JSON.parse(JSON.stringify(option))
  newOpt.xAxis = newOpt.xAxis || {}
  newOpt.xAxis.data = dates
  newOpt.series = series

  // 修正 tooltip，基于当前 series/values 显示
  newOpt.tooltip = {
    show: true,
    trigger: 'axis',
    axisPointer: { lineStyle: { color: color[0] } },
    backgroundColor: 'rgba(11, 46, 115, 0.6)',
    borderColor: '#409FFF',
    backdropFilter: 'blur(4px)',
    textStyle: { color: '#fff' },
    formatter: (p: any) => {
      if (!p || !p.length) return ''
      return (
        (p[0].axisValue ?? '') +
        p
          .map(
            (i: any) =>
              `<br/><div style="display:inline-block;margin-right:4px;width:12px;height:12px;background:${i.color};border-radius: 50%;"></div><div style="display:inline-block;">${i.seriesName}: ${i.value}</div>`,
          )
          .join('')
      )
    },
  }

  return { newOpt, dates, enterprises }
}

// 修改：根据 range 调用对应接口并刷新图表，优先处理 {date, enterprise, num} 形式
const updateChartForRange = async (range: 'week' | 'month') => {
  try {
    const api = range === 'week' ? qyyx_qpcztj_7day : qyyx_qpcztj_4week
    const signed = createSignedPayload({}, API_KEY)
    const res = await api(signed)
    const body = res?.data ?? res

    let list: any[] | null = null
    if (Array.isArray(body)) {
      list = body
    } else if (Array.isArray(body?.data)) {
      list = body.data
    } else if (Array.isArray(body?.result)) {
      list = body.result
    }

    if (!Array.isArray(list) || list.length === 0) {
      console.warn('气瓶充装统计接口未返回有效数组，保留原图表', res)
      return
    }

    // 如果数据项包含 date 与 enterprise 字段，则优先按该结构处理
    const sample = list[0]
    const hasDateEnterprise =
      ('date' in sample || 'day' in sample || 'datetime' in sample) &&
      ('enterprise' in sample || 'name' in sample || 'company' in sample)

    if (hasDateEnterprise) {
      const { newOpt, dates, enterprises } = processEnterpriseData(list)
      console.log('处理后的横坐标（dates）：', dates)
      chart?.setOption(newOpt, true)
      console.debug('气瓶充装统计已应用企业分线数据，types:', enterprises)
      return
    }

    // 否则尝试兼容旧格式（保持原有处理）
    if (Array.isArray(list) && list.length > 0 && Array.isArray(list[0]?.value)) {
      const newOption = buildOptionFromData(list)
      chart?.setOption(newOption, true)
    } else {
      console.warn('气瓶充装统计接口返回格式不完全符合预期，保留原有图表', res)
    }
  } catch (err) {
    console.error('获取气瓶充装统计失败', err)
  }
}

// 占位：初始图表渲染
const initchart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  chart.setOption(option)
  startHighlightAnimation()
}

// 修改：高亮轮播从图表当前 option 动态获取数据长度，避免引用已删除的 mock
const startHighlightAnimation = () => {
  if (!chart) return

  // 尝试从当前图表 option 获取第一个系列的数据长度
  const currentOpt: any = chart.getOption ? (chart.getOption() as any) : null
  const dataCount =
    currentOpt?.series && currentOpt.series[0] && Array.isArray(currentOpt.series[0].data)
      ? currentOpt.series[0].data.length
      : 0

  if (dataCount === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮（如果存在）
    if (currentIndex >= 0) {
      chart?.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: currentIndex,
      })
    }

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initchart()
  // 初次加载时根据默认选中范围请求数据
  updateChartForRange(selectedRange.value)
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.panel-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-section {
  width: 100%;
  height: 376px;
}

.donut-chart {
  width: 100%;
  height: 100%;
}

/* 调整：将按钮整体下移并减小高度 */
.header-actions {
  display: flex;
  gap: 8px;
  margin-top: 6px; /* 向下移动按钮 */
  align-items: center;
}
/* 减小按钮高度与内边距，使视觉更低 */
.range-btn {
  padding: 2px 8px;
  height: 26px;
  line-height: 22px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.12);
  background: rgba(255, 255, 255, 0.02);
  color: #fff;
  font-size: 12px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.range-btn:hover {
  background: rgba(255, 255, 255, 0.04);
}
.range-btn.active {
  background: rgba(64, 159, 255, 0.15);
  border-color: #409fff;
  color: #409fff;
}
</style>
