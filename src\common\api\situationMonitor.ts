import request from '@/utils/request'

// 请求体接口（根据实际字段调整）
export interface DeviceOverviewPayload {
  // ...根据接口实际需要添加字段
  [key: string]: any
}

// 响应数据接口（根据实际返回结构调整）
export interface DeviceOverviewResponse {
  code?: number
  message?: string
  data?: any
}
//态势监管-企业概况
export function qygk(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/tsjg/qygk/524DEA49A7A1B7A2B90A4B178B924FBA', payload, {
    params: { applicationName: 'fxrq' },
  })
}

//态势监管-从业人员与持证情况
export function cyryczqk(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/tsjg/cyyyyczqk/E17A99F687CB68ACA360341072DF51EF', payload, {
    params: { applicationName: 'fxrq' },
  })
}

//态势监管-设备报警
export function sbbj(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/tsjg/sbbj/9431FB7301DCDBC3B4498DCF50DB7B2E', payload, {
    params: { applicationName: 'fxrq' },
  })
}

//态势监管-设备报警列表
export function sbbjlb(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/tsjg/bjlb/492EBCE929E5689E4267DA18626723C1', payload, {
    params: { applicationName: 'fxrq' },
  })
}

//态势监管-用户概况
export function yhgk(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/tsjg/yhgk/3CB63A2A7FBD7398B90A4B178B924FBA', payload, {
    params: { applicationName: 'fxrq' },
  })
}

//态势监管-供需分析
export function gxfx(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/tsjg/gxfx/CDDE867B493E1BAB645FA1961DC5C423', payload, {
    params: { applicationName: 'fxrq' },
  })
}

//态势监管-企业列表
export function qylb(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/tsjg/enterprise_list/C7F6AD89C1BD3177BB6AAFCDC6B3FABC15E4E9DE32CF40B6',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

//态势监管-企业用户技防设施接入统计
export function qyyhjfssjrtj(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/tsjg/qyyhjfssjrtj/3FAEE0C554E69C827164993ECBC3D86DB4498DCF50DB7B2E',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
