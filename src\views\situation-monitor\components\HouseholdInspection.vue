<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">设备报警</div>
      <div class="header-dropdown"></div>
    </div>
    <div class="p-4 panel-content">
      <div class="equipment-alarm-container">
        <!-- 左侧统计卡片区域 -->
        <div class="stats-cards">
          <div
            v-for="cat in categories"
            :key="cat.key"
            class="stat-card"
            :class="{ active: activeCategory === cat.key }"
            @click="handleCategoryClick(cat)"
            style="cursor:pointer;"
          >
            <div class="card-title">{{ cat.label }}</div>
            <div class="card-icon">
              <img src="/src/assets/industry-icons/point.svg" alt="point" class="point-icon" />
            </div>
            <div class="card-value">
              <!-- 动态显示对应统计数据 -->
              {{
                cat.key === ''
                  ? alarmStats.sbbjzl
                  : cat.key === 'gdsbbj'
                  ? alarmStats.gdsbbj
                  : cat.key === 'zdfkbj'
                  ? alarmStats.zdfkbj
                  : cat.key === 'yhsbbj'
                  ? alarmStats.yhsbbj
                  : 0
              }}
            </div>
          </div>
        </div>

        <!-- 右侧表格区域 -->
        <div class="alarm-table">
          <div class="table-header">
            <div class="header-cell">报警时间</div>
            <div class="header-cell">报警设备</div>
            <div class="header-cell">设备位置</div>
            <div class="header-cell">处置状态</div>
          </div>
          <div class="table-body">
            <ul class="alarm-list">
              <template v-if="alarmList.length > 0">
                <li
                  v-for="(item, index) in alarmList"
                  :key="index"
                  class="alarm-item"
                  :class="{ highlighted: item.highlighted }"
                >
                  <span class="alarm-cell">{{ item.alarmTime }}</span>
                  <span class="alarm-cell">{{ item.alarmDevice1 }}</span>
                  <span class="alarm-cell">{{ item.alarmDevice2 }}</span>
                  <span class="alarm-cell status-cell" :class="getStatusClass(item.status)">{{ item.status }}</span>
                </li>
              </template>
              <template v-else>
                <li class="alarm-item no-data">
                  <span class="alarm-cell no-data-cell">
                    暂无数据
                  </span>
                </li>
              </template>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { sbbj, sbbjlb } from '@/common/api/situationMonitor'

// 定义报警数据接口
interface AlarmItem {
  alarmTime: string
  alarmDevice1: string
  alarmDevice2: string
  status: string
  highlighted?: boolean
}

// 报警列表数据
const alarmList = ref<AlarmItem[]>([])

// 新增：设备报警统计数据
const alarmStats = ref({
  sbbjzl: 0,
  gdsbbj: 0,
  zdfkbj: 0,
  yhsbbj: 0,
})

// 新增：报警分类及映射
const categories = [
  { key: '', label: '设备报警总量', bjfl: '' },
  { key: 'gdsbbj', label: '管道设备报警', bjfl: 'gdsbbj' },
  { key: 'zdfkbj', label: '重点防控报警', bjfl: 'zdfkbj' },
  { key: 'yhsbbj', label: '用户设备报警', bjfl: 'yhsbbj' },
]
const activeCategory = ref(null) // 默认不选中任何卡片

// 获取状态样式类
const getStatusClass = (status: string) => {
  switch (status) {
    case '待处置':
      return 'status-pending'
    case '处理中':
      return 'status-processing'
    case '已处置':
      return 'status-completed'
    default:
      return ''
  }
}

// 新增：点击分类卡片时切换并请求报警列表
const handleCategoryClick = async (cat: any) => {
  if (activeCategory.value === cat.key) {
    activeCategory.value = null
    try {
      const res = await sbbjlb({ bjfl: '' })
      console.log('报警列表接口返回:', res)
      // 映射接口字段到 alarmList
      alarmList.value = Array.isArray(res?.data)
        ? res.data.map((item: any) => ({
            alarmTime: item.bjsj,
            alarmDevice1: item.bjsb,
            alarmDevice2: item.bjlx,
            status: item.czzt,
          }))
        : []
    } catch (err) {
      console.error('报警列表接口请求失败:', err)
    }
    return
  }
  activeCategory.value = cat.key
  try {
    const res = await sbbjlb({ bjfl: cat.bjfl })
    console.log('报警列表接口返回:', res)
    alarmList.value = Array.isArray(res?.data)
      ? res.data.map((item: any) => ({
          alarmTime: item.bjsj,
          alarmDevice1: item.bjsb,
          alarmDevice2: item.bjlx,
          status: item.czzt,
        }))
      : []
  } catch (err) {
    console.error('报警列表接口请求失败:', err)
  }
}

onMounted(async () => {
  try {
    const sbbjRes = await sbbj({})
    const stats = Array.isArray(sbbjRes?.data) ? sbbjRes.data[0] : null
    if (stats) {
      alarmStats.value.sbbjzl = Number(stats.sbbjzl ?? 0)
      alarmStats.value.gdsbbj = Number(stats.gdsbbj ?? 0)
      alarmStats.value.zdfkbj = Number(stats.zdfkbj ?? 0)
      alarmStats.value.yhsbbj = Number(stats.yhsbbj ?? 0)
    }

    // 默认加载全部报警列表
    const bjlbRes = await sbbjlb({ bjfl: '' })
    console.log('设备报警列表接口返回:', bjlbRes)
    alarmList.value = Array.isArray(bjlbRes?.data)
      ? bjlbRes.data.map((item: any) => ({
          alarmTime: item.bjsj,
          alarmDevice1: item.bjsb,
          alarmDevice2: item.bjlx,
          status: item.czzt,
        }))
      : []
  } catch (err) {
    console.error('调用设备报警相关接口失败:', err)
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.equipment-alarm-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 16px;
}

.stats-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 8px;
  width: 248px;
}

.stat-card {
  position: relative;
  width: 120px;
  height: 108px;
  background: linear-gradient(0deg, rgba(64, 159, 255, 0.15) 0%, rgba(64, 159, 255, 0) 100%);
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(64, 159, 255, 0) 0%, rgba(64, 159, 255, 0.15) 100%) 1;

  .card-title {
    position: absolute;
    bottom: 12px;
    left: 18px;
    width: 84px;
    height: 20px;
    white-space: nowrap;
    color: #99d5ff;
    font-family: 'Noto Sans SC';
    font-size: 14px;
    line-height: 20px;
    text-align: center;
  }

  .card-icon {
    position: absolute;
    top: 32px;
    left: 28px;
    width: 64px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    .point-icon {
      width: 60px;
      height: 55px;
    }
  }

  .card-value {
    position: absolute;
    top: 12px;
    left: 40px;
    width: 40px;
    height: 32px;
    white-space: nowrap;
    color: #ffc61a;
    font-family: 'DINPro';
    font-size: 24px;
    line-height: 32px;
    text-align: center;
  }

  &.active {
    border: 2px solid #409fff;
    box-shadow: 0 0 12px #409fff55;
  }
}

.alarm-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .table-header {
    display: flex;
    height: 48px;
    background: linear-gradient(270deg, rgba(64, 159, 255, 0.3) 0%, rgba(64, 159, 255, 0) 100%);
    flex-shrink: 0;

    .header-cell {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 16px;
      color: #ffffff;
      font-family: 'Noto Sans SC';
      font-size: 14px;
      line-height: 20px;
    }
  }

  .table-body {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(64, 159, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(64, 159, 255, 0.5);
      border-radius: 3px;

      &:hover {
        background: rgba(64, 159, 255, 0.7);
      }
    }

    /* Firefox 滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: rgba(64, 159, 255, 0.5) rgba(64, 159, 255, 0.1);
  }

  .alarm-list {
    list-style: none;
    margin: 0;
    padding: 0;

    .alarm-item {
      display: flex;
      height: 48px;
      border-bottom: 1px solid rgba(64, 159, 255, 0.3);

      &.highlighted {
        background: rgba(64, 159, 255, 0.15);
      }

      &.no-data {
        height: 48px;
        background: none;
        border-bottom: none;

        .alarm-cell.no-data-cell {
          flex: 1;
          text-align: center;
          justify-content: center;
          color: #99d5ff;
          font-size: 14px;
          display: flex;
          align-items: center;
        }
      }

      &:last-child {
        border-bottom: none;
      }

      .alarm-cell {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 16px;
        color: rgba(255, 255, 255, 0.8);
        font-family: 'Noto Sans SC';
        font-size: 14px;
        line-height: 20px;

        &.status-cell {
          font-weight: 500;

          &.status-pending {
            color: #ff6b6b;
          }

          &.status-processing {
            color: #ffc61a;
          }

          &.status-completed {
            color: #51cf66;
          }
        }
      }
    }
  }
}
</style>
