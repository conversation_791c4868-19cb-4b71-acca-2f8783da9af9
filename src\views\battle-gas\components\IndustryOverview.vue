<template>
  <div class="panel-container">
    <div class="panel-header">
      <div class="header-title">资源概况</div>
    </div>
    <div class="panel-content">
      <div class="user-overview-container">
        <div v-for="(item, index) in userStats" :key="index" class="user-card">
          <div class="card-content">
            <div class="card-icon">
              <img :src="item.icon" alt="" />
              <div v-if="item.secondIcon" class="second-icon">
                <img :src="item.secondIcon" alt="" />
              </div>
            </div>
            <div class="card-info">
              <div class="card-title">{{ item.title }}</div>
              <div class="card-value-container">
                <div class="card-value">{{ item.value }}</div>
                <div class="card-unit">{{ item.unit }}</div>
              </div>
            </div>
          </div>
          <img :src="iconData.union" alt="" class="card-border-bottom" />
          <div class="card-side-lines">
            <img :src="iconData.vectorLeft" alt="" class="side-line left" />
            <img :src="iconData.vectorRight" alt="" class="side-line right" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { pzsyq_zygk } from '@/common/api/bottledGas'

// 图标数据配置 - 使用图片路径
const iconData = {
  userTotal: '/src/assets/industry-icons/total-user.svg',
  totalbBattle: '/src/assets/battle-gas/total-battle.svg',
  bottle: '/src/assets/industry-icons/battle-gas.svg',
  webPoint: '/src/assets/battle-gas//web-point.svg',
  iconDown: '/src/assets/industry-icons/icon-down.svg',
  vectorLeft: '/src/assets/industry-icons/Vector-Left.svg',
  vectorRight: '/src/assets/industry-icons/Vector-right.svg',
  union: '/src/assets/industry-icons/Union.svg',
}

// 用户概况数据
interface UserStat {
  type: string
  title: string
  value: any
  unit: string
  icon: string
  secondIcon?: string
}
const userStats = ref<UserStat[]>([])

onMounted(async () => {
  try {
    const res = await pzsyq_zygk()
    if (Array.isArray(res.data) && res.data.length > 0) {
      const d = res.data[0]
      userStats.value = [
        {
          type: 'total',
          title: '总气瓶数量',
          value: d['总气瓶数量'] ?? '--',
          unit: '个',
          icon: iconData.iconDown,
          secondIcon: iconData.totalbBattle,
        },
        {
          type: 'pipeline',
          title: '在用气瓶数量',
          value: d['在用气瓶数量'] ?? '--',
          unit: '个',
          icon: iconData.iconDown,
          secondIcon: iconData.bottle,
        },
        {
          type: 'bottle',
          title: '瓶装气用户数',
          value: d['瓶装气用户数'] ?? '--',
          unit: '个',
          icon: iconData.iconDown,
          secondIcon: iconData.userTotal,
        },
        {
          type: 'business',
          title: '服务网点总数',
          value: d['配送站点数量'] ?? '--',
          unit: '个',
          icon: iconData.iconDown,
          secondIcon: iconData.webPoint,
        },
      ]
    }
    console.log('pzsyq_zygk response:', res)
  } catch (err) {
    console.error('pzsyq_zygk error:', err)
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.panel-content {
  padding: 16px;
}

.user-overview-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 20px;
  width: 100%;
  height: 100%;
}

.user-card {
  position: relative;
  width: 348px;
  height: 104px;
  overflow: hidden;

  &.pipeline-user {
    .card-icon {
      display: flex;
      flex-direction: column;
      gap: 0;
      width: 56px;
      height: 48px;
      position: relative;

      > div:first-child .group-1000008936 {
        position: absolute;
        top: 20px;
        left: 0px;
        width: 56px;
        height: 28px;
      }

      .second-icon {
        position: absolute;
        top: 20px;
        left: 14px;
        width: 28px;
        height: 28px;
        overflow: hidden;

        .union-5 {
          position: absolute;
          top: 2px;
          left: 2px;
          width: 23.33px;
          height: 23.33px;
        }
      }
    }

    .card-info {
      .card-title {
        width: 84px;
      }

      .card-value-container {
        .card-unit {
          width: 14px;
        }
      }
    }
  }

  .card-content {
    position: absolute;
    top: 27px;
    left: 58px;
    width: 170px;
    height: 50px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
    gap: 12px;

    .card-icon {
      flex-shrink: 0;
      width: 56px;
      height: 38px;
      display: flex;
      flex-direction: column;
      gap: 0;
      position: relative;

      > img {
        position: relative;
        top: 10px;
      }

      .second-icon {
        position: absolute;
        top: 0px;
        left: 16px;
        width: 28px;
        height: 28px;
        overflow: hidden;
      }
    }

    .card-info {
      flex-shrink: 0;
      width: 102px;
      height: 50px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: start;

      .card-title {
        flex-shrink: 0;
        width: 102px;
        height: 22px;
        white-space: nowrap;
        color: #66ffff;
        font-family: 'Noto Sans SC';
        font-size: 14px;
        line-height: 22px;
        text-align: left;
      }

      .card-value-container {
        flex-shrink: 0;
        width: 102px;
        height: 28px;
        display: flex;
        flex-direction: row;
        justify-content: start;
        align-items: center;

        .card-value {
          flex-shrink: 0;
          width: 88px;
          height: 28px;
          white-space: nowrap;
          color: #ffffff;
          font-family: 'DINPro';
          font-size: 20px;
          line-height: 28px;
          display: flex;
          align-items: center;
        }

        .card-unit {
          flex-shrink: 0;
          width: 14px;
          height: 28px;
          display: flex;
          flex-direction: row;
          justify-content: start;
          align-items: center;
          gap: 5.64px;
          padding: 6px 0px 0px 0px;

          white-space: nowrap;
          color: #ffffff;
          font-family: 'Noto Sans SC';
          font-size: 14px;
          line-height: 22px;
          text-align: center;
        }
      }
    }
  }

  .card-border-bottom {
    position: absolute;
    left: 30px;
    bottom: 0px;
    width: 288px;
    height: 12px;
  }

  .card-side-lines {
    .side-line {
      position: absolute;
      top: 0px;
      width: 5.5px;
      height: 115px;

      &.left {
        left: 0px;
      }

      &.right {
        right: 0px;
      }
    }
  }
}
</style>
