<!-- 智慧燃气页面 -->
<template>
  <div class="dashboard-layout">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <IndustryOverview />
      <HouseholdInspection />
      <RiskHazardMonitoring />
    </div>

    <!-- 中央地图区域 -->
    <div class="center-panel">
      <!-- 地图控制组件 -->
      <LayersTool v-model="layersData" @layer-change="handleLayerChange" />
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <PipelineOverview />
      <!-- <OldPipeNetworkRenovation /> -->
      <RoundInspection />
      <div class="flex gap-6">
        <BottledGasStatus />
        <CylinderFillingMonitoring />
      </div>
    </div>

    <!-- 弹窗 -->
    <IndustryInfoDialog :open="industryInfoOpen" @close="industryInfoOpen = false" />
    <StationDialog :open="stationInfoOpen" @close="stationInfoOpen = false" />
    <PipelineDialog :open="pipelineInfoOpen" @close="pipelineInfoOpen = false" />
    <AreaDialog :open="areaInfoOpen" @close="areaInfoOpen = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject, watch } from 'vue'
import IndustryOverview from './components/IndustryOverview.vue'
import HouseholdInspection from './components/HouseholdInspection.vue'
// import SupervisionInspection from './components/SupervisionInspection.vue'
import RiskHazardMonitoring from './components/RiskHazardMonitoring.vue'
// import OldPipeNetworkRenovation from './components/OldPipeNetworkRenovation.vue'
import BottledGasStatus from './components/BottledGasStatus.vue'
import CylinderFillingMonitoring from './components/CylinderFillingMonitoring.vue'
import PipelineOverview from './components/PipelineOverview.vue'
import RoundInspection from './components/RoundInspection.vue'

import LayersTool from '@/components/LayersTool.vue'
import IndustryInfoDialog from '@/components/ue-dialog/IndustryInfoDialog.vue'
import StationDialog from '@/components/ue-dialog/StationDialog.vue'
import PipelineDialog from '@/components/ue-dialog/PipelineDialog.vue'
import AreaDialog from '@/components/ue-dialog/AreaDialog.vue'
import { layerData } from './layerData'
import { useResettableLayers } from '@/hooks/useResettableLayers'
import type { LayerChangeEvent } from '@/types/mapLayers'
import type { GasCompanyItem, StationItem, PipelineItem } from '@/types/mapTypes'
import {
  getGasCompanyLocations,
  getStationLocations,
  getPipelineLocations,
  getRiskAreaLocations,
  getGasCompanyDetail,
  getStationDetail,
  getValveWellDetail,
  getPipelineDetail,
  getRiskAreaDetail,
  type GasCompanyLocationData,
  type StationLocationData,
  type PipelineLocationData,
  type RiskAreaLocationData,
} from '@/common/api/dashboard'

// 导入图片资源
import gasIcon from '@/assets/map/gas-icon.png'
import stationIcon from '@/assets/map/station-icon.png'

// 弹窗状态
const industryInfoOpen = ref(false)
const stationInfoOpen = ref(false)
const pipelineInfoOpen = ref(false)
const areaInfoOpen = ref(false)

// 图层数据
const { layersData, resetLayersState } = useResettableLayers(layerData)

// 注入地图引用和准备状态
const mapRef: any = inject('mapRef')
const mapReady: any = inject('mapReady')

// 处理燃气企业图层
const handleGasCompanyLayer = async (checked: boolean, itemId?: string) => {
  if (!mapRef.value) return

  if (checked) {
    try {
      const response = await getGasCompanyLocations()

      if (response && response.data) {
        const locations = (Array.isArray(response.data) ? response.data : [response.data]) as GasCompanyLocationData[]
        const markersConfig = locations.map((item: GasCompanyLocationData) => ({
          id: item.id,
          position: { lng: item.longitude, lat: item.latitude },
          iconUrl: gasIcon,
          extData: { ...item },
          content: `
            <div style="position:relative;width:24px;height:24px;border: 1px solid #309CE8;background:rgba(20, 51, 82, 0.60) url(\'${gasIcon}\') center no-repeat; background-size:16px 16px;">
            </div>
          `,
          onClick: async (markerData: any) => {
            try {
              const detailResponse = await getGasCompanyDetail({ id: markerData.id })
              if (detailResponse && detailResponse.data) {
                const detail = Array.isArray(detailResponse.data) ? detailResponse.data[0] : detailResponse.data
                const content = `
                  <div style="padding: 8px 12px; white-space: nowrap;border-radius: 4px;border: 1px solid #309CE8;background: rgba(20, 51, 82, 0.60);backdrop-filter: blur(2px);z-index:1000;">
                    <div style="color: #47EBEB;font-size: 16px;font-weight: 500;">${detail.name || '燃气企业'}</div>
                    <div style="color: #fff;font-size: 14px;">类型：${detail.type || '未知'}</div>
                    <div style="color: #fff;font-size: 14px;">地址：${detail.detailed_pos || '未知'}</div>
                    <div style="color: #fff;font-size: 14px;">负责人：${detail.representative || '未知'}</div>
                    <div style="color: #fff;font-size: 14px;">联系电话：${detail.contact || '未知'}</div>
                  </div>
                `
                mapRef.value.showInfoWindow(content, { lng: item.longitude, lat: item.latitude })
              }
            } catch (error) {
              console.error('获取燃气企业详情失败:', error)
            }
          },
          getInfoWindowContent: (infoObj: any) => `
              <div style="padding: 8px 12px; white-space: nowrap;border-radius: 4px;border: 1px solid #309CE8;background: rgba(20, 51, 82, 0.60);backdrop-filter: blur(2px);z-index:1000;">
                <div style="color: #47EBEB;font-size: 16px;font-weight: 500;">燃气企业</div>
                <div style="color: #fff;font-size: 14px;">类型：${infoObj.type || '未知'}</div>
                <div style="color: #fff;font-size: 14px;">点击查看详情</div>
              </div>
            `,
        }))
        mapRef.value.addGenericMarkers('gasCompanyLayer', markersConfig)
      }
    } catch (error) {
      console.error('加载燃气企业数据失败:', error)
    }
  } else {
    mapRef.value.clearLayer('gasCompanyLayer')
  }
}

// 处理场站图层
const handleStationLayer = async (checked: boolean, itemId?: string) => {
  if (!mapRef.value) return

  if (checked) {
    try {
      const response = await getStationLocations()

      if (response && response.data) {
        const locations = (Array.isArray(response.data) ? response.data : [response.data]) as StationLocationData[]
        const markersConfig = locations.map((item: StationLocationData) => ({
          id: item.id,
          position: { lng: item.longitude, lat: item.latitude },
          iconUrl: stationIcon,
          extData: { ...item },
          content: `
            <div style="position:relative;width:24px;height:24px;border: 1px solid #309CE8;background:rgba(20, 51, 82, 0.60) url(\'${stationIcon}\') center no-repeat; background-size:16px 16px;">
            </div>
          `,
          onClick: async (markerData: any) => {
            try {
              // 根据场站类型选择不同的详情API
              let detailResponse
              if (markerData.type === '阀门井') {
                detailResponse = await getValveWellDetail({ id: markerData.id })
              } else {
                detailResponse = await getStationDetail({ id: markerData.id })
              }

              if (detailResponse && detailResponse.data) {
                const detail = Array.isArray(detailResponse.data) ? detailResponse.data[0] : detailResponse.data
                const address = (detail as any).detailed_pos || (detail as any).address || '未知'
                const content = `
                  <div style="padding: 8px 12px; white-space: nowrap;border-radius: 4px;border: 1px solid #309CE8;background: rgba(20, 51, 82, 0.60);backdrop-filter: blur(2px);z-index:1000;">
                    <div style="color: #47EBEB;font-size: 16px;font-weight: 500;">${detail.name || '场站'}</div>
                    <div style="color: #fff;font-size: 14px;">类型：${detail.type || '未知'}</div>
                    <div style="color: #fff;font-size: 14px;">地址：${address}</div>
                    <div style="color: #fff;font-size: 14px;">负责人：${detail.representative || '未知'}</div>
                    <div style="color: #fff;font-size: 14px;">联系电话：${detail.contact || '未知'}</div>
                  </div>
                `
                mapRef.value.showInfoWindow(content, { lng: markerData.longitude, lat: markerData.latitude })
              }
            } catch (error) {
              console.error('获取场站详情失败:', error)
            }
          },
          getInfoWindowContent: (infoObj: any) => `
              <div style="padding: 8px 12px; white-space: nowrap;border-radius: 4px;border: 1px solid #309CE8;background: rgba(20, 51, 82, 0.60);backdrop-filter: blur(2px);z-index:1000;">
                <div style="color: #47EBEB;font-size: 16px;font-weight: 500;">场站设施</div>
                <div style="color: #fff;font-size: 14px;">类型：${infoObj.type || '未知'}</div>
                <div style="color: #fff;font-size: 14px;">点击查看详情</div>
              </div>
            `,
        }))
        mapRef.value.addGenericMarkers('stationLayer', markersConfig)
      }
    } catch (error) {
      console.error('加载场站数据失败:', error)
    }
  } else {
    mapRef.value.clearLayer('stationLayer')
  }
}

// 处理管线图层
const handlePipelineLayer = async (itemId: string | undefined, checked: boolean) => {
  if (!mapRef.value) return

  if (checked) {
    try {
      const response = await getPipelineLocations()

      if (response && response.data) {
        const locations = (Array.isArray(response.data) ? response.data : [response.data]) as PipelineLocationData[]

        // 根据企业名称分组（因为API返回的数据中没有type字段，使用enterprise字段）
        const pipelinesByType = locations.reduce(
          (acc, item) => {
            // 根据企业名称确定管网类型
            let type = 'unknown'
            const enterprise = (item as any).enterprise
            if (enterprise && enterprise.includes('中燃')) {
              type = 'zr-pipeline'
            } else if (enterprise && enterprise.includes('赵都')) {
              type = 'zd-pipeline'
            } else if (enterprise && enterprise.includes('国奥')) {
              type = 'ga-pipeline'
            }

            if (!acc[type]) {
              acc[type] = []
            }
            acc[type].push(item)
            return acc
          },
          {} as Record<string, PipelineLocationData[]>,
        )

        // 处理每种类型的管网
        Object.entries(pipelinesByType).forEach(([type, pipelines]) => {
          if (!itemId || itemId === type) {
            const pipelineConfig = pipelines
              .filter(item => item.point_location)
              .map(item => {
                // 解析经纬度字符串，处理可能的格式问题
                const coordinates = item
                  .point_location!.split(/[，,]/)
                  .map(coord => parseFloat(coord.trim()))
                  .filter(coord => !isNaN(coord))
                if (coordinates.length < 2) {
                  console.warn('管网坐标数据格式错误:', item)
                  return null
                }
                return {
                  id: item.id,
                  path: [{ lng: coordinates[0], lat: coordinates[1] }], // 单点路径，实际应该是线段
                  strokeColor: getStrokeColorByType(type),
                  extData: { ...item },
                  onClick: async (lineData: any) => {
                    try {
                      const detailResponse = await getPipelineDetail({ id: lineData.id })
                      if (detailResponse && detailResponse.data) {
                        const detail = Array.isArray(detailResponse.data) ? detailResponse.data[0] : detailResponse.data
                        const content = `
                        <div style="padding: 8px 12px; white-space: nowrap;border-radius: 4px;border: 1px solid #309CE8;background: rgba(20, 51, 82, 0.60);backdrop-filter: blur(2px);z-index:1000;">
                          <div style="color: #47EBEB;font-size: 16px;font-weight: 500;">${detail.section_name || '管网'}</div>
                          <div style="color: #fff;font-size: 14px;">企业：${detail.enterprise || '未知'}</div>
                          <div style="color: #fff;font-size: 14px;">压力类型：${detail.pressure_type || '未知'}</div>
                          <div style="color: #fff;font-size: 14px;">管道产品：${detail.pipe_product || '未知'}</div>
                          <div style="color: #fff;font-size: 14px;">埋深：${detail.earth_depth || '未知'}</div>
                          <div style="color: #fff;font-size: 14px;">建设日期：${detail.build_date || '未知'}</div>
                          <div style="color: #fff;font-size: 14px;">负责人：${detail.representative || '未知'}</div>
                          <div style="color: #fff;font-size: 14px;">联系电话：${detail.contact || '未知'}</div>
                        </div>
                      `
                        mapRef.value.showInfoWindow(content, { lng: coordinates[0], lat: coordinates[1] })
                      }
                    } catch (error) {
                      console.error('获取管网详情失败:', error)
                    }
                  },
                  getInfoWindowContent: (infoObj: any) => `
                  <div style="padding: 8px 12px; white-space: nowrap;border-radius: 4px;border: 1px solid #309CE8;background: rgba(20, 51, 82, 0.60);backdrop-filter: blur(2px);z-index:1000;">
                    <div style="color: #47EBEB;font-size: 16px;font-weight: 500;">管网</div>
                    <div style="color: #fff;font-size: 14px;">类型：${infoObj.type || '未知'}</div>
                    <div style="color: #fff;font-size: 14px;">点击查看详情</div>
                  </div>
                `,
                }
              })
              .filter(config => config !== null)

            if (pipelineConfig.length > 0) {
              mapRef.value.addGenericPolylines(`${type}PipelineLayer`, pipelineConfig)
            }
          }
        })
      }
    } catch (error) {
      console.error('加载管网数据失败:', error)
    }
  } else {
    // 清除所有管网图层
    mapRef.value.clearLayer('ZRPipelineLayer')
    mapRef.value.clearLayer('ZDPipelineLayer')
    mapRef.value.clearLayer('GAPipelineLayer')
  }
}

// 根据管网类型获取颜色
const getStrokeColorByType = (type: string): string => {
  switch (type) {
    case 'zr-pipeline':
    case '中燃管网':
      return '#FFFF00'
    case 'zd-pipeline':
    case '赵都管网':
      return '#00FF00'
    case 'ga-pipeline':
    case '国奥管网':
      return '#FF0000'
    default:
      return '#FFFFFF'
  }
}

// 处理风险区域图层
const handleRiskAreaLayer = async (checked: boolean, itemId?: string) => {
  if (!mapRef.value) return

  if (checked) {
    try {
      const response = await getRiskAreaLocations()

      if (response && response.data) {
        const locations = (Array.isArray(response.data) ? response.data : [response.data]) as RiskAreaLocationData[]

        // 根据风险等级分组
        const risksByLevel = locations.reduce(
          (acc, item) => {
            if (!acc[item.level]) {
              acc[item.level] = []
            }
            acc[item.level].push(item)
            return acc
          },
          {} as Record<string, RiskAreaLocationData[]>,
        )

        // 处理每种风险等级的区域
        Object.entries(risksByLevel).forEach(([level, risks]) => {
          if (!itemId || itemId === `risk-level-${level}`) {
            const markersConfig = risks.map(item => ({
              id: item.id,
              position: { lng: item.lng, lat: item.lat },
              iconUrl: getRiskIconByLevel(level),
              extData: { ...item },
              content: `
                <div style="position:relative;width:24px;height:24px;border: 1px solid ${getRiskColorByLevel(level)};background:rgba(20, 51, 82, 0.60) url('${getRiskIconByLevel(level)}') center no-repeat; background-size:16px 16px;">
                </div>
              `,
              onClick: async (markerData: any) => {
                try {
                  const detailResponse = await getRiskAreaDetail({ id: markerData.id })
                  if (detailResponse && detailResponse.data) {
                    const detail = Array.isArray(detailResponse.data) ? detailResponse.data[0] : detailResponse.data
                    const content = `
                      <div style="padding: 8px 12px; white-space: nowrap;border-radius: 4px;border: 1px solid #309CE8;background: rgba(20, 51, 82, 0.60);backdrop-filter: blur(2px);z-index:1000;">
                        <div style="color: #47EBEB;font-size: 16px;font-weight: 500;">${detail.area_name || '风险区域'}</div>
                        <div style="color: #fff;font-size: 14px;">风险等级：${detail.risk_level || '未知'}</div>
                        <div style="color: #fff;font-size: 14px;">地址：${detail.address || '未知'}</div>
                        <div style="color: #fff;font-size: 14px;">监管负责人：${detail.supervisor || '未知'}</div>
                        <div style="color: #fff;font-size: 14px;">联系电话：${detail.supervisor_phone || '未知'}</div>
                        <div style="color: #fff;font-size: 14px;">监控：${detail.videos || '未知'}</div>
                      </div>
                    `
                    mapRef.value.showInfoWindow(content, { lng: markerData.lng, lat: markerData.lat })
                  }
                } catch (error) {
                  console.error('获取风险区域详情失败:', error)
                }
              },
              getInfoWindowContent: (infoObj: any) => `
                  <div style="padding: 8px 12px; white-space: nowrap;border-radius: 4px;border: 1px solid #309CE8;background: rgba(20, 51, 82, 0.60);backdrop-filter: blur(2px);z-index:1000;">
                    <div style="color: #47EBEB;font-size: 16px;font-weight: 500;">风险区域</div>
                    <div style="color: #fff;font-size: 14px;">等级：${infoObj.level || '未知'}</div>
                    <div style="color: #fff;font-size: 14px;">点击查看详情</div>
                  </div>
                `,
            }))
            mapRef.value.addGenericMarkers(`riskLevel${level}Layer`, markersConfig)
          }
        })
      }
    } catch (error) {
      console.error('加载风险区域数据失败:', error)
    }
  } else {
    // 清除所有风险区域图层
    mapRef.value.clearLayer('riskLevel1Layer')
    mapRef.value.clearLayer('riskLevel2Layer')
    mapRef.value.clearLayer('riskLevel3Layer')
    mapRef.value.clearLayer('riskLevel4Layer')
  }
}

// 根据风险等级获取图标
const getRiskIconByLevel = (level: string): string => {
  // 这里应该根据实际的图标资源返回对应的图标URL
  return '/src/assets/map/warning-icon.png' // 默认使用警告图标
}

// 根据风险等级获取颜色
const getRiskColorByLevel = (level: string): string => {
  switch (level) {
    case '1':
      return '#FF0000' // 红色 - 一级风险
    case '2':
      return '#FF8000' // 橙色 - 二级风险
    case '3':
      return '#FFFF00' // 黄色 - 三级风险
    case '4':
      return '#00FF00' // 绿色 - 四级风险
    default:
      return '#FFFFFF'
  }
}

// 处理图层变化
const handleLayerChange = async (event: LayerChangeEvent) => {
  const { groupId, itemId, checked } = event

  if (!mapRef.value) {
    console.warn('Map reference not available.')
    return
  }

  // 检查地图是否准备就绪
  if (!mapReady.value) {
    console.warn('Map is not ready yet. Please wait for map initialization.')
    return
  }

  // 获取 AMap 实例，用于创建 Pixel 等
  const AMapInstance = mapRef.value.getAMapInstance()
  if (!AMapInstance) {
    console.error('AMap instance not available.')
    return
  }

  switch (groupId) {
    case 'gasCompany':
      handleGasCompanyLayer(checked, itemId)
      break
    case 'station':
      handleStationLayer(checked, itemId)
      break
    case 'pipeline':
      handlePipelineLayer(itemId, checked)
      break
    case 'riskArea':
      handleRiskAreaLayer(checked, itemId)
      break
    // ... 其他图层类型
  }
}

// 初始化图层的函数
const initializeLayers = () => {
  console.log('初始化图层，当前图层数据:', layersData.value)
  // 遍历 layersData，对所有 checked 为 true 的图层触发 handleLayerChange
  Object.entries(layersData.value).forEach(([groupId, group]) => {
    console.log(`检查图层组 ${groupId}:`, group)
    if (group.checked === true) {
      console.log(`初始化图层组 ${groupId}`)
      handleLayerChange({ groupId, checked: true })
    } else if (group.checked === 'indeterminate' && group.children) {
      group.children.forEach(child => {
        if (child.checked) {
          console.log(`初始化子图层 ${groupId}.${child.id}`)
          handleLayerChange({ groupId, itemId: child.id, checked: true })
        }
      })
    }
  })
}

// 监听地图准备状态
watch(
  () => mapReady.value,
  isReady => {
    console.log('地图准备状态变化:', isReady)
    if (isReady) {
      console.log('地图准备完成，开始初始化图层')
      initializeLayers()
    }
  },
  { immediate: true },
)

onMounted(() => {
  console.log('Dashboard页面挂载，mapRef:', mapRef.value, 'mapReady:', mapReady.value)
  // 页面加载时重置图例状态
  resetLayersState()

  // 如果地图已经准备好，立即初始化图层
  if (mapReady.value) {
    console.log('地图已准备好，立即初始化图层')
    initializeLayers()
  }
})

onUnmounted(() => {
  // 清除所有图层
  if (mapRef.value) {
    mapRef.value.clearLayer('gasCompanyLayer')
    mapRef.value.clearLayer('stationLayer')
    mapRef.value.clearLayer('ZRPipelineLayer')
    mapRef.value.clearLayer('ZDPipelineLayer')
    mapRef.value.clearLayer('GAPipelineLayer')
    mapRef.value.clearLayer('riskLevel1Layer')
    mapRef.value.clearLayer('riskLevel2Layer')
    mapRef.value.clearLayer('riskLevel3Layer')
    mapRef.value.clearLayer('riskLevel4Layer')
  }

  // 页面卸载时重置图例状态
  resetLayersState()
})
</script>

<style scoped>
.dashboard-layout {
  position: absolute;
  top: 96px;
  left: 24px;
  right: 24px;
  height: calc(100% - 96px);
  overflow: hidden;
}

.left-panel,
.right-panel {
  position: absolute;
  top: 0;
  width: 744px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.left-panel {
  left: 0;
}

.right-panel {
  right: 0;
}

.center-panel {
  position: absolute;
  top: 0;
  left: 768px;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .dashboard-content {
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    padding: 20px;
    padding-top: 120px;
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 280px 1fr 280px;
    gap: 16px;
    padding: 16px;
    padding-top: 120px;
  }
}
</style>
