<template>
  <div class="panel-container">
    <div class="panel-header">隐患治理列表</div>
    <div class="p-4 panel-content">
      <!-- 搜索输入框带icon -->
      <div style="margin-bottom: 12px; position: relative">
        <span class="search-icon">
          <!-- 你可以替换为自己的svg或图片 -->
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <circle cx="7" cy="7" r="5.5" stroke="#fff" stroke-width="1.2" />
            <path d="M12 12L15 15" stroke="#fff" stroke-width="1.2" stroke-linecap="round" />
          </svg>
        </span>
        <input v-model="searchText" type="text" placeholder="燃气企业" class="search-input" />
      </div>
      <Table class="w-full table-fixed">
        <colgroup>
          <col style="width: 20%" />
          <col style="width: 20%" />
          <col style="width: 20%" />
          <col style="width: 25%" />
          <col style="width: 15%" />
        </colgroup>
        <TableHeader>
          <TableRow class="border-[#99D5FF]/30 hover:bg-transparent">
            <TableHead class="font-bold text-white">所属区域</TableHead>
            <TableHead class="font-bold text-white">隐患名称</TableHead>
            <TableHead class="font-bold text-white">上报日期</TableHead>
            <TableHead class="font-bold text-white">所属企业</TableHead>
            <TableHead class="font-bold text-white">操作</TableHead>
          </TableRow>
        </TableHeader>
      </Table>
      <div class="overflow-hidden" :style="{ height: `${3 * rowHeight}px` }" ref="tableContainerRef">
        <Table class="w-full table-fixed">
          <colgroup>
            <col style="width: 20%" />
            <col style="width: 20%" />
            <col style="width: 20%" />
            <col style="width: 25%" />
            <col style="width: 15%" />
          </colgroup>
          <TableBody
            :style="{
              transform: `translateY(-${scrollTop}px)`,
              transition: transitionEnabled ? 'transform 0.5s ease' : 'none',
            }"
          >
            <TableRow
              v-for="(item, index) in scrollList"
              :key="index"
              class="border-[#99D5FF]/30 hover:bg-[#99D5FF]/30"
              :style="{ height: `${rowHeight}px` }"
            >
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.region }}</TableCell>
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.riskName }}</TableCell>
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.reportDate }}</TableCell>
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.company }}</TableCell>
              <TableCell class="text-[#99D5FF] cursor-pointer" @click="handleShowDialog(item)">查看详情</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Table,
  TableBody,
  // TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { scyf_yhzllb } from '@/common/api/doublePrevention'

const emit = defineEmits<{
  (e: 'click', value: any): void
}>()

const handleShowDialog = (record: any) => {
  emit('click', record)
}

// 新表格数据结构
interface HazardItem {
  id?: number
  region: string
  riskName: string
  reportDate: string
  company: string
}

const sourceData = ref<HazardItem[]>([])

const searchText = ref('')

// 获取隐患治理列表数据
const fetchHazardManagementList = async (enterprise?: string) => {
  try {
    const response = await scyf_yhzllb({ enterprise })
    if (response && response.data && response.data.length > 0) {
      // 转换接口数据为表格所需格式
      sourceData.value = response.data.map(item => ({
        id: item.id,
        region: item.area || '',
        riskName: item.hidden_trouble_name || '',
        reportDate: item.report_time || '',
        company: item.enterprise_name || '',
      }))
    }
  } catch (error) {
    console.error('获取隐患治理列表数据失败:', error)
  }
}

// 过滤后的数据
const filteredData = computed(() =>
  sourceData.value.filter(
    item =>
      item.region.includes(searchText.value) ||
      item.riskName.includes(searchText.value) ||
      item.reportDate.includes(searchText.value) ||
      item.company.includes(searchText.value),
  ),
)
const scrollList = computed(() => [...filteredData.value, ...filteredData.value])
const tableContainerRef = ref<HTMLElement | null>(null)
const scrollTimer = ref<any>(null)
const scrollTop = ref(0)
const rowHeight = 48
const transitionEnabled = ref(true)

const startScrolling = () => {
  stopScrolling()
  scrollTimer.value = setInterval(() => {
    scrollTop.value += rowHeight

    if (scrollTop.value >= sourceData.value.length * rowHeight) {
      transitionEnabled.value = false
      scrollTop.value = 0
      setTimeout(() => {
        transitionEnabled.value = true
      }, 50)
    }
  }, 3000)
}

const stopScrolling = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value)
    scrollTimer.value = null
  }
}

onMounted(() => {
  // 初始化加载数据
  fetchHazardManagementList('')
  startScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.addEventListener('mouseenter', stopScrolling)
    container.addEventListener('mouseleave', startScrolling)
  }
})

// 监听搜索框变化，重新获取数据（可选功能）
// 也可以只使用前端过滤，根据实际需求选择
// watch(searchText, (newVal) => {
//   if (newVal) {
//     fetchHazardManagementList(newVal)
//   } else {
//     fetchHazardManagementList()
//   }
// })

onUnmounted(() => {
  stopScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.removeEventListener('mouseenter', stopScrolling)
    container.removeEventListener('mouseleave', startScrolling)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';
.search-input {
  width: 100%;
  padding: 8px 12px 6px 34px;
  border: 1px solid #409fff;
  border-radius: 4px;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 14px;
  outline: none;
}
.search-input::placeholder {
  color: #fff;
  opacity: 0.7;
}
.search-icon {
  color: #fff;
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  pointer-events: none;
  z-index: 2;
}
</style>
