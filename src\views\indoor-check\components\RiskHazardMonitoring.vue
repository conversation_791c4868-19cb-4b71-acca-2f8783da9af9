<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">入户类型分布</div>
      <div class="header-dropdown"></div>
    </div>
    <div class="p-4 panel-content">
      <div class="equipment-alarm-container">
        <!-- 左侧统计卡片区域 -->
        <div class="user-overview-container">
          <div v-for="(item, index) in userStats" :key="index" class="user-card">
            <div class="card-content">
              <div class="card-icon">
                <img :src="item.icon" alt="" />
                <div v-if="item.secondIcon" class="second-icon">
                  <img :src="item.secondIcon" alt="" />
                </div>
              </div>
              <div class="card-info">
                <div class="card-title">{{ item.title }}</div>
                <div class="card-value-container">
                  <div class="card-value">{{ item.value }}</div>
                  <div class="card-unit">{{ item.unit }}</div>
                </div>
              </div>
            </div>

            <!-- 移除两侧竖线图片，改为背景图 -->
          </div>
        </div>
        <!-- 右侧图表区域 -->
        <div class="chart-panel">
          <div class="chart-content">
            <div ref="chartRef" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { rhlxfb } from '@/common/api/indoor'

interface UserStat {
  type: string
  title: string
  value: string
  icon: string
  secondIcon?: string // 添加可选的 secondIcon 属性
  unit?: string // 添加可选的 unit 属性
}

// 图标数据配置 - 使用图片路径
const iconData = {
  dwUser: '/src/assets/indoor-icon/dw-user.svg',
  czUser: '/src/assets/indoor-icon/cz-user.svg',
  ncUser: '/src/assets/indoor-icon/nc-user.svg',
  iconDown: '/src/assets/indoor-icon/user-bg.svg',
  union: '/src/assets/industry-icons/Union.svg',
}

const userStats = ref<UserStat[]>([])
const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

const fillUserStats = (data: any) => {
  const total = (data.ruralResidentUserNumber ?? 0) + (data.urbanResidentUserNumber ?? 0)
  userStats.value = [
    {
      type: 'total',
      title: '单位用户（个）',
      value: total,
      icon: iconData.dwUser,
      unit: '个',
    },
    {
      type: 'pipeline',
      title: '城镇居民（户）',
      value: data.urbanResidentUserNumber ?? 0,
      icon: iconData.czUser,
      unit: '户',
    },
    {
      type: 'pipeline',
      title: '农村居民（户）',
      value: data.ruralResidentUserNumber ?? 0,
      icon: iconData.ncUser,
      unit: '户',
    },
  ]
}

// 饼图option，只展示城镇/农村居民，中心显示单位用户和数量
const getPieOption = (data: any) => {
  const total = (data.ruralResidentUserNumber ?? 0) + (data.urbanResidentUserNumber ?? 0)
  return {
    color: ['#409FFF', '#66FFFF'],
    title: {
      text: '单位用户',
      subtext: String(total),
      left: 'center',
      top: '38%',
      textStyle: {
        color: '#fff',
        fontSize: 14,
        fontWeight: 'bold',
      },
      subtextStyle: {
        color: '#fff',
        fontSize: 22,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,32,64,0.95)',
      borderColor: '#409fff',
      borderWidth: 1,
      textStyle: { color: '#fff' },
      formatter: (params: any) => {
        return `${params.marker}${params.name}：${params.value}户 (${params.percent}%)`
      }
    },
    legend: {
      data: ['城镇居民（户）', '农村居民（户）'],
      textStyle: { color: '#fff' },
      right: '1%', // 更靠近网格右下角
      bottom: '1%',
      orient: 'vertical',
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '65%'],
        center: ['50%', '45%'],
        data: [
          { value: data.urbanResidentUserNumber ?? 0, name: '城镇居民（户）' },
          { value: data.ruralResidentUserNumber ?? 0, name: '农村居民（户）' },
        ],
        label: {
          show: true,
          color: '#fff',
          formatter: '{b}\n{c}户\n{d}%',
        },
        labelLine: { show: true },
      },
    ],
  }
}

const initChart = (data: any) => {
  if (!chartRef.value) return
  chart = echarts.init(chartRef.value)
  chart.setOption(getPieOption(data))
}

const handleResize = () => {
  chart?.resize()
}

onMounted(async () => {
  try {
    const res = await rhlxfb()
    const d = Array.isArray(res?.data) ? res.data[0] : {}
    fillUserStats(d)
    initChart(d)
    window.addEventListener('resize', handleResize)
  } catch (err) {
    console.error('rhlxfb接口调用失败:', err)
    initChart({})
    window.addEventListener('resize', handleResize)
  }
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.equipment-alarm-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.chart-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .chart-content {
    flex: 1;
    padding: 4px;

    .chart-container {
      width: 100%;
      height: 100%;
    }
  }
}

.user-overview-container {
  display: flex;
  flex-direction: column;
  width: 33.33%;
  flex-shrink: 0;
  height: 100%;
}

.user-card {
  position: relative;
  width: 100%;
  flex: 1;
  overflow: hidden;
  /* 卡片间距 */
  background: url('/src/assets/indoor-icon/right-bg.svg') no-repeat center/cover;
}
.user-card:not(:last-child) {
  margin-bottom: 16px;
}

.card-content {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  bottom: 12px;
  height: auto;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
}

.card-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  > img {
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    z-index: 1;
  }
  .second-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 18px;
    height: 18px;
    z-index: 2;
    img {
      width: 100%;
      height: 100%;
    }
  }
}

.card-info {
  flex: 1;
  min-width: 0;
  height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  .card-title {
    width: 100%;
    height: 18px;
    white-space: nowrap;
    color: #66ffff;
    font-family: 'Noto Sans SC';
    font-size: 13px;
    line-height: 18px;
    text-align: left;
    margin-bottom: 2px;
  }
  .card-value-container {
    width: 100%;
    height: 20px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: baseline;
    .card-value {
      color: #ffffff;
      font-family: 'DINPro';
      font-size: 16px;
      line-height: 20px;
      font-weight: bold;
      margin-right: 2px;
    }
    .card-unit {
      color: #ffffff;
      font-family: 'Noto Sans SC';
      font-size: 12px;
      line-height: 16px;
    }
  }
}

.card-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;

  > img {
    position: absolute;
    top: 0;
    left: 0;
    width: 48px;
    height: 48px;
    z-index: 1;
  }

  .second-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    z-index: 2;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.card-info {
  flex: 1;
  height: 48px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  .card-title {
    width: 100%;
    height: 20px;
    white-space: nowrap;
    color: #66ffff;
    font-family: 'Noto Sans SC';
    font-size: 14px;
    line-height: 20px;
    text-align: left;
    margin-bottom: 4px;
  }

  .card-value-container {
    width: 100%;
    height: 24px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: baseline;

    .card-value {
      color: #ffffff;
      font-family: 'DINPro';
      font-size: 18px;
      line-height: 24px;
      font-weight: bold;
      margin-right: 4px;
    }

    .card-unit {
      color: #ffffff;
      font-family: 'Noto Sans SC';
      font-size: 14px;
      line-height: 20px;
    }
  }
}

.card-border-bottom {
  position: absolute;
  left: 30px;
  bottom: 0px;
  width: calc(100% - 60px);
  height: 12px;
}
</style>
