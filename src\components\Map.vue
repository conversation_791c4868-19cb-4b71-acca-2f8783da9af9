<template>
  <div class="map-container">
    <div id="contain"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader'
import type {
  AMapMap,
  AMapMarker,
  AMapPolygon,
  AMapPolyline,
  AMapInstance,
  AMapInfoWindow,
  LocaContainerInstance,
  LocaHeatMapLayer,
  LocaLoadedInstance,
  HeatDataItem,
  Position,
  GenericMarkerConfig,
  GenericPolylineConfig,
} from '@/types/mapTypes'
// 将 svg 强制为 URL（Vite 可识别 ?url；webpack 下也通常返回 URL），并做兼容处理
import userTypeSvgRaw from '@/assets/map/userType.svg?url'

// 兼容不同打包器的导出形式（有时导入会是 { default: "/.../file.svg" }）
const userTypeSvg: string = (userTypeSvgRaw as any)?.default ?? (userTypeSvgRaw as any)

const props = defineProps({
  center: {
    type: Array,
    default: () => [114.828, 36.575],
  },
  zoom: {
    type: Number,
    default: 11.7,
  },
  viewMode: {
    type: String,
    default: '3D',
  },
  pitch: {
    type: Number,
    default: 0,
  },
  clickable: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['map-click', 'map-ready'])

// 响应式状态
const map = ref<AMapMap | null>(null)
const locaContainer = ref<LocaContainerInstance | null>(null)
const AMap = ref<AMapInstance | null>(null)
const Loca = ref<LocaLoadedInstance | null>(null)
const autoTourTimer = ref<NodeJS.Timeout | null>(null)
const currentMarker = ref<AMapMarker | null>(null) // 重命名，避免与通用标记混淆
const polygon = ref<AMapPolygon | null>(null)
const infoWindow = ref<AMapInfoWindow | null>(null)

const heatmapLayer = ref<LocaHeatMapLayer | null>(null)
const outLayer = ref<AMapPolygon | null>(null)

// 存储所有通用图层元素
const allMapLayers = ref<Map<string, (AMapMarker | AMapPolyline | AMapPolygon)[]>>(new Map())
// 新增：存储聚合器实例（layerId -> cluster instance）
const clusterers = ref<Map<string, any>>(new Map())

// 新增：控制是否允许点击地图关闭 InfoWindow（避免 marker 点击后被立即关闭）
const allowInfoWindowClose = ref<boolean>(true)

// 初始化地图
const initMap = async () => {
  try {
    await AMapLoader.load({
      key: '8a6865ecdde60542806eb4dd4da77aed',
      version: '2.0',
      // 新增 MarkerCluster 插件（JSAPI 2.0）
      plugins: ['AMap.GeoJSON', 'AMap.InfoWindow', 'AMap.MarkerCluster'],
      Loca: { version: '2.0.0' },
    })

    // 从全局 window 对象获取 AMap 和 Loca 实例
    AMap.value = (window as any).AMap as AMapInstance
    Loca.value = (window as any).Loca as LocaLoadedInstance

    if (!AMap.value) {
      console.error('AMap is not available after loading.')
      return
    }

    map.value = new AMap.value.Map('contain', {
      zoom: props.zoom,
      viewMode: props.viewMode,
      pitch: props.pitch,
      rotation: 0,
      center: props.center,
      mapStyle: 'amap://styles/darkblue',
      skyColor: '#081245',
      willReadFrequently: true,
      dragEnable: true,
      zoomEnable: true,
      doubleClickZoom: true,
      keyboardEnable: true,
    })

    // 新增：全局地图点击：当允许时点击地图会关闭 infoWindow（用于“点击非点出关闭小弹框”）
    map.value.on('click', () => {
      try {
        if (infoWindow.value && allowInfoWindowClose.value) {
          infoWindow.value.close()
          infoWindow.value = null
        }
      } catch (err) {
        /* ignore */
      }
    })

    // 在地图加载完成后再创建 Loca 容器
    map.value.on('complete', () => {
      if (Loca.value && map.value) {
        locaContainer.value = new Loca.value.Container({ map: map.value })
        if (locaContainer.value) {
          locaContainer.value.pointLight.intensity = 0
          locaContainer.value.ambLight.intensity = 1
          locaContainer.value.animate.start()
          // 显示区域图层
          showDistrictLayer()
        }
      }

      // 适应边界范围
      if (map.value && outLayer.value) {
        map.value.setFitView([outLayer.value], false, [20, 20, 20, 20])
      }

      emit('map-ready')
    })

    if (props.clickable) {
      map.value.on('click', e => {
        // 清除上一个标记
        if (currentMarker.value) {
          map.value!.remove(currentMarker.value)
        }
        // 创建新的标记
        currentMarker.value = new AMap.value!.Marker({
          position: e.lnglat,
        })
        map.value!.add(currentMarker.value)
        // 发送事件
        emit('map-click', e.lnglat)
      })
    }
  } catch (error) {
    console.error('地图加载失败:', error)
  }
}
// 显示区域图层
const showDistrictLayer = () => {
  if (!locaContainer.value || !Loca.value) {
    console.error('Loca or AMap is not initialized.')
    return
  }
  const outGeo = new Loca.value.GeoJSONSource({
    url: '/geo/feixiang-geometry.geojson',
  })

  const outLayer = new Loca.value.PolygonLayer({
    zIndex: 1,
    cullface: 'none',
    shininess: 1,
    acceptLight: false,
    blockHide: false,
    hasSide: true,
    hasTop: true,
    hasBottom: false,
    depth: true,
  })
  outLayer.setSource(outGeo)
  outLayer.setStyle({
    sideTopColor: function () {
      return 'rgba(30, 96, 174, 0.2)'
    },
    topColor: function () {
      return 'rgba(30, 96, 174, 0.2)'
    },
    sideBottomColor: function () {
      return 'rgba(45, 206, 252, 1)'
    },
    height: 1000,
    altitude: 0,
  })
  locaContainer.value.add(outLayer)
}

// 显示热力图
const showHeatMap = (heatData: HeatDataItem[]) => {
  destroyHeatMap() // 先移除已有的

  if (!heatData || heatData.length === 0) {
    return
  }

  if (!locaContainer.value || !Loca.value) {
    console.error('Loca or AMap is not initialized.')
    return
  }

  const heatGeo = new Loca.value.GeoJSONSource({
    data: {
      type: 'FeatureCollection',
      features: heatData.map(item => ({
        type: 'Feature',
        properties: { num: item.num },
        geometry: {
          type: 'Point',
          coordinates: item.lnglat,
        },
      })),
    },
  })

  heatmapLayer.value = new Loca.value.HeatMapLayer({
    zIndex: 10,
    opacity: 1,
    visible: true,
    zooms: [2, 22],
  })

  heatmapLayer.value.setSource(heatGeo, {
    radius: 30, // 半径
    unit: 'meter',
    height: 100, // 高度
    gradient: {
      0.1: '#2A85B8',
      0.2: '#16B0A9',
      0.3: '#29CF6F',
      0.4: '#5CE182',
      0.5: '#7DF675',
      0.6: '#FFF100',
      0.7: '#FAA53F',
      1: '#D04343',
    },
    value: (_index: number, feature: any) => feature.properties.num,
    heightBezier: [0, 0.53, 0.37, 0.98],
  })

  locaContainer.value.add(heatmapLayer.value)
}

// 销毁热力图
const destroyHeatMap = () => {
  if (heatmapLayer.value && locaContainer.value) {
    locaContainer.value.remove(heatmapLayer.value)
    heatmapLayer.value = null
  }
}

const addMarker = (position: Position) => {
  if (!map.value || !AMap.value) {
    console.error('Map or AMap is not initialized.')
    return
  }
  if (currentMarker.value) {
    map.value.remove(currentMarker.value)
  }
  currentMarker.value = new AMap.value.Marker({
    position: [position.lng, position.lat],
  })
  map.value.add(currentMarker.value)
}

const destroyMarker = () => {
  if (currentMarker.value && map.value) {
    map.value.remove(currentMarker.value)
    currentMarker.value = null
  }
}

// 绘制多边形
const drawPolygon = (path: any[]) => {
  destroyPolygon()
  if (!map.value || !AMap.value) {
    console.error('Map or AMap is not initialized.')
    return
  }
  polygon.value = new AMap.value.Polygon({
    path: path,
    strokeColor: '#00FF00',
    strokeWeight: 2,
    strokeOpacity: 0.8,
    fillColor: '#00FF00',
    fillOpacity: 0.3,
    zIndex: 50,
  })
  map.value.add(polygon.value)
  map.value.setFitView([polygon.value])
}

// 销毁多边形
const destroyPolygon = () => {
  if (polygon.value && map.value) {
    map.value.remove(polygon.value)
    polygon.value = null
  }
}

// 通用添加标记方法
const addGenericMarkers = (layerId: string, markersConfig: GenericMarkerConfig[]) => {
  if (!map.value || !AMap.value) {
    console.error('Map or AMap is not initialized.')
    return
  }
  clearLayer(layerId) // 先清除该图层已有的元素

  const markers: AMapMarker[] = []
  markersConfig.forEach(config => {
    // 判断是否为告警类型（优先判断 layerType，其次检查 iconUrl 名称）
    const isAlarm =
      config.layerType === 'alarm' || (config.iconUrl && /(?:user-alarm|gwsb-alarm)/.test(String(config.iconUrl)))
    const iconSrc = config.iconUrl || userTypeSvg

    // 默认样式改为根据类型动态生成：告警使用六边形动画，普通使用圆形
    const defaultMarkerContent = isAlarm
      ? `
      <div style="position:relative;width:36px;height:36px;display:flex;align-items:center;justify-content:center;">
        <!-- 描边层：稍大于主体，提供渐变描边效果 -->
        <div style="
          position:absolute;
          width:36px;
          height:36px;
          clip-path: polygon(25% 2%, 75% 2%, 100% 50%, 75% 98%, 25% 98%, 0% 50%);
          background: linear-gradient(135deg, rgba(255,200,140,0.95), rgba(255,120,60,0.85));
          opacity:0.9;
          filter: blur(1.5px);
          transform-origin:center;
        "></div>
        <!-- 发光脉冲层（较小）-->
        <div style="
          position:absolute;
          width:32px;
          height:32px;
          clip-path: polygon(25% 2%, 75% 2%, 100% 50%, 75% 98%, 25% 98%, 0% 50%);
          background: radial-gradient(circle, rgba(255,160,100,0.22) 0%, rgba(255,160,100,0.06) 60%, transparent 100%);
          animation: hexPulse 2.2s ease-out infinite;
          filter: blur(5px);
          transform-origin:center;
        "></div>
        <!-- 半透明光晕层（外圈）-->
        <div style="
          position:absolute;
          width:40px;
          height:40px;
          filter: blur(5px);
          opacity:0.12;
          border-radius:50%;
          background: radial-gradient(circle, rgba(255,170,110,0.10), transparent);
        "></div>
        <!-- 主体六边形（内层）-->
        <div style="
          width:28px;
          height:28px;
          clip-path: polygon(25% 2%, 75% 2%, 100% 50%, 75% 98%, 25% 98%, 0% 50%);
          display:flex;
          align-items:center;
          justify-content:center;
          background: linear-gradient(135deg, rgba(255,150,90,0.98), rgba(255,100,50,0.9));
          box-shadow: 0 8px 20px rgba(255,140,90,0.20), 0 0 12px rgba(255,170,110,0.12);
          border: 1.6px solid rgba(255,210,160,0.16);
          border-radius: 3px;
        ">
          <img src="${iconSrc}" style="width:12px;height:12px;display:block;filter:brightness(1.1)"/>
        </div>
      </div>
      <style>
        @keyframes hexPulse {
          0% { transform: scale(1); opacity: 0.95; }
          50% { transform: scale(1.3); opacity: 0.5; }
          100% { transform: scale(1.8); opacity: 0; }
        }
      </style>
    `
      : `
      <div style="
        width:36px;
        height:36px;
        border-radius:50%;
        display:flex;
        align-items:center;
        justify-content:center;
        background: rgba(64,159,255,0.12);
        backdrop-filter: blur(6px);
        -webkit-backdrop-filter: blur(6px);
        box-shadow: 0 6px 18px rgba(3,10,30,0.35);
        border: 1px solid rgba(64,159,255,0.18);
      ">
        <img src="${iconSrc}" style="width:16px;height:16px;display:block;"/>
      </div>
    `

    // 根据类型调整偏移，告警使用 -18 以匹配 36px 容器大小
    const offsetPixel = isAlarm ? new AMap.value!.Pixel(-18, -18) : new AMap.value!.Pixel(-18, -18)

    const marker = new AMap.value!.Marker({
      position: [config.position.lng, config.position.lat],
      offset: offsetPixel,
      extData: config.extData,
      content: config.content || defaultMarkerContent,
    })

    // 点击事件（优先调用配置里的 onClick，否则打印信息）
    if ((config as any).onClick) {
      marker.on('click', (e: any) => {
        try {
          ;(config as any).onClick(config.extData, e)
        } catch (err) {
          console.error('marker onClick error:', err)
        }
      })
    } else {
      marker.on('click', () => {
        console.log('marker clicked extData:', marker.getExtData())
      })
    }

    // 悬停显示 InfoWindow（保持原有逻辑）
    if (config.getInfoWindowContent) {
      marker.on('mouseover', (e: any) => {
        ;(e.target as AMapMarker).setTop(true)
        const infoObj = (e.target as AMapMarker).getExtData()
        const content = config.getInfoWindowContent!(infoObj)
        if (infoWindow.value) {
          infoWindow.value.close()
        }
        infoWindow.value = new AMap.value!.InfoWindow({
          content: `<div class="amap-info-custom">${content}</div>`,
          offset: new AMap.value!.Pixel(0, -30),
          isCustom: true,
          closeWhenClickMap: false,
        })
        infoWindow.value.open(map.value!, e.lnglat)
      })

      marker.on('mouseout', e => {
        ;(e.target as AMapMarker).setTop(false)
        if (infoWindow.value) {
          infoWindow.value.close()
        }
      })
    }

    map.value!.add(marker)
    markers.push(marker)
  })
  allMapLayers.value.set(layerId, markers)
}

// 通用添加管线方法
const addGenericPolylines = (layerId: string, polylinesConfig: GenericPolylineConfig[]) => {
  if (!map.value || !AMap.value) {
    console.error('Map or AMap is not initialized.')
    return
  }
  clearLayer(layerId)

  const polylines: AMapPolyline[] = []
  polylinesConfig.forEach(config => {
    const polyline = new AMap.value!.Polyline({
      path: config.path.map(p => [p.lng, p.lat]),
      strokeColor: config.strokeColor || '#FF0000',
      strokeWeight: config.strokeWeight || 3,
      strokeOpacity: config.strokeOpacity || 0.8,
      extData: config.extData,
    })

    if (config.getInfoWindowContent) {
      polyline.on('mouseover', (e: any) => {
        try {
          e.target.setOptions({ strokeOpacity: 1, isOutline: true })
        } catch (err) {
          /* ignore setOptions error for some AMap versions */
        }
        const infoObj = polyline.getExtData()
        const content = config.getInfoWindowContent!(infoObj)
        if (infoWindow.value) infoWindow.value.close()
        // 包装内容，使用统一样式类（折线处）
        infoWindow.value = new AMap.value!.InfoWindow({
          content: `<div class="amap-info-custom">${content}</div>`,
          offset: new AMap.value!.Pixel(0, -30),
          isCustom: true,
          closeWhenClickMap: false,
        })
        infoWindow.value.open(map.value!, e.lnglat)
      })

      polyline.on('mouseout', () => {
        try {
          polyline.setOptions({ strokeOpacity: config.strokeOpacity ?? 0.8, isOutline: false })
        } catch (err) {
          /* ignore */
        }
        if (infoWindow.value) infoWindow.value.close()
      })
    }

    map.value!.add(polyline)
    polylines.push(polyline)
  })
  allMapLayers.value.set(layerId, polylines)
}

// 新增：使用 MarkerCluster 添加聚合点（正确的 2.0 方式）
const addClusteredMarkers = (
  layerId: string,
  markersConfig: GenericMarkerConfig[],
  options?: { gridSize?: number; maxZoom?: number; renderClusterMarker?: Function },
) => {
  if (!map.value || !AMap.value) {
    console.error('Map or AMap is not initialized.')
    return
  }

  // 先清除已有该图层（包括普通元素和已有聚合器）
  clearLayer(layerId)

  console.log(`准备创建聚合点，数据数量: ${markersConfig.length}`)

  // ⭐ 关键修复：MarkerCluster 2.0 接受的是包含 lnglat 字段的数据对象数组，而不是 Marker 对象！
  // 参考官方示例：points = [{ lnglat: ["108.939621", "34.343147"] }, ...]
  const points = markersConfig.map((config, index) => {
    const point = {
      lnglat: [config.position.lng, config.position.lat],
      ...config.extData, // 保存原始数据
      _config: config, // 保存配置，用于自定义渲染和事件
    }

    if (index < 3) {
      console.log(`Point ${index}:`, point)
    }

    return point
  })

  console.log(`转换后的 points 数据数量: ${points.length}`)

  // 高德地图 2.0 正确的聚合器使用方式
  if (!AMap.value) {
    console.error('AMap 未初始化')
    return
  }

  AMap.value.plugin('AMap.MarkerCluster', () => {
    try {
      console.log('✅ MarkerCluster 插件加载成功')

      // 自定义单个点的渲染样式（非聚合状态）
      const renderMarker = (context: any) => {
        const data = context.data[0] // 单个点的数据
        const config = data._config || {}
        const isAlarm =
          config.layerType === 'alarm' || (config.iconUrl && /(?:user-alarm|gwsb-alarm)/.test(String(config.iconUrl)))
        // 统一使用用户设备的圆形配色与图标（管网设备 gwsb 不再特殊化）
        const outerGradient = 'rgba(64,159,255,0.4)'
        const midGradient = 'rgba(64,159,255,0.3)'
        const coreGradientStart = 'rgba(64,159,255,0.95)'
        const coreGradientEnd = 'rgba(50,140,235,0.7)'
        const iconSrc = config.iconUrl || userTypeSvg

        if (isAlarm) {
          // 告警单点：较小的六边形样式，增加描边与荧光，偏移 -18
          const content = `
            <div style="position:relative;width:36px;height:36px;display:flex;align-items:center;justify-content:center;">
              <div style="
                position:absolute;
                width:36px;
                height:36px;
                clip-path: polygon(25% 2%, 75% 2%, 100% 50%, 75% 98%, 25% 98%, 0% 50%);
                background: linear-gradient(135deg, rgba(255,200,140,0.95), rgba(255,120,60,0.85));
                opacity:0.9;
                filter: blur(1.5px);
                transform-origin:center;
              "></div>
              <div style="
                position:absolute;
                width:32px;
                height:32px;
                clip-path: polygon(25% 2%, 75% 2%, 100% 50%, 75% 98%, 25% 98%, 0% 50%);
                background: radial-gradient(circle, rgba(255,160,100,0.22) 0%, rgba(255,160,100,0.06) 60%, transparent 100%);
                animation: hexPulse 2.2s ease-out infinite;
                filter: blur(5px);
                transform-origin:center;
              "></div>
              <div style="
                position:absolute;
                width:40px;
                height:40px;
                filter: blur(5px);
                opacity:0.12;
                border-radius:50%;
                background: radial-gradient(circle, rgba(255,170,110,0.10), transparent);
              "></div>
              <div style="
                width:28px;
                height:28px;
                clip-path: polygon(25% 2%, 75% 2%, 100% 50%, 75% 98%, 25% 98%, 0% 50%);
                display:flex;
                align-items:center;
                justify-content:center;
                background: linear-gradient(135deg, rgba(255,150,90,0.98), rgba(255,100,50,0.9));
                box-shadow: 0 8px 20px rgba(255,140,90,0.20), 0 0 12px rgba(255,170,110,0.12);
                border: 1.6px solid rgba(255,210,160,0.16);
                border-radius: 3px;
              ">
                <img src="${iconSrc}" style="width:12px;height:12px;display:block;filter:brightness(1.1)"/>
              </div>
            </div>
            <style>
              @keyframes hexPulse {
                0% { transform: scale(1); opacity: 0.95; }
                50% { transform: scale(1.3); opacity: 0.5; }
                100% { transform: scale(1.8); opacity: 0; }
              }
            </style>
          `
          context.marker.setContent(content)
          context.marker.setOffset(new AMap.value!.Pixel(-18, -18))
        } else {
          // 统一为用户设备的圆形样式（管网设备也使用此样式）
          const content =
            config?.content ||
            `
            <div class="map-marker-wrapper" style="
              position: relative;
              width: 36px;
              height: 36px;
            ">
              <div style="
                position: absolute;
                width: 36px;
                height: 36px;
                border-radius: 50%;
                background: radial-gradient(circle, ${outerGradient} 0%, rgba(0,0,0,0) 70%);
                animation: markerPulse 2s ease-out infinite;
              "></div>
              <div style="
                position: absolute;
                width: 30px;
                height: 30px;
                top: 3px;
                left: 3px;
                border-radius: 50%;
                background: radial-gradient(circle, ${midGradient} 0%, rgba(0,0,0,0) 60%);
                border: 1px solid rgba(255,255,255,0.06);
                box-shadow: 0 0 8px rgba(64,159,255,0.45);
              "></div>
              <div style="
                position: absolute;
                width: 20px;
                height: 20px;
                top: 8px;
                left: 8px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                background: linear-gradient(135deg, ${coreGradientStart} 0%, ${coreGradientEnd} 100%);
                box-shadow: 0 2px 8px rgba(3,10,30,0.5);
              ">
                <img src="${iconSrc}" style="width:12px;height:12px;display:block;filter:brightness(1.4);"/>
              </div>
            </div>
            <style>
              @keyframes markerPulse {
                0% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.5); opacity: 0.5; }
                100% { transform: scale(2); opacity: 0; }
              }
            </style>
          `
          context.marker.setContent(content)
          context.marker.setOffset(new AMap.value!.Pixel(-18, -18))
        }

        // 添加点击事件
        if (config?.onClick) {
          context.marker.on('click', (e: any) => {
            try {
              config.onClick(config.extData, e)
            } catch (err) {
              console.error('marker onClick error:', err)
            }
          })
        }
      }

      // 自定义聚合点的渲染样式（炫酷科技风）
      const renderClusterMarker =
        options?.renderClusterMarker ||
        ((context: any) => {
          const count = context.count
          // 所有类型（非告警）统一采用用户设备的圆形科技风配色逻辑
          const baseSize = 40
          const sizeFactor = Math.min(Math.log(count + 1) * 8, 26)
          const size = baseSize + sizeFactor

          // 分级配色（与用户设备保持一致）
          let colorStart: string, colorEnd: string, glowColor: string, borderColor: string
          if (count < 5) {
            colorStart = 'rgba(100,200,255,0.95)'
            colorEnd = 'rgba(80,180,255,0.7)'
            glowColor = 'rgba(100,200,255,0.8)'
            borderColor = 'rgba(150,220,255,0.7)'
          } else if (count < 15) {
            colorStart = 'rgba(64,159,255,0.95)'
            colorEnd = 'rgba(50,140,235,0.7)'
            glowColor = 'rgba(64,159,255,0.8)'
            borderColor = 'rgba(102,180,255,0.7)'
          } else if (count < 30) {
            colorStart = 'rgba(40,120,220,0.95)'
            colorEnd = 'rgba(30,100,200,0.7)'
            glowColor = 'rgba(40,120,220,0.8)'
            borderColor = 'rgba(70,150,255,0.7)'
          } else {
            colorStart = 'rgba(20,80,180,0.95)'
            colorEnd = 'rgba(15,60,150,0.7)'
            glowColor = 'rgba(20,80,180,0.8)'
            borderColor = 'rgba(50,120,200,0.7)'
          }

          const div = document.createElement('div')
          div.style.position = 'relative'
          div.style.width = `${size}px`
          div.style.height = `${size}px`

          div.innerHTML = `
          <div style="
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            border-radius: 50%;
            background: radial-gradient(circle, ${glowColor} 0%, transparent 70%);
            animation: clusterPulse 3s ease-in-out infinite;
          "></div>
          <div style="
            position: absolute;
            width: ${size - 8}px;
            height: ${size - 8}px;
            top: 4px;
            left: 4px;
            border-radius: 50%;
            border: 2px solid transparent;
            border-top-color: ${borderColor};
            border-right-color: ${borderColor};
            animation: rotate 4s linear infinite;
            opacity: 0.7;
          "></div>
          <div style="
            position: absolute;
            width: ${size - 20}px;
            height: ${size - 20}px;
            top: 10px;
            left: 10px;
            border-radius: 50%;
            background: linear-gradient(135deg, ${colorStart} 0%, ${colorEnd} 100%);
            border: 2px solid ${borderColor};
            box-shadow: 0 0 20px ${glowColor}, inset 0 0 15px rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
          ">
            <span style="
              color: #fff;
              font-size: ${Math.max(16, Math.min(20, 12 + count * 0.2))}px;
              font-weight: bold;
            ">${count}</span>
          </div>
          <style>
            @keyframes clusterPulse { 0%,100%{transform:scale(1);opacity:0.7;}50%{transform:scale(1.25);opacity:0.35;} }
            @keyframes rotate { from{transform:rotate(0deg);} to{transform:rotate(360deg);} }
          </style>
        `

          context.marker.setContent(div)
          context.marker.setOffset(new AMap.value!.Pixel(-size / 2, -size / 2))
        })

      // 聚合器配置
      const clusterOptions = {
        gridSize: options?.gridSize ?? 60,
        maxZoom: options?.maxZoom ?? 18,
        renderClusterMarker: renderClusterMarker,
        renderMarker: renderMarker, // 关键：自定义单个点的样式
      }
      console.log('聚合器配置:', clusterOptions)

      const cluster = new (AMap.value as any).MarkerCluster(map.value!, points, clusterOptions)

      // 保存引用
      clusterers.value.set(layerId, cluster)
      // 注意：这里保存的是 points 而不是 markers
      allMapLayers.value.set(layerId, points as any)

      // 不自动缩放地图，保持用户当前的视野
      // 如需自动缩放，可取消下面的注释
      // if (points.length > 0) {
      //   setTimeout(() => {
      //     try {
      //       const bounds = new AMap.value!.Bounds(
      //         points[0].lnglat as [number, number],
      //         points[0].lnglat as [number, number]
      //       )
      //       points.forEach(p => {
      //         bounds.extend(p.lnglat as [number, number])
      //       })
      //       map.value!.setBounds(bounds, false, [100, 100, 100, 100])
      //     } catch (e) {
      //       console.warn('自适应地图视野失败:', e)
      //     }
      //   }, 500)
      // }
    } catch (err) {
      console.error('错误详情:', err)
    }
  })
}

// 通用清除图层方法（扩展以支持聚合器清理）
const clearLayer = (layerId: string) => {
  if (!map.value) return

  console.log(`清除图层: ${layerId}`)

  // 优先处理聚合器（如果存在）
  const cluster = clusterers.value.get(layerId)
  if (cluster) {
    try {
      console.log(`清除聚合器: ${layerId}`)
      // 高德地图 2.0 的 MarkerCluster 清理方法
      if (typeof cluster.setMap === 'function') {
        cluster.setMap(null)
        console.log('聚合器已从地图移除')
      }
    } catch (err) {
      console.warn(`清除聚合器失败: ${layerId}`, err)
    }
    clusterers.value.delete(layerId)
  }

  // 清除普通元素（非聚合的标记、折线等）
  const elements = allMapLayers.value.get(layerId)
  if (elements && elements.length > 0) {
    // 检查是否是 Marker 对象数组（而不是聚合器的 points 数据）
    const firstElement = elements[0]
    if (firstElement && typeof firstElement === 'object' && 'setMap' in firstElement) {
      // 是 Marker 对象，需要从地图移除
      try {
        console.log(`从地图移除 ${elements.length} 个标记元素`)
        map.value.remove(elements)
      } catch (err) {
        console.warn(`移除地图元素失败: ${layerId}`, err)
      }
    } else {
      // 是 points 数据，不需要手动移除（聚合器已处理）
      console.log(`清除 ${elements.length} 个 points 数据引用`)
    }
    allMapLayers.value.delete(layerId)
  }

  if (infoWindow.value) {
    infoWindow.value.close()
  }
}

// 新增：在地图组件内部提供显示/关闭 InfoWindow 的方法，供父组件调用
const showInfoWindow = (content: string, position?: any) => {
  if (!AMap.value || !map.value) return
  try {
    // 先关闭之前的
    if (infoWindow.value) {
      try {
        infoWindow.value.close()
      } catch (e) {
        /* ignore */
      }
      infoWindow.value = null
    }

    // 在即将打开时，暂时禁止地图点击关闭（避免 marker 点击之后被立即关闭）
    allowInfoWindowClose.value = false

    infoWindow.value = new AMap.value!.InfoWindow({
      content: `<div class="amap-info-custom">${content}</div>`,
      offset: new AMap.value!.Pixel(0, -30),
      isCustom: true,
      closeWhenClickMap: false,
    })
    // position 支持：AMap.LngLat、{lng,lat} 或 [lng,lat]
    if (position && (position.lng !== undefined || Array.isArray(position))) {
      infoWindow.value.open(map.value!, position)
    } else {
      infoWindow.value.open(map.value!, map.value!.getCenter())
    }

    // 小延时后允许地图点击关闭（100ms-300ms 之间可调，避免事件竞争）
    setTimeout(() => {
      allowInfoWindowClose.value = true
    }, 200)
  } catch (err) {
    console.error('showInfoWindow error:', err)
  }
}

const closeInfoWindow = () => {
  try {
    if (infoWindow.value) {
      infoWindow.value.close()
      infoWindow.value = null
    }
  } catch (err) {
    /* ignore */
  }
}

// 新增：地图聚焦到指定位置
const flyToPosition = (lng: number | string, lat: number | string, zoom?: number) => {
  if (!map.value || !AMap.value) {
    console.warn('地图未初始化，无法执行聚焦操作')
    return
  }

  try {
    // 转换坐标为数字类型
    const lngNum = typeof lng === 'string' ? parseFloat(lng) : lng
    const latNum = typeof lat === 'string' ? parseFloat(lat) : lat

    // 验证坐标有效性
    if (isNaN(lngNum) || isNaN(latNum)) {
      console.warn('无效的坐标数据:', { lng, lat })
      return
    }

    // 设置默认缩放级别
    const targetZoom = zoom ?? 15

    console.log(`地图聚焦到坐标: [${lngNum}, ${latNum}], 缩放级别: ${targetZoom}`)

    // 使用高德地图的 setZoomAndCenter 方法实现平滑聚焦
    map.value.setZoomAndCenter(targetZoom, [lngNum, latNum], false, 800)
  } catch (error) {
    console.error('地图聚焦失败:', error)
  }
}

// 生命周期钩子
onMounted(initMap)

defineExpose({
  showHeatMap,
  destroyHeatMap,
  addMarker,
  destroyMarker,
  drawPolygon,
  destroyPolygon,
  addGenericMarkers,
  addGenericPolylines,
  // 新增暴露聚合添加方法和清除方法
  addClusteredMarkers,
  clearLayer,
  getAMapInstance: () => AMap.value, // 暴露 AMap 实例，以便父组件创建复杂元素
  // 新增：暴露 info window 操作
  showInfoWindow,
  closeInfoWindow,
  // 新增：暴露地图聚焦方法
  flyToPosition,
})

onBeforeUnmount(() => {
  if (locaContainer.value) locaContainer.value.destroy()
  if (map.value) map.value.destroy()
  if (autoTourTimer.value) clearTimeout(autoTourTimer.value)
  if (infoWindow.value) infoWindow.value.close()
  allMapLayers.value.clear()
  // 清理所有聚合器引用
  clusterers.value.forEach(c => {
    try {
      if (typeof c.clearMarkers === 'function') c.clearMarkers()
      else if (typeof c.setMap === 'function') c.setMap(null)
    } catch (e) {
      /* ignore */
    }
  })
  clusterers.value.clear()
})
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#contain {
  width: 100%;
  height: 100%;
}
</style>

<style>
.amap-info-custom {
  background: rgba(12, 22, 40, 0.95);
  color: #e6f4ff;
  padding: 10px 12px;
  border-radius: 8px;
  font-size: 13px;
  line-height: 1.5;
  max-width: 260px;
  box-shadow: 0 6px 20px rgba(3, 10, 30, 0.6);
  border: 1px solid rgba(64, 159, 255, 0.12);
  word-break: break-word;
}
/* 强调标题或字段名 */
.amap-info-custom .title {
  font-weight: 600;
  color: #fff;
  margin-bottom: 6px;
  font-size: 14px;
}
/* 标签和数值分开显示 */
.amap-info-custom .row {
  display: flex;
  gap: 8px;
  /* 改为顶部对齐，避免长文本导致左右第一行不齐 */
  align-items: flex-start;
  margin: 4px 0;
}
.amap-info-custom .label {
  color: #9fc8ff;
  min-width: 56px;
  font-weight: 500;
}
.amap-info-custom .value {
  color: #e6f4ff;
  flex: 1;
  word-break: break-word;
}
/* 小屏时适当缩小字体 */
@media (max-width: 900px) {
  .amap-info-custom {
    font-size: 12px;
    padding: 8px 10px;
    max-width: 200px;
  }
}
</style>
