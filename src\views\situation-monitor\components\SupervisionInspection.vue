<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">从业人员与持证情况</div>
      <div class="header-dropdown"></div>
    </div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { cyryczqk } from '@/common/api/situationMonitor'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
let tooltipTimer: ReturnType<typeof setInterval> | null = null
let currentIndex = -1

// 图表 option 初始化为空，等接口数据回来再填充
let option: any = {
  animation: true,
  backgroundColor: 'transparent',
  grid: {
    left: '5%',
    right: '0%',
    top: '15%',
    bottom: '0%',
    containLabel: true,
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'shadow' },
    textStyle: { color: '#fff' },
    backgroundColor: 'rgba(42, 56, 77, 0.6)',
    borderWidth: 0,
  },
  legend: {
    data: ['从业人员', '持证人员'],
    textStyle: { color: '#fff', fontSize: 14 },
    itemWidth: 20,
    itemHeight: 10,
    icon: 'rect',
    itemGap: 24,
    top: 0,
    selectedMode: false,
  },
  xAxis: {
    type: 'category',
    data: [],
    axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.45)' } },
    axisLabel: { color: '#fff', fontSize: 14 },
    axisTick: { show: false },
  },
  yAxis: {
    type: 'value',
    name: '单位：万次',
    nameTextStyle: { color: '#fff', fontSize: 14 },
    axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.45)' } },
    axisLabel: { color: '#fff', fontSize: 14 },
    splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)', type: 'dashed' } },
  },
  series: [
    {
      name: '从业人员',
      type: 'bar',
      data: [],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(255, 198, 26, 0.15)' },
          { offset: 1, color: 'rgba(255, 198, 26, 0.75)' },
        ]),
      },
      legendHoverLink: false,
      legend: { itemStyle: { color: '#FFC61A' } },
      barWidth: 20,
      z: 2,
    },
    {
      name: 'icon',
      type: 'pictorialBar',
      emphasis: { disabled: true },
      symbol: 'rect',
      symbolPosition: 'end',
      symbolSize: [20, 2],
      symbolOffset: ['-55%', 0],
      z: 10,
      itemStyle: { color: '#FFC61A' },
      tooltip: { show: false },
      data: [],
    },
    {
      name: '持证人员',
      type: 'bar',
      data: [],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(77, 203, 98, 0.15)' },
          { offset: 1, color: 'rgba(77, 203, 98, 0.75)' },
        ]),
      },
      legendHoverLink: false,
      emphasis: { itemStyle: { color: '#4DCB62' } },
      barWidth: 20,
      z: 2,
    },
    {
      name: 'icon',
      type: 'pictorialBar',
      emphasis: { disabled: true },
      symbol: 'rect',
      symbolPosition: 'end',
      symbolSize: [20, 2],
      symbolOffset: ['55%', 0],
      z: 10,
      itemStyle: { color: '#4DCB62' },
      tooltip: { show: false },
      data: [],
    },
  ],
}

const initChart = () => {
  if (!chartRef.value) return
  chart = echarts.init(chartRef.value)
  chart.setOption(option)
  startTooltipAnimation()
}

// 根据数据更新 option 并刷新图表
const updateChartData = (data: any[]) => {
  const xAxisData = data.map(item => item.qymc)
  const cyryData = data.map(item => item.cyry)
  const czryData = data.map(item => item.czry)

  option.xAxis.data = xAxisData
  option.series[0].data = cyryData
  option.series[1].data = cyryData
  option.series[2].data = czryData
  option.series[3].data = czryData

  chart?.setOption(option, true)
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = option.xAxis.data.length
  if (!chart || dataCount === 0) return

  if (tooltipTimer) clearInterval(tooltipTimer)

  tooltipTimer = setInterval(() => {
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
    currentIndex = (currentIndex + 1) % dataCount
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(async () => {
  initChart()
  window.addEventListener('resize', handleResize)

  try {
    const res = await cyryczqk()
    // 只处理有 data 的情况
    if (Array.isArray(res.data)) {
      updateChartData(res.data)
    }
    console.log('cyryczqk response:', res)
  } catch (err) {
    console.error('cyryczqk error:', err)
  }
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) clearInterval(tooltipTimer)
})
</script>

<style scoped>
@import '@/styles/index.css';

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
