import request from '@/utils/request'

/**
 * 安全评价-量化评估概况 数据结构
 */
export interface SafetyAssessmentIndicatorData {
  count: number
  standardCount: number
  unStandardCount: number
  standardRate?: number | string
  toBeRectifiedCount: number
  addToBeRectifiedCount: number
  underRectificationCount: number
  progressRate?: number | string
  acceptedCount: number
  thisMonthAcceptedCount: number
  notRectificationCount: number
}

/**
 * 通用响应结构，保持与现有 runMonitor 风格一致：
 * - 拦截器已将 response.data 返回，这里通常为 { code, message, data } 或直接为数据对象
 */
export interface ApiResponse<T = any> {
  code?: number
  message?: string
  msg?: string
  success?: boolean
  pageInfo?: any
  data?: T | T[]
}

// 请求体接口（根据实际字段调整）
export interface DeviceOverviewPayload {
  // ...根据接口实际需要添加字段
  [key: string]: any
}

// 额外查询参数（与其余 API 一致）
export interface IndicatorParams {
  [key: string]: any
}

/**
 * 安全评价-量化评估概况
 * 说明：
 * - 参考 [export function sbjc_sbgk()](src/common/api/runMonitor.ts:16) 的风格
 * - 默认附带 applicationName=fxrq（与现有接口保持一致）
 * - 返回值保持拦截器后的原样（可能是 { code, data, message }，也可能是直接数据对象）
 */
export function getSafetyAssessmentIndicator(
  payload: DeviceOverviewPayload = {},
): Promise<ApiResponse<SafetyAssessmentIndicatorData>> {
  return request.post(
    '/service-api/exposureAPIS/path/safetyAssessment/indicator/9227274C7D9EE8CE721D8ACB84C100C28D28C89C7F622E0B323DEB35BB1CE4B6',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}


/**
 * 企业安全五维评估
 * GET /api/exposureAPIS/path/safetyAssessment/fiveDimensional/{token}
 */
export function getSafetyAssessmentFiveDimensional(
  payload: DeviceOverviewPayload = {},
): Promise<ApiResponse<any>> {
  return request.post(
    '/service-api/exposureAPIS/path/safetyAssessment/fiveDimensional/9227274C7D9EE8CE721D8ACB84C100C2724F286AF0DC60EBC05D8646D8B59B7A4DB02F93383C0102',
    payload,
    { params: { applicationName: 'fxrq' } },
  )
}

/**
 * 未达标企业趋势分析
 * GET /api/exposureAPIS/path/safetyAssessment/notEnterpriseTrend/{token}
 */
export function getSafetyAssessmentNotEnterpriseTrend(
  payload: DeviceOverviewPayload = {},
): Promise<ApiResponse<any>> {
  return request.post(
    '/service-api/exposureAPIS/path/safetyAssessment/notEnterpriseTrend/9227274C7D9EE8CE721D8ACB84C100C2C8C9C155F07A9C90D898B91EDC52055D0838B1C1A2E6601B',
    payload,
    { params: { applicationName: 'fxrq' } },
  )
}

/**
 * 预警事件月度分析
 * GET /api/exposureAPIS/path/safetyAssessment/warningEventsMonthlyAnalysis/{token}
 */
export function getSafetyAssessmentWarningEventsMonthlyAnalysis(
  payload: DeviceOverviewPayload = {},
): Promise<ApiResponse<any>> {
  return request.post(
    '/service-api/exposureAPIS/path/safetyAssessment/warningEventsMonthlyAnalysis/9227274C7D9EE8CE721D8ACB84C100C226B3AB1C9B3D505EEC113CCB4A9884C2C7AA18A8BA6E0436743D072C4DA9ECB1',
    payload,
    { params: { applicationName: 'fxrq' } },
  )
}

/**
 * 企业安全评分TOP5
 * GET /api/exposureAPIS/path/safetyAssessment/securityScoreTop5/{token}
 */
export function getSafetyAssessmentSecurityScoreTop5(
  payload: DeviceOverviewPayload = {},
): Promise<ApiResponse<any>> {
  return request.post(
    '/service-api/exposureAPIS/path/safetyAssessment/securityScoreTop5/9227274C7D9EE8CE721D8ACB84C100C295EFEC4870801649D351A4D8E7D979EFC340F9FE7C982802',
    payload,
    { params: { applicationName: 'fxrq' } },
  )
}

/**
 * 地区达标情况
 * GET /api/exposureAPIS/path/safetyAssessment/regionalStandards/{token}
 */
export function getSafetyAssessmentRegionalStandards(
  payload: DeviceOverviewPayload = {},
): Promise<ApiResponse<any>> {
  return request.post(
    '/service-api/exposureAPIS/path/safetyAssessment/regionalStandards/9227274C7D9EE8CE721D8ACB84C100C2FADE1B6B38FF9632484A6A09E50DC7A61C3B5DD6EE108D4D',
    payload,
    { params: { applicationName: 'fxrq' } },
  )
}

/**
 * 未达标企业经纬度信息
 * GET /api/exposureAPIS/path/safetyAssessment/nonCompliantEnterprises/{token}
 */
export function getSafetyAssessmentNonCompliantEnterprises(
  payload: DeviceOverviewPayload = {},
): Promise<ApiResponse<any>> {
  return request.post(
    '/service-api/exposureAPIS/path/safetyAssessment/nonCompliantEnterprises/9227274C7D9EE8CE721D8ACB84C100C2B1E164BC47D7638FE9DF0F8FF52AA1341DC13C6A0C3829744DB02F93383C0102',
    payload,
    { params: { applicationName: 'fxrq' } },
  )
}

/**
 * 预警事件经纬度信息
 * GET /api/exposureAPIS/path/safetyAssessment/warningEventInfo/{token}
 */
export function getSafetyAssessmentWarningEventInfo(
  payload: DeviceOverviewPayload = {},
): Promise<ApiResponse<any>> {
  return request.post(
    '/service-api/exposureAPIS/path/safetyAssessment/warningEventInfo/9227274C7D9EE8CE721D8ACB84C100C226B3AB1C9B3D505EE02DB6A6930FEB1799E9B221B5598C38',
    payload,
    { params: { applicationName: 'fxrq' } },
  )
}
