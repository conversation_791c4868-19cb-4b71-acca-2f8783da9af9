export const layerData = {
  monitor: {
    label: '监测设备',
    icon: 'monitor',
    // 由于用户设备已被选中，告警组整体设为选中（仅对未 disabled 的子项有效）
    checked: true,
    // 允许对告警组中的用户设备进行操作，其他子项禁用
    children: [
      {
        // id 从 'pipeline-alarm' 改为 'gwsb'，以匹配地图渲染逻辑中对管网设备图层的判断 (itemId === 'gwsb')
        id: 'gwsb',
        label: '管网设备',
        checked: true,
        disabled: false,
      },
      {
        id: 'user',
        label: '用户设备',
        checked: true,
        disabled: false,
      },
    ],
  },
  alarm: {
    label: '设备告警',
    icon: 'alarm',
    // 由于用户设备已被选中，告警组整体设为选中（仅对未 disabled 的子项有效）
    checked: true,
    // 允许对告警组中的用户设备进行操作，其他子项禁用
    children: [
      {
        // id 从 'pipeline-alarm' 改为 'gwsb'，以匹配地图渲染逻辑中对管网设备图层的判断 (itemId === 'gwsb')
        id: 'gwsb-alarm',
        label: '管网设备告警',
        checked: true,
        disabled: false,
      },
      {
        id: 'user-alarm',
        label: '用户设备告警',
        checked: true,
        disabled: false,
      },
    ],
  },
  station: {
    label: '场站设施',
    icon: 'station',
    checked: false,
    // 整个组禁用（不能操作）
    disabled: true,
    children: [
      {
        id: 'door-station',
        label: '门站',
        checked: false,
        disabled: true,
      },
      {
        id: 'storage-station',
        label: '储备站',
        checked: false,
        disabled: true,
      },
      {
        id: 'pressure-station-1',
        label: '调压站',
        checked: false,
        disabled: true,
      },
      {
        id: 'valve-chamber-1',
        label: '阀门井',
        checked: false,
        disabled: true,
      },
    ],
  },
  pipeline: {
    label: '管网',
    icon: 'pipeline',
    checked: false,
    disabled: true,
    children: [
      {
        id: 'high-pressure-1',
        label: '高压管网',
        checked: false,
        disabled: true,
      },
      {
        id: 'medium-pressure-h1',
        label: '中压管网',
        checked: false,
        disabled: true,
      },
      {
        id: 'low-pressure-h1-1',
        label: '低压管网',
        checked: false,
        disabled: true,
      },
    ],
  },
}
