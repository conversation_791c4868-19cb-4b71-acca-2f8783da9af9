<template>
  <div class="panel-container">
    <div class="panel-header">未整改隐患预警</div>
    <div class="warning-list-outer" :style="{ height: `${visibleCount * rowHeight}px` }" ref="listContainerRef">
      <div
        class="warning-list"
        :style="{
          transform: `translateY(-${scrollTop}px)`,
          transition: transitionEnabled ? 'transform 0.5s ease' : 'none',
        }"
      >
        <div
          v-for="item in scrollList"
          :key="item.id + '-' + Math.random()"
          class="warning-row"
          :style="{ backgroundImage: `url(/src/assets/patrol-inspection/${item.n}.svg)` }"
        >
          <span class="warning-title" :title="`${item.content || item.problemDescription} - ${item.address}`">
            {{ item.content || item.problemDescription || '未知隐患内容' }}-{{ item.address || '未知位置' }}
          </span>
          <span class="warning-overdue">超期{{ item.overdueDays || item.n }}天</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { patrol_hidden_danger } from '@/common/api/patrol' // 新增：导入接口

function getRandomInt(min: number, max: number) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}
function shuffle<T>(arr: T[]): T[] {
  return arr
    .map(item => ({ item, sort: Math.random() }))
    .sort((a, b) => a.sort - b.sort)
    .map(({ item }) => item)
}

// 新增：计算超期天数
const calculateOverdueDays = (findTime: string): number => {
  const findDate = new Date(findTime)
  const now = new Date()
  const diffTime = now.getTime() - findDate.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
}

// 新增：获取未整改隐患数据
const fetchHiddenDangerData = async () => {
  try {
    const res = await patrol_hidden_danger()

    // API返回格式：{ code: 200, msg: null, data: { total, list, pageNum, ... } }
    const body = res?.data

    if (body && Array.isArray(body.list) && body.list.length > 0) {
      // 转换API数据为组件需要的格式
      const transformedData = body.list.map((item: any) => {
        const overdueDays = calculateOverdueDays(item.findTime || '')

        return {
          id: item.id || Math.random(),
          code: String(item.id || '').padStart(3, '0'),
          n: Math.min(overdueDays, 5), // 限制最大值为5，对应图片编号1-5
          content: item.content || item.problemDescription || '未知隐患内容',
          level: item.level || '',
          address: item.address || '',
          findPerson: item.findPerson || '',
          findTime: item.findTime || '',
          inspectionRecord: item.inspectionRecord || '',
          inspectionItem: item.inspectionItem || '',
          problemDescription: item.problemDescription || '',
          areaType: item.areaType || '',
          planName: item.planName || '',
          enterpriseName: item.enterpriseName || '',
          rePerson: item.rePerson || '',
          reTime: item.reTime || '',
          reCondition: item.reCondition || '',
          overdueDays: overdueDays
        }
      })

      // 如果有数据则使用API数据，否则保持默认数据
      if (transformedData.length > 0) {
        warningData.value = shuffle(transformedData)
      }
    } else {
      // 无有效数据时不做变更
      console.warn('未整改隐患预警接口返回格式不符或无数据，未更新数据', res)
    }
  } catch (err) {
    console.error('获取未整改隐患预警失败', err)
  }
}

const total = 20
const warningData = ref(
  shuffle(
    Array.from({ length: total }, (_, i) => ({
      id: i + 1,
      code: (getRandomInt(1, 999) + '').padStart(3, '0'),
      n: getRandomInt(1, 5),
    })),
  ),
)

const rowHeight = 50
const visibleCount = 5
const scrollList = computed(() => [...warningData.value, ...warningData.value])
const listContainerRef = ref<HTMLElement | null>(null)
const scrollTimer = ref<any>(null)
const scrollTop = ref(0)
const transitionEnabled = ref(true)

const startScrolling = () => {
  stopScrolling()
  scrollTimer.value = setInterval(() => {
    scrollTop.value += rowHeight
    if (scrollTop.value >= warningData.value.length * rowHeight) {
      transitionEnabled.value = false
      scrollTop.value = 0
      setTimeout(() => {
        transitionEnabled.value = true
      }, 50)
    }
  }, 2000)
}

const stopScrolling = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value)
    scrollTimer.value = null
  }
}

onMounted(() => {
  // 初始加载隐患数据
  fetchHiddenDangerData()

  startScrolling()
  const container = listContainerRef.value
  if (container) {
    container.addEventListener('mouseenter', stopScrolling)
    container.addEventListener('mouseleave', startScrolling)
  }
})
onUnmounted(() => {
  stopScrolling()
  const container = listContainerRef.value
  if (container) {
    container.removeEventListener('mouseenter', stopScrolling)
    container.removeEventListener('mouseleave', startScrolling)
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.warning-list-outer {
  overflow: hidden;
  padding: 6px 0;
}
.warning-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.warning-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 0 24px;
  border-radius: 8px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  color: #fff;
  font-family: 'Noto Sans SC', sans-serif;
  font-size: 14px;
  font-weight: 500;
}
.warning-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.warning-overdue {
  margin-left: 16px;
  font-size: 14px;
  font-weight: 400;
  white-space: nowrap;
}
</style>
