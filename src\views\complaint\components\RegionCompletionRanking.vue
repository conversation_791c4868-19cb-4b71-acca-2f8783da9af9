<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">区域办结率排行</div>
      <div class="header-dropdown">
        <div class="btn-group">
          <button class="period-btn" :class="{ active: sortOrder === 'desc' }" @click="handleSortChange('desc')">
            降序
          </button>
          <button class="period-btn" :class="{ active: sortOrder === 'asc' }" @click="handleSortChange('asc')">
            升序
          </button>
        </div>
      </div>
    </div>
    <div class="panel-content p-4">
      <div class="ranking-list">
        <div 
          v-for="(item, index) in sortedData" 
          :key="item.name"
          class="ranking-item"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <div class="area-name">{{ item.name }}</div>
          <div class="progress-wrapper">
            <div class="progress-track">
              <div 
                class="progress-bar" 
                :style="{ width: `${item.value}%` }"
              >
                <div class="progress-dot"></div>
              </div>
            </div>
          </div>
          <div class="value-label">{{ item.value }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { tsjsc_qybjlpm } from '@/common/api/complaint'

const sortOrder = ref<string>('desc')

interface RankingItem {
  name: string
  value: number
}

const rankingData = ref<RankingItem[]>([])

// 加载数据
const loadData = async (type: 'desc' | 'asc') => {
  try {
    const response = await tsjsc_qybjlpm({ type })
    if (response.success && response.data && response.data.length > 0) {
      rankingData.value = response.data.map(item => {
        // 处理百分比字符串，如 "50.00%" 或小数 0.5
        let ratioValue = 0
        if (typeof item.ratio === 'string') {
          // 移除百分号并转换为数字
          ratioValue = parseFloat(item.ratio.replace('%', ''))
        } else {
          // 如果是小数形式（如 0.5），转换为百分比
          ratioValue = item.ratio ? parseFloat(item.ratio.toString()) * 100 : 0
        }
        return {
          name: item.area || '',
          value: Math.round(ratioValue) // 四舍五入为整数百分比
        }
      })
    }
  } catch (error) {
    console.error('加载区域办结率排名数据失败:', error)
  }
}

const sortedData = computed(() => {
  const data = [...rankingData.value]
  if (sortOrder.value === 'desc') {
    // 降序：从高到低
    return data.sort((a, b) => b.value - a.value)
  } else {
    // 升序：从低到高
    return data.sort((a, b) => a.value - b.value)
  }
})

const handleSortChange = (order: string) => {
  sortOrder.value = order
  loadData(order as 'desc' | 'asc')
}

onMounted(() => {
  loadData('desc')
})
</script>

<style scoped lang="scss">
@import '@/styles/index.css';

.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}

.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.ranking-item {
  display: grid;
  grid-template-columns: 100px 1fr 60px;
  align-items: center;
  gap: 16px;
  opacity: 0;
  animation: fadeInUp 0.5s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.area-name {
  color: #fff;
  font-size: 14px;
  font-weight: 300;
  white-space: nowrap;
  text-align: left;
}

.progress-wrapper {
  position: relative;
  width: 100%;
}

.progress-track {
  position: relative;
  width: 100%;
  height: 10px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  overflow: visible;
}

.progress-bar {
  position: relative;
  height: 100%;
  background: linear-gradient(90deg, #B8860B 0%, #FFD700 50%, #FFC61A 100%);
  border-radius: 5px;
  transition: width 0.8s ease;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.4);
}

.progress-dot {
  position: absolute;
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background: transparent;
  border: 3px solid #FFD700;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.6);
  animation: pulse 2s ease-in-out infinite;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background: #FFD700;
    border-radius: 50%;
    box-shadow: 0 0 6px rgba(255, 215, 0, 0.8);
  }
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.6);
    transform: translateY(-50%) scale(1);
  }
  50% {
    box-shadow: 0 0 16px rgba(255, 215, 0, 0.9);
    transform: translateY(-50%) scale(1.05);
  }
}

.value-label {
  color: #fff;
  font-size: 16px;
  font-weight: 400;
  font-family: 'DINPro', sans-serif;
  text-align: right;
}

/* 滚动条样式 */
.ranking-list::-webkit-scrollbar {
  width: 4px;
}

.ranking-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.ranking-list::-webkit-scrollbar-thumb {
  background: rgba(102, 255, 255, 0.3);
  border-radius: 2px;
}

.ranking-list::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 255, 255, 0.5);
}
</style>
