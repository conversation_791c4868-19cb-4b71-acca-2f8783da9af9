<template>
  <div class="panel-container-col">
    <div class="panel-header">企业安全管理人员</div>
    <div class="p-4 panel-content">
      <!-- 固定表头 -->
      <Table class="w-full table-fixed">
        <colgroup>
          <col style="width: 30%" />
          <col style="width: 40%" />
          <col style="width: 30%" />
        </colgroup>
        <TableHeader>
          <TableRow class="border-[#99D5FF]/30 hover:bg-transparent">
            <TableHead class="font-bold text-white">姓名</TableHead>
            <TableHead class="font-bold text-white">资格类别</TableHead>
            <TableHead class="font-bold text-white">发证时间</TableHead>
          </TableRow>
        </TableHeader>
      </Table>
      
      <!-- 滚动内容区域 -->
      <div class="overflow-hidden" :style="{ height: `${8 * rowHeight}px` }" ref="tableContainerRef">
        <div
          :style="{
            transform: `translateY(-${scrollTop}px)`,
            transition: transitionEnabled ? 'transform 0.5s ease' : 'none',
          }"
        >
          <Table class="w-full table-fixed">
            <colgroup>
              <col style="width: 30%" />
              <col style="width: 40%" />
              <col style="width: 30%" />
            </colgroup>
            <TableBody>
              <TableRow
                v-for="(item, index) in sourceData"
                :key="index"
                class="border-[#99D5FF]/30 hover:bg-[#99D5FF]/30 cursor-pointer"
                :style="{ height: `${rowHeight}px` }"
                @click="handleShowDialog(item)"
              >
                <TableCell class="text-white">{{ item.name }}</TableCell>
                <TableCell class="text-white">{{ item.qualification_category }}</TableCell>
                <TableCell class="text-white">{{ item.issue_date }}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { getCorpSafetyManager } from '@/common/api/hazard'

const emit = defineEmits<{
  (e: 'click', value: any): void
}>()

const handleShowDialog = (record: any) => {
  emit('click', record)
}

const sourceData = ref<any[]>([])

const fetchCorpSafetyManager = async () => {
  try {
    const res = await getCorpSafetyManager({})
    const data = Array.isArray(res?.data) ? res.data : []
    console.log(data);
    sourceData.value = data
  } catch (e) {
    console.error('获取企业安全管理人员失败:', e)
  }
}

const tableContainerRef = ref<HTMLElement | null>(null)
const scrollTimer = ref<any>(null)
const scrollTop = ref(0)
const rowHeight = 40 // 每行高度为40px
const transitionEnabled = ref(true)
const currentIndex = ref(0)

const startScrolling = () => {
  // 如果数据少于等于8条，不需要滚动
  if (sourceData.value.length <= 8) {
    return
  }
  
  stopScrolling()
  scrollTimer.value = setInterval(() => {
    currentIndex.value++
    
    // 循环到最后一条时重置
    if (currentIndex.value >= sourceData.value.length) {
      currentIndex.value = 0
      transitionEnabled.value = false
      scrollTop.value = 0
      setTimeout(() => {
        transitionEnabled.value = true
        currentIndex.value = 1
        scrollTop.value = rowHeight
      }, 50)
    } else {
      scrollTop.value = currentIndex.value * rowHeight
    }
  }, 3000)
}

const stopScrolling = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value)
    scrollTimer.value = null
  }
}

onMounted(() => {
  fetchCorpSafetyManager().then(() => {
    startScrolling()
    const container = tableContainerRef.value
    if (container) {
      container.addEventListener('mouseenter', stopScrolling)
      container.addEventListener('mouseleave', startScrolling)
    }
  })
})

onUnmounted(() => {
  stopScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.removeEventListener('mouseenter', stopScrolling)
    container.removeEventListener('mouseleave', startScrolling)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';
</style>
