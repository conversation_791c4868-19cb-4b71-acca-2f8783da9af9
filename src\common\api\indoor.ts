import request from '@/utils/request'

// 请求体接口（根据实际字段调整）
export interface DeviceOverviewPayload {
  // ...根据接口实际需要添加字段
  [key: string]: any
}

// 响应数据接口（根据实际返回结构调整）
export interface DeviceOverviewResponse {
  code?: number
  message?: string
  data?: any
}
//入户安检-入户安检总览
export function rhajzl(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/rhaj/rhajzl/F016F7CB8BD71CEAF23581E9FC05097F', payload, {
    params: { applicationName: 'fxrq' },
  })
}

//入户安检-年度管道气入户安检率分析
export function ndgdqrhajlfx(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/rhaj/ndgdqrhajlfx/1CD6EEAC797D1A880B115CFEAFEBFC6B645FA1961DC5C423', payload, {
    params: { applicationName: 'fxrq' },
  })
}

//入户安检-当年到访不遇用户统计总数
export function dndfbuyhtjzs(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/rhaj/dndfbyyhzs/765305CCEAF62BF9E37ED9456481BDA0', payload, {
    params: { applicationName: 'fxrq' },
  })
}

//入户安检-当年到访不遇用户图表
export function dndfbyyhtb(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {        
    return request.post('/service-api/exposureAPIS/path/rhaj/dndfbyyhtb/765305CCEAF62BF9ADFB21A0DE745687', payload, {
        params: { applicationName: 'fxrq' },
    })
}

//入户安检-入户类型分布
export function rhlxfb(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {        
    return request.post('/service-api/exposureAPIS/path/rhaj/rhlxfb/2B3B886699EE7A087CA4052C3858B1D5', payload, {
        params: { applicationName: 'fxrq' },
    })
}

//入户安检-隐患整改分析图表
export function yhzgfxtb(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {        
    return request.post('/service-api/exposureAPIS/path/rhaj/yhzgfxtb/29954E99B2E0E1C92C9C742AC9CC7B0B', payload, {
        params: { applicationName: 'fxrq' },
    })
}

//入户安检-隐患整改分析统计总数
export function yhzgfxtjzs(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {        
    return request.post('/service-api/exposureAPIS/path/rhaj/yhzgfxzs/29954E99B2E0E1C9157BC28BF0B7A1AF', payload, {
        params: { applicationName: 'fxrq' },
    })
}

//入户安检-特殊用户管理数量统计
export function tsyhglsltj(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {        
    return request.post('/service-api/exposureAPIS/path/rhaj/tsyhglsltj/116535686D35D71AFBD146D5A6367B85', payload, {
        params: { applicationName: 'fxrq' },
    })
}

//入户安检-特殊用户管理列表
export function tsyhgllb(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {        
    return request.post('/service-api/exposureAPIS/path/rhaj/tsyhgllb/116535686D35D71AE30A6607DF2C48BC', payload, {
        params: { applicationName: 'fxrq' },
    })
}