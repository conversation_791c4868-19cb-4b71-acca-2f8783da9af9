import request from '@/utils/request'

/**
 * 通用响应结构
 */
export interface ApiResponse<T = any> {
  code?: number
  message?: string
  msg?: string
  success?: boolean
  pageInfo?: any
  data?: T | T[]
}

// 请求体接口（根据实际字段调整）
export interface DashboardPayload {
  [key: string]: any
}

// 行业概况数据结构
export interface IndustryOverviewData {
  valve_wells: number // 阀门井数量
  pipe_length: number // 管网长度
  bottle_users: number // 瓶装居民用户
  gate_stations: number // 门站数量
  pipe_companies: number // 管道燃气企业
  after_sale_sites: number // 售后服务网点
  bottle_companies: number // 瓶装燃气企业
  station_companies: number // 加气站企业
  user_device: number // 用户监测设备
  pipe_units: number // 管道单位用户
  reserve_stations: number // 储备站数量
  station_users: number // 移动加气用户
  practitioners: number // 从业人员数量
  pressure_stations: number // 调压站数量
  pipe_users: number // 管道居民用户
  bottle_units: number // 瓶装单位用户
}

// 入户安检数据结构
export interface HouseholdInspectionData {
  households_plan: number // 安检计划
  households_plan_finish: number // 完成计划
  patrol_troubles: number // 现存入户安检隐患
  patrol_plan_users: number // 安检用户
}

// 巡查巡检数据结构
export interface RoundInspectionData {
  patrol_plan: number // 巡检计划
  patrol_plan_finished: number // 完成计划
  warning: number // 风险预警
  patrol_troubles: number // 现存隐患
}

// 风险隐患监管-风险数据结构
export interface RiskData {
  second_level: number // 二级风险
  first_level: number // 一级风险
  third_level: number // 三级风险
  forth_level: number // 四级风险
}

// 风险隐患监管-隐患数据结构
export interface HazardData {
  un_resolve_trouble: number // 现有隐患
  resolve_trouble: number // 已解决隐患
}

// 风险隐患监管-现存风险隐患数据结构
export interface CurrentRiskHazardData {
  district: string // 区划
  trouble_count: number // 现存隐患
  risk_count: number // 现存风险
}

// 瓶装气状态数据结构
export interface BottledGasStatusData {
  stop_use: number // 停用
  other: number // 报废/流失/注销
  in_use: number // 在用
}

// 气瓶充装监管数据结构
export interface CylinderFillingData {
  filling_count: number // 充装数量
  time_period: string // 时间段
}

// 燃气管网数据结构
export interface PipelineNetworkData {
  low_pressure: number // 低压管网
  pipe_length: number // 管网总长
  undervoltage_device: number // 亏电设备
  high_pressure: number // 高压管网
  mid_pressure: number // 中压管网
  leak_alarm: number // 泄露报警
  offline_device: number // 离线设备
  large_age: number // 管龄>15年
  village_length: number // 农村管网
  pipe_count: number // 管段数量
  little_age: number // 管龄≤15年
  node_count: number // 节点数量
  town_length: number // 城镇管网
  detector: number // 管网燃气探测器
}

// 燃气企业点位数据结构
export interface GasCompanyLocationData {
  latitude: number // 纬度
  id: string // id
  type: string // 企业类型
  longitude: number // 经度
}

// 场站设施点位数据结构
export interface StationLocationData {
  latitude: number // 纬度
  id: string // id
  type: string // 场站类型
  longitude: number // 经度
}

// 管网点位数据结构
export interface PipelineLocationData {
  id: string // id
  type?: string // 管网类型
  point_location?: string // 经纬度
  enterprise?: string // 企业名称
}

// 风险区域点位数据结构
export interface RiskAreaLocationData {
  lat: number // 纬度
  id: string // id
  level: string // 风险等级
  lng: number // 经度
}

// 燃气企业详情数据结构
export interface GasCompanyDetailData {
  name: string // 企业名称
  representative: string // 企业负责人
  type: string // 企业类别
  contact: string // 负责人电话
  detailed_pos: string // 企业地址
}

// 场站详情数据结构
export interface StationDetailData {
  name: string // 场站名称
  representative: string // 场站负责人
  type: string // 场站类别
  contact: string // 负责人电话
  detailed_pos: string // 场站地址
}

// 阀门井详情数据结构
export interface ValveWellDetailData {
  name: string // 场站名称
  representative: string // 场站负责人
  type: string // 场站类别
  contact: string // 负责人电话
  address: string // 场站地址
}

// 管网详情数据结构
export interface PipelineDetailData {
  section_name: string // 管段名称
  enterprise: string // 企业
  pressure_type: string // 压力类型
  pipe_product: string // 管道产品
  earth_depth: string // 埋深
  build_date: string // 建设日期
  representative: string // 负责人
  contact: string // 联系电话
  section_one: string // 起点
  section_two: string // 终点
}

// 风险区域详情数据结构
export interface RiskAreaDetailData {
  area_name: string // 区域名称
  risk_level: string // 区域风险等级
  videos: string // 区域监控
  address: string // 地址
  supervisor_phone: string // 负责人电话
  supervisor: string // 监管负责人
}

/**
 * 行业概况18个指标
 */
export function getIndustryOverview(payload: DashboardPayload = {}): Promise<ApiResponse<IndustryOverviewData>> {
  return request.post('/service-api/exposureAPIS/path/fxrq/zhrq_hygk/A0E262B82E74EFB33ABDDC8B13B7D2C0', payload, {
    params: { applicationName: 'fxrq' },
  })
}

/**
 * 入户安检
 */
export function getHouseholdInspection(payload: DashboardPayload = {}): Promise<ApiResponse<HouseholdInspectionData>> {
  return request.post('/service-api/exposureAPIS/path/fxrq/zhrq_rhaj/A0E262B82E74EFB35BC823D9A21D5E6B', payload, {
    params: { applicationName: 'fxrq' },
  })
}

/**
 * 巡查巡检
 */
export function getRoundInspection(payload: DashboardPayload = {}): Promise<ApiResponse<RoundInspectionData>> {
  return request.post('/service-api/exposureAPIS/path/fxrq/zhrq_xcxj/A0E262B82E74EFB39426C6D371108B82', payload, {
    params: { applicationName: 'fxrq' },
  })
}

/**
 * 风险隐患监管-风险
 */
export function getRiskData(payload: DashboardPayload = {}): Promise<ApiResponse<RiskData>> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/zhrq_fxyhjgfx/A0E262B82E74EFB3569CB460AFA8023ACC1B904E84C9FD71',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 风险隐患监管-隐患
 */
export function getHazardData(payload: DashboardPayload = {}): Promise<ApiResponse<HazardData>> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/zhrq_fxyhjgyh/A0E262B82E74EFB3569CB460AFA8023A3691926B69A9DBF6',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 风险隐患监管-现存风险隐患
 */
export function getCurrentRiskHazard(payload: DashboardPayload = {}): Promise<ApiResponse<CurrentRiskHazardData[]>> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/zhrq_fxyhjgxcfxyh/A0E262B82E74EFB3569CB460AFA8023AAE35EB1859AE34F9',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 瓶装气状态
 */
export function getBottledGasStatus(payload: DashboardPayload = {}): Promise<ApiResponse<BottledGasStatusData>> {
  return request.post('/service-api/exposureAPIS/path/fxrq/zhrq_pzqzt/A0E262B82E74EFB32AD11754C91B9D54', payload, {
    params: { applicationName: 'fxrq' },
  })
}

/**
 * 气瓶充装监管-本周每日
 */
export function getCylinderFillingWeekly(payload: DashboardPayload = {}): Promise<ApiResponse<CylinderFillingData[]>> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/zhrq_qpczbzmr/A0E262B82E74EFB3915B6F9127AAD151F1030F3E7A7864B9',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 气瓶充装监管-本月每日
 */
export function getCylinderFillingMonthly(payload: DashboardPayload = {}): Promise<ApiResponse<CylinderFillingData[]>> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/zhrq_qpczbymr/A0E262B82E74EFB3B3091BF9613A6891F1030F3E7A7864B9',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 气瓶充装监管-本年每日
 */
export function getCylinderFillingYearly(payload: DashboardPayload = {}): Promise<ApiResponse<CylinderFillingData[]>> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/zhrq_qpczbnmr/A0E262B82E74EFB3E60EAB1136874050F1030F3E7A7864B9',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 燃气管网
 */
export function getPipelineNetwork(payload: DashboardPayload = {}): Promise<ApiResponse<PipelineNetworkData>> {
  return request.post('/service-api/exposureAPIS/path/fxrq/zhrq_gwzc/A0E262B82E74EFB3BAFF4B9DD4BEA21F', payload, {
    params: { applicationName: 'fxrq' },
  })
}

/**
 * 燃气企业点位列表
 */
export function getGasCompanyLocations(payload: DashboardPayload = {}): Promise<ApiResponse<GasCompanyLocationData[]>> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/zhrq_rqqydwlb/A0E262B82E74EFB3420A9DB467B4F16FC925146668FD9575',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 场站设施点位列表
 */
export function getStationLocations(payload: DashboardPayload = {}): Promise<ApiResponse<StationLocationData[]>> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/zhrq_czssdwlb/A0E262B82E74EFB3DC0B8DB82ECFAE8AC925146668FD9575',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 管网点位列表
 */
export function getPipelineLocations(payload: DashboardPayload = {}): Promise<ApiResponse<PipelineLocationData[]>> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/zhrq_gwdwlb/A0E262B82E74EFB3100A0D965E73E4854DB02F93383C0102',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 风险区域点位列表
 */
export function getRiskAreaLocations(payload: DashboardPayload = {}): Promise<ApiResponse<RiskAreaLocationData[]>> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/zhrq_fxqydwlb/A0E262B82E74EFB3092965EF482197EAC925146668FD9575',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 燃气企业详情
 */
export function getGasCompanyDetail(
  payload: DashboardPayload & { id: string },
): Promise<ApiResponse<GasCompanyDetailData>> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/zhrq_rqqyxq/A0E262B82E74EFB37F9439709C3AE3094DB02F93383C0102',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 场站点位详情(不包括阀门井)
 */
export function getStationDetail(payload: DashboardPayload & { id: string }): Promise<ApiResponse<StationDetailData>> {
  return request.post('/service-api/exposureAPIS/path/fxrq/zhrq_czxq/A0E262B82E74EFB3E9E6139E22AA4254', payload, {
    params: { applicationName: 'fxrq' },
  })
}

/**
 * 阀门井点位详情
 */
export function getValveWellDetail(
  payload: DashboardPayload & { id: string },
): Promise<ApiResponse<ValveWellDetailData>> {
  return request.post('/service-api/exposureAPIS/path/fxrq/zhrq_fmjxq/A0E262B82E74EFB336F5CC46B8B3E898', payload, {
    params: { applicationName: 'fxrq' },
  })
}

/**
 * 管网点位详情
 */
export function getPipelineDetail(
  payload: DashboardPayload & { id: string },
): Promise<ApiResponse<PipelineDetailData>> {
  return request.post('/service-api/exposureAPIS/path/fxrq/zhrq_gwxq/A0E262B82E74EFB32CA6F84056AC27DD', payload, {
    params: { applicationName: 'fxrq' },
  })
}

/**
 * 风险区域点位详情
 */
export function getRiskAreaDetail(
  payload: DashboardPayload & { id: string },
): Promise<ApiResponse<RiskAreaDetailData>> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/zhrq_fxqyxq/A0E262B82E74EFB3C0D389D2B38D34D14DB02F93383C0102',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
