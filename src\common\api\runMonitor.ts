import request from '@/utils/request'

// 请求体接口（根据实际字段调整）
export interface DeviceOverviewPayload {
  // ...根据接口实际需要添加字段
  [key: string]: any
}

// 响应数据接口（根据实际返回结构调整）
export interface DeviceOverviewResponse {
  code?: number
  message?: string
  data?: any
}
// 设备监控-设备概况
export function sbjc_sbgk(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/yxjc/sbjc_sbgk/435A00F0B249A611FEC77CA1CE221A02', payload, {
    params: { applicationName: 'fxrq' },
  })
}
// 设备监控-用户设备告警
export function sbjc_yhsbgj(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yxjc/sbjc_yhsbgj/435A00F0B249A6117F022035484615834DB02F93383C0102',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
// 设备监控-用户设备告警-详情
export function sbjc_yhsbgj_xq(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yxjc/sbjc_yhsbgj_xq/435A00F0B249A6117F02203548461583DF07A3ADF2EF2C7C',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
// 设备监控-站点概况
export function zdjc_zdgk(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/yxjc/zdjc_zdgk/3CFB60A5DE016CA258C61CF419FC47A8', payload, {
    params: { applicationName: 'fxrq' },
  })
}
// 设备监控-场站监控画面
export function zdjc_czjkhm(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yxjc/zdjc_czjkhm/3CFB60A5DE016CA29CA45931E41E8EE84DB02F93383C0102',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
// 设备监控-管道泄漏告警
export function collector_alarm_list(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/ditu/collector_alarm_list/C0E4FB4B02A4062D4B791EFE30CA7573F2ED920C3A5F744DC3D65CC180C68427',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
// 设备监控-阀门压力井监测
export function czsb_fmjyljc(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yxjc/czsb_fmjyljc/9AEE85DEBD75CF936D357FAFE5D2E2968B101CCA2E4435F1',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
// 设备监控-管道气站点设备监测
export function czsb_gdqzdsbjc(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yxjc/czsb_gdqzdsbjc/9AEE85DEBD75CF93F6D56534169BC31AD8752B1A6B424074',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

// 设备监控-管道气站点设备监测-图表
export function czsb_gdqzdsbjc_xq(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yxjc/czsb_gdqzdsbjc_xq/9AEE85DEBD75CF93F6D56534169BC31A4E9F86106576D8E3',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
// 设备监控-阀门压力井监测-图表
export function czsb_fmjyljc_xq(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yxjc/czsb_fmjyljc_xq/9AEE85DEBD75CF936D357FAFE5D2E2968991D59E3A32B80A',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
//设备监控-气瓶充装统计-7天
export function qyyx_qpcztj_7day(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yxjc/qyyx_qpcztj_7day/4417C20A828AE81FA5F7363FDE250D8487F8965B4193CFC2',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
//设备监控-气瓶充装统计-一月
export function qyyx_qpcztj_4week(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yxjc/qyyx_qpcztj_4week/4417C20A828AE81FA5F7363FDE250D848383CEE7E4283CB7',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
//设备监控-企业运行
export function qyyx_qytyzyljc(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yxjc/qyyx_qytyzyljc/4417C20A828AE81F7F991D77DAF870AEDF263A4753C3E5EE',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
//中心地图接口-用户设备
export function jcsb_yhsb(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/ditu/jcsb_yhsb/9612E51DD829EBEA7154AA7855882411', payload, {
    params: { applicationName: 'fxrq' },
  })
}
//中心地图接口-用户设备详情
export function jcsb_yhsb_tc(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/ditu/jcsb_yhsb_tc/9612E51DD829EBEA3DDAB221C51270958B101CCA2E4435F1',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
//中心地图接口-管网设备
export function jcsb_gwsb(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/ditu/jcsb_gwsb/9612E51DD829EBEA8ABAF2ACF0E80A21', payload, {
    params: { applicationName: 'fxrq' },
  })
}
//中心地图接口-管网设备详情
export function jcsb_gwsb_tc(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/ditu/jcsb_gwsb_tc/9612E51DD829EBEAB7F0B7173FF6FB448B101CCA2E4435F1',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
//中心地图接口-用户设备告警
export function sbgj_yhsbgj(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/ditu/sbgj_yhsbgj/FE7E6E1E618CA193CB195066B62D01964DB02F93383C0102',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
//中心地图接口-用户设备告警弹窗
export function sbgj_yhsbgj_tc(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/ditu/sbgj_yhsbgj_tc/FE7E6E1E618CA193CB195066B62D0196E767395744053B1F',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
//中心地图接口-管网设备告警
export function sbgj_gwsbgj(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/ditu/sbgj_gwsbgj/FE7E6E1E618CA193E60A5F189400E4F54DB02F93383C0102',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

//中心地图接口-管网设备告警弹窗
export function sbgj_gwsbgj_tc(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/ditu/sbgj_gwsbgj_tc/FE7E6E1E618CA193E60A5F189400E4F5E767395744053B1F',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}
