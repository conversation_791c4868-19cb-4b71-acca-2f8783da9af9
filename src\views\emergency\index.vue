<template>
  <div class="dashboard-layout">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <Tabs default-value="tab1">
        <TabsList
          class="flex flex-col h-auto absolute top-0 right-0 translate-x-[100%] border-0 bg-[#409fff]/40 backdrop-blur-sm"
        >
          <TabsTrigger
            value="tab1"
            class="vertical-tab h-auto border-0 bg-none text-[#66ffff] cursor-pointer data-[state=active]:bg-[#409fff]/70 data-[state=active]:text-white"
          >
            应急资源统计
          </TabsTrigger>
          <TabsTrigger
            value="tab2"
            class="vertical-tab h-auto border-0 bg-none text-[#66ffff] cursor-pointer data-[state=active]:bg-[#409fff]/70 data-[state=active]:text-white"
          >
            应急动态监控
          </TabsTrigger>
        </TabsList>
        <TabsContent value="tab1">
          <div class="flex flex-col gap-6">
            <SourceIndicators />
            <SourceEntry @click="onShowSourceEntryDialog" />
          </div>
        </TabsContent>
        <TabsContent value="tab2">
          <div class="flex flex-col gap-6">
            <OnlineRate />
            <OnlineVideo @click="onShowOnlineVideoDialog" />
            <PipelineLeakTable @click="onShowPipelineLeakDialog" />
          </div>
        </TabsContent>
      </Tabs>
    </div>

    <!-- 中央地图区域 -->
    <div class="center-panel-left">
      <LayersTool v-model="layersData" />
    </div>
    <div class="center-panel-right">
      <MapTool @click="onShowLeakModelDialog" />
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <Tabs default-value="tab1">
        <TabsList class="flex flex-col h-auto absolute top-0 right-[744px] border-0 bg-[#409fff]/40 backdrop-blur-sm">
          <TabsTrigger
            value="tab1"
            class="vertical-tab h-auto border-0 bg-none text-[#66ffff] cursor-pointer data-[state=active]:bg-[#409fff]/70 data-[state=active]:text-white"
          >
            应急预案与演练
          </TabsTrigger>
          <TabsTrigger
            value="tab2"
            class="vertical-tab h-auto border-0 bg-none text-[#66ffff] cursor-pointer data-[state=active]:bg-[#409fff]/70 data-[state=active]:text-white"
          >
            应急事故统计
          </TabsTrigger>
        </TabsList>
        <TabsContent value="tab1">
          <div class="flex flex-col gap-6">
            <PlanPie />
            <DrillsTop />
          </div>
        </TabsContent>
        <TabsContent value="tab2">
          <div class="flex flex-col gap-6">
            <PipelineLeakLine />
            <AccidentTable />
          </div>
        </TabsContent>
      </Tabs>
    </div>
    <!-- 资源录入 -->
    <SourceEntryDialog :open="showSourceEntryDialog" :data="sourceEntryInfo" @close="showSourceEntryDialog = false" />
    <!-- 泄漏预演 -->
    <LeakModelDialog :open="showLeakModelDialog" @close="showLeakModelDialog = false" />
    <!-- 管网泄漏 -->
    <PipelineLeakDialog
      :open="showPipelineLeakDialog"
      :data="pipelineLeakInfo"
      @close="showPipelineLeakDialog = false"
    />
    <!-- 在线视频 -->
    <VideoDialog :open="showVideoDialog" @close="showVideoDialog = false" :data="videoInfo" />

    <!-- UE弹窗 -->
    <EmergencyResourceDialog :open="resourceInfoOpen" @close="resourceInfoOpen = false" />
    <EmergencyEventsDialog :open="eventInfoOpen" @close="eventInfoOpen = false" />
    <StationDialog :open="stationInfoOpen" @close="stationInfoOpen = false" />
    <PipelineDialog :open="pipelineInfoOpen" @close="pipelineInfoOpen = false" />
  </div>
</template>

<script setup lang="ts">
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { toast } from 'vue-sonner'
import SourceIndicators from './components/SourceIndicators.vue'
import SourceEntry from './components/SourceEntry.vue'
import OnlineRate from './components/OnlineRate.vue'
import OnlineVideo from './components/OnlineVideo.vue'
import PipelineLeakTable from './components/PipelineLeakTable.vue'
import PipelineLeakLine from './components/PipelineLeakLine.vue'
import PlanPie from './components/PlanPie.vue'
import DrillsTop from './components/DrillsTop.vue'
import AccidentTable from './components/AccidentTable.vue'
import MapTool from './components/MapTool.vue'
import LeakModelDialog from './LeakModelDialog.vue'
import SourceEntryDialog from './SourceEntryDialog.vue'
import PipelineLeakDialog from './PipelineLeakDialog.vue'
import VideoDialog from './VideoDialog.vue'
import LayersTool from '@/components/LayersTool.vue'
import EmergencyResourceDialog from '@/components/ue-dialog/EmergencyResourceDialog.vue'
import EmergencyEventsDialog from '@/components/ue-dialog/EmergencyEventsDialog.vue'
import StationDialog from '@/components/ue-dialog/StationDialog.vue'
import PipelineDialog from '@/components/ue-dialog/PipelineDialog.vue'
import { layerData } from './layerData'

const showLeakModelDialog = ref<boolean>(false)
const showSourceEntryDialog = ref<boolean>(false)
const showPipelineLeakDialog = ref<boolean>(false)
const showVideoDialog = ref<boolean>(false)

const sourceEntryInfo = ref<any>({})
const pipelineLeakInfo = ref<any>({})
const videoInfo = ref<any>({
  id: '1',
  title: '--',
  address: '--',
  url: '',
})

const resourceInfoOpen = ref(false)
// const industryInfoData: Ref<IndustryInfo | null> = ref(null)
const stationInfoOpen = ref(false)
// const stationInfoData: Ref<StationInfo | null> = ref(null)
const pipelineInfoOpen = ref(false)
// const pipelineInfoData: Ref<PipelineInfo | null> = ref(null)
const eventInfoOpen = ref(false)
// const areaInfoData: Ref<AreaInfo | null> = ref(null)

// 图层数据
const layersData = ref(layerData)

// 全局加载状态
const globalLoading = ref(false)

// 数据刷新状态
const dataRefreshStatus = ref({
  emergencyResources: false,
  resourceEntries: false,
  planLevels: false,
  drillRankings: false,
  pipelineLeaks: false,
  leakTrend: false,
  accidents: false,
})

// 全局数据刷新函数
const refreshAllData = async () => {
  try {
    globalLoading.value = true
    toast.success('开始刷新应急管理数据...')

    // 这里可以触发各个组件的数据刷新
    // 由于各组件已经在mounted时自动获取数据，这里主要用于手动刷新

    // 可以通过事件总线或者provide/inject来通知各组件刷新数据
    // 或者通过ref获取组件实例来调用刷新方法

    setTimeout(() => {
      toast.success('应急管理数据刷新完成')
    }, 2000)
  } catch (error) {
    console.error('刷新数据失败:', error)
    toast.error('刷新应急管理数据失败')
  } finally {
    globalLoading.value = false
  }
}

// 监听layersData的变化
watch(layersData, newLayers => {
  console.log('Layers data updated:', newLayers)
  // 可以在这里处理图层数据的变化
})

const onShowLeakModelDialog = (value: boolean) => {
  console.log('Dialog visibility:', value)
  // 可以在这里处理弹窗显示逻辑
  showLeakModelDialog.value = true
}
const onShowSourceEntryDialog = (record: any) => {
  sourceEntryInfo.value = record
  showSourceEntryDialog.value = true
  console.log('Source entry record:', record)
}
const onShowPipelineLeakDialog = (record: any) => {
  console.log('Pipeline leak record:', record)
  pipelineLeakInfo.value = record
  showPipelineLeakDialog.value = true
}
const onShowOnlineVideoDialog = (video: any) => {
  console.log('Online video clicked:', video)
  videoInfo.value = video
  // 可以在这里处理弹窗显示逻辑
  showVideoDialog.value = true
}

// 交互处理

onMounted(() => {
  // todo
})

onUnmounted(() => {
  // 清除所有图层
})
</script>

<style scoped>
.dashboard-layout {
  position: absolute;
  top: 96px;
  left: 24px;
  right: 24px;
  height: calc(100% - 96px);
  overflow: hidden;
}

.left-panel,
.right-panel {
  position: absolute;
  top: 0;
  width: 744px;
}
.left-panel {
  left: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.right-panel {
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.center-panel-left {
  position: absolute;
  top: 0;
  left: 788px;
  z-index: 10;
}

.center-panel-right {
  position: absolute;
  top: 0;
  right: 788px;
  z-index: 10;
}

/* Tabs 样式重写 */
:deep(.vertical-tab) {
  writing-mode: vertical-lr;
  text-orientation: upright;
  padding: 0.75rem 0.25rem;
  line-height: 1.5;
  letter-spacing: 0.1em;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .dashboard-content {
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    padding: 20px;
    padding-top: 120px;
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 280px 1fr 280px;
    gap: 16px;
    padding: 16px;
    padding-top: 120px;
  }
}
</style>
