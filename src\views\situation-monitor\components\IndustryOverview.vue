<template>
  <div class="panel-container">
    <div class="panel-header">企业概况</div>
    <div class="industry-grid">
      <div v-for="item in industryData" :key="item.id" class="industry-card">
        <!-- 数值区域 -->
        <div class="card-value-section">
          <div class="value-number">{{ item.value }}</div>
          <div class="value-unit">{{ item.unit }}</div>
        </div>

        <!-- 图标和标题区域 -->
        <div class="card-content-section">
          <div class="icon-container">
            <!-- 背景装饰星形 -->
            <img class="star-bg-large" src="/src/assets/industry-icons/Star2.svg" alt="" />
            <img class="star-bg-small" src="/src/assets/industry-icons/Star3.svg" alt="" />
            <div class="main-icon">
              <img :src="item.icon" :alt="item.name" />
            </div>
          </div>
          <div class="card-title">{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { qygk } from '@/common/api/situationMonitor'

type IndustryItem = {
  id: number
  name: string
  value: number | string
  unit: string
  icon: string
}

// 初始占位数据（移除原有固定 mock 数值）
const industryData = ref<IndustryItem[]>([
  {
    id: 1,
    name: '企业总量',
    value: '--',
    unit: '家',
    icon: '/src/assets/industry-icons/enterprise-total.svg',
  },
  {
    id: 2,
    name: '管道燃气企业',
    value: '--',
    unit: '家',
    icon: '/src/assets/industry-icons/pipeline-gas.svg',
  },
  {
    id: 3,
    name: '瓶装燃气企业',
    value: '--',
    unit: '家',
    icon: '/src/assets/industry-icons/bottled-gas.svg',
  },
  {
    id: 4,
    name: '加气站企业',
    value: '--',
    unit: '家',
    icon: '/src/assets/industry-icons/gas-station.svg',
  },
])

onMounted(async () => {
  try {
    const res = await qygk()
    console.log('qygk response:', res)
    const payload = res?.data ?? {}

    // 获取后端数组：支持 payload 为数组 或 payload.data 为数组 的两种情况
    const arr: Array<{ num?: number; type?: string }> = Array.isArray(payload.data)
      ? payload.data
      : Array.isArray(payload)
        ? payload
        : []

    // 目标字段初始化
    const map: Record<string, number | undefined> = {
      total: undefined,
      enter_type_bottle: undefined,
      enter_type_pipeline: undefined,
      enter_type_station: undefined,
    }

    const unlabeled: number[] = []

    // 填充已标注 type 的项，未标注的收集到 unlabeled
    arr.forEach(it => {
      const n = typeof it.num === 'number' ? it.num : undefined
      const t = it.type
      if (t && Object.prototype.hasOwnProperty.call(map, t)) {
        map[t] = n
      } else if (typeof n === 'number') {
        unlabeled.push(n)
      }
    })

    // 若有未标注数据，按优先级依次补到仍未填的目标字段（避免丢失数据）
    const fillOrder = ['total', 'enter_type_pipeline', 'enter_type_bottle', 'enter_type_station']
    for (const key of fillOrder) {
      if ((map as any)[key] === undefined && unlabeled.length > 0) {
        ;(map as any)[key] = unlabeled.shift()
      }
    }

    // 若 total 仍然缺失，则尝试用其它三项求和作为回退
    if (map.total === undefined) {
      const sum = [map.enter_type_pipeline ?? 0, map.enter_type_bottle ?? 0, map.enter_type_station ?? 0].reduce(
        (s, v) => s + (typeof v === 'number' ? v : 0),
        0,
      )
      map.total = sum || undefined
    }

    console.log('parsed industry map:', map)

    // 更新视图数据，缺失值显示为 '--'
    industryData.value = [
      {
        id: 1,
        name: '企业总量',
        value: typeof map.total === 'number' ? map.total : '--',
        unit: '家',
        icon: '/src/assets/industry-icons/enterprise-total.svg',
      },
      {
        id: 2,
        name: '管道燃气企业',
        value: typeof map.enter_type_pipeline === 'number' ? map.enter_type_pipeline : '--',
        unit: '家',
        icon: '/src/assets/industry-icons/pipeline-gas.svg',
      },
      {
        id: 3,
        name: '瓶装燃气企业',
        value: typeof map.enter_type_bottle === 'number' ? map.enter_type_bottle : '--',
        unit: '家',
        icon: '/src/assets/industry-icons/bottled-gas.svg',
      },
      {
        id: 4,
        name: '加气站企业',
        value: typeof map.enter_type_station === 'number' ? map.enter_type_station : '--',
        unit: '家',
        icon: '/src/assets/industry-icons/gas-station.svg',
      },
    ]
  } catch (err) {
    console.error('qygk error:', err)
  }
})
</script>
<style lang="scss" scoped>
@import '@/styles/index.css';

.industry-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 20px;
}

.industry-card {
  width: 348px;
  height: 104px;
  background: linear-gradient(
    270deg,
    rgba(64, 159, 255, 0) 0%,
    rgba(64, 159, 255, 0.15) 50%,
    rgba(64, 159, 255, 0) 100%
  );
  border: 1px solid;
  border-image: linear-gradient(0deg, rgba(64, 159, 255, 0) 0%, rgba(64, 159, 255, 0.6) 50%, rgba(64, 159, 255, 0) 100%)
    1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  overflow: hidden;
}

.card-value-section {
  position: absolute;
  top: 36px;
  right: 24px;
  width: 45px;
  height: 32px;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;

  .value-number {
    flex-shrink: 0;
    width: 29px;
    height: 32px;
    white-space: nowrap;
    color: #66ffff;
    font-family: 'Noto Sans SC', sans-serif;
    font-size: 24px;
    line-height: 32px;
    text-align: right;
    font-weight: bold;
  }

  .value-unit {
    flex-shrink: 0;
    width: 16px;
    height: 24px;
    white-space: nowrap;
    color: #ffffff;
    font-family: 'Noto Sans SC', sans-serif;
    font-size: 16px;
    line-height: 24px;
    text-align: right;
  }
}

.card-content-section {
  position: absolute;
  top: 20px;
  left: 24px;
  width: 144px;
  height: 64px;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  column-gap: 16px;

  .icon-container {
    flex-shrink: 0;
    width: 64px;
    height: 64px;
    position: relative;
    overflow: hidden;

    .star-bg-large {
      position: absolute;
      top: 0px;
      left: 4px;
      width: 56px;
      height: 64px;
      z-index: 1;
    }

    .star-bg-small {
      position: absolute;
      top: 8px;
      left: 12px;
      width: 40px;
      height: 48px;
      z-index: 2;
    }

    .main-icon {
      position: absolute;
      top: 20px;
      left: 20px;
      width: 24px;
      height: 24px;
      z-index: 3;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        filter: drop-shadow(0 0 2.4px rgba(19, 96, 134, 0.6));
      }
    }
  }

  .card-title {
    flex-shrink: 0;
    width: 64px;
    height: 24px;
    white-space: nowrap;
    color: #ffffff;
    font-family: 'Noto Sans SC', sans-serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
  }
}
</style>
