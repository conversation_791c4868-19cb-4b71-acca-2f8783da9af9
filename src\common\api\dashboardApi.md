#### API列表

行业概况18个指标GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_hygk/A0E262B82E74EFB33ABDDC8B13B7D2C0?applicationName=fxrq 出参: { "valve_wells": 阀门井数量, "pipe_length": 管网长度, "bottle_users": 瓶装居民用户, "gate_stations": 门站数量, "pipe_companies": 管道燃气企业, "after_sale_sites": 售后服务网点, "bottle_companies": 瓶装燃气企业, "station_companies": 加气站企业, "user_device": 用户监测设备, "pipe_units": 管道单位用户, "reserve_stations": 储备站数量, "station_users": 移动加气用户, "practitioners": 从业人员数量, "pressure_stations": 调压站数量, "pipe_users": 管道居民用户, "bottle_units": 瓶装单位用户 }

入户安检GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_rhaj/A0E262B82E74EFB35BC823D9A21D5E6B?applicationName=fxrq 出参:{ "households_plan": 安检计划, "households_plan_finish": 完成计划, "patrol_troubles": 现存入户安检隐患, "patrol_plan_users": 安检用户 }

巡查巡检GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_xcxj/A0E262B82E74EFB39426C6D371108B82?applicationName=fxrq 出参:{ "patrol_plan": 巡检计划, "patrol_plan_finished": 完成计划, "warning": 风险预警, "patrol_troubles": 现存隐患 }

风险隐患监管-风险GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_fxyhjgfx/A0E262B82E74EFB3569CB460AFA8023ACC1B904E84C9FD71?applicationName=fxrq 出参: { "second_level": 二级风险, "first_level": 一级风险, "third_level": 三级风险, "forth_level": 四级风险 }

风险隐患监管-隐患GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_fxyhjgyh/A0E262B82E74EFB3569CB460AFA8023A3691926B69A9DBF6?applicationName=fxrq 出参: { "un_resolve_trouble": 现有隐患, "resolve_trouble":已解决隐患 }

风险隐患监管-现存风险隐患GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_fxyhjgxcfxyh/A0E262B82E74EFB3569CB460AFA8023AAE35EB1859AE34F9?applicationName=fxrq 出参: [{ "district":区划, "trouble_count": 现存隐患, "risk_count": 现存风险 }]

瓶装气状态GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_pzqzt/A0E262B82E74EFB32AD11754C91B9D54?applicationName=fxrq 出参: { "stop_use": 停用, "other": 报废/流失/注销, "in_use": 在用 }

气瓶充装-本周每日GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_qpczbzmr/A0E262B82E74EFB3915B6F9127AAD151F1030F3E7A7864B9?applicationName=fxrq 出参: [{ "date": 日期, "count": 数量 }]

气瓶充装-本月每日GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_qpczbymr/A0E262B82E74EFB3B3091BF9613A6891F1030F3E7A7864B9?applicationName=fxrq 出参: [{ "date": 日期, "count": 数量 }]

气瓶充装-本年每日GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_qpczbnmr/A0E262B82E74EFB3E60EAB1136874050F1030F3E7A7864B9?applicationName=fxrq 出参: [{ "date": 日期, "count": 数量 }]

燃气企业点位列表GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_rqqydwlb/A0E262B82E74EFB3420A9DB467B4F16FC925146668FD9575?applicationName=fxrq 出参: [{ "latitude": 纬度, "id": id, "type": 企业类型, "longitude": 经度 }]

场站设施点位列表GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_czssdwlb/A0E262B82E74EFB3DC0B8DB82ECFAE8AC925146668FD9575?applicationName=fxrq 出参: [{ "latitude": 纬度, "id": id, "type": 场站类型, "longitude": 经度 }]

管网点位列表GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_gwdwlb/A0E262B82E74EFB3100A0D965E73E4854DB02F93383C0102?applicationName=fxrq 出参: [{ "id": id, "type": 管网类型, "point_location": 经纬度 }]

风险区域点位列表GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_fxqydwlb/A0E262B82E74EFB3092965EF482197EAC925146668FD9575?applicationName=fxrq 出参: [{ "lat": 纬度, "id": id, "level": 风险等级, "lng": 经度 }]

燃气企业点位详情GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_rqqyxq/A0E262B82E74EFB37F9439709C3AE3094DB02F93383C0102?applicationName=fxrq params: {"id":"1"} 出参: { "name": "特许经营企业", "representative": "企业负责人", "contact": "负责人电话", "high_pressure": 高压管网, "mid_pressure": 中压管网, "low_pressure": 低压官网, "bottle_users": 瓶装气用户, "first_level": 一级风险点, "forth_level": 四级风险点, "total_len": 老旧管网改造, "patrol_hidden_danger": 入户安检隐患, "monitor_unit": "监管单位", "monitor_person": "监管单位联系人", "monitor_contact": "联系电话", "in_use_bottle": 0, "third_level": 三级风险点, "total_troubles": 巡检发现隐患, "rectification_troubles": 已解决隐患, "gas_station": 加气站, "valve_wells": 阀门井, "patrol_task": 巡检任务, "finished_len": 已改造管网, "total_patrol_users": 入户安检, "gate_stations": 门站, "after_sale_sites": 售后服务站, "patrol_task_finished": 已完成巡检任务, "user_device": 用户监测设备, "second_level": 二级风险点, "reserve_stations": 储备站, "pressure_stations": 调压站, "patroled_users": 已入户安检, "not_rectified_troubles": 现有隐患, "pipe_users": 管道气用户 }

场站点位详情(不包括阀门井) GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_czxq/A0E262B82E74EFB3E9E6139E22AA4254?applicationName=fxrq params: {"id":"1"} 出参: { "name": "场站名称", "representative": "场站负责人", "type": "场站类别", "contact": 负责人电话, "detailed_pos": 场站地址 }

阀门井点位详情GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_fmjxq/A0E262B82E74EFB336F5CC46B8B3E898?applicationName=fxrq params: {"id":"1"} 出参: { "name": "场站名称", "representative": "场站负责人", "type": "场站类别", "contact": 负责人电话, "address": 场站地址 }

管网点位详情GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_gwxq/A0E262B82E74EFB32CA6F84056AC27DD?applicationName=fxrq params: {"id":"1"} 出参: { "section_name": "场站名称", "enterprise": "场站负责人", "pressure_type": "场站类别", "pipe_product": 负责人电话, "earth_depth": 场站地址, "build_date": 场站地址, "representative": 场站地址, "contact": 场站地址, "section_one": 场站地址, "section_two": 场站地址 }

风险区域点位详情GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_fxqyxq/A0E262B82E74EFB3C0D389D2B38D34D14DB02F93383C0102?applicationName=fxrq params: {"id":"1"} 出参: { "area_name": "区域名称", "risk_level": "区域风险等级", "videos": 区域监控, "address": "地址", "supervisor_phone": "负责人电话", "supervisor": "监管负责人" }

燃气管网GET https://datacenterdev.city189.cn/service-api/exposureAPIS/path/fxrq/zhrq_gwzc/A0E262B82E74EFB3BAFF4B9DD4BEA21F?applicationName=fxrq 出参: { "low_pressure": "低压管网", "pipe_length": "管网总长", "undervoltage_device": "亏电设备", "high_pressure": "高压管网", "mid_pressure": "中压管网", "leak_alarm": "泄露报警", "offline_device": "离线设备", "large_age": "管龄>15年", "village_length": "农村管网", "pipe_count": "管段数量", "little_age": "管龄≤15年", "node_count": "节点数量", "town_length": "城镇管网", "detector": "管网燃气探测器" }
