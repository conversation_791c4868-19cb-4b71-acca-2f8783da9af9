<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">企业安全五维评估</div>
      <div class="header-dropdown">
        <Select v-model:model-value="selectedOpt">
          <SelectTrigger class="text-sm dropdown-btn" size="sm">
            <SelectValue placeholder="选择区域" />
          </SelectTrigger>
          <SelectContent class="text-[#99D5FF]">
            <SelectGroup>
              <!-- <SelectLabel>Fruits</SelectLabel> -->
              <SelectItem value="area1">区域一</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
    <div class="flex p-4 panel-content">
      <div ref="chartRef" class="w-[450px] h-full"></div>
      <div class="w-[240px]">
        <div class="text-base text-center text-[#47EBEB]">评估参数</div>
        <ul>
          <li
            class="flex justify-between leading-8 text-sm border-b-1 border-[#1AB2ff]/60"
            v-for="item in list"
            :key="item.id"
          >
            <p>{{ item.name }}</p>
            <p>{{ item.value }}</p>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  // SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import * as echarts from 'echarts'
import { getSafetyAssessmentFiveDimensional } from '@/common/api/safetyAssessment'

const selectedOpt = ref<string>('龙王庙镇/西木提村')
const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

const list = ref<{ name: string; value: number; id: number }[]>([])

const buildOption = () => ({
  color: ['#FF791A'],
  radar: {
    indicator: list.value.map(item => ({ name: item.name, max: 100 })),
    axisName: {
      show: true,
      color: '#fff',
    },
    axisLine: {
      lineStyle: {
        color: '#2A637D',
      },
    },
    splitLine: {
      lineStyle: {
        color: '#2A637D',
      },
    },
    splitArea: {
      show: false,
    },
  },
  series: [
    {
      name: '',
      type: 'radar',
      data: [
        {
          value: list.value.map(item => item.value),
          areaStyle: {
            color: 'rgba(255, 121, 26, 0.15)',
          },
        },
      ],
    },
  ],
})

const fetchFiveDimensional = async () => {
  try {
    const areaParam = (typeof selectedOpt.value === 'string' && selectedOpt.value.trim()) ? selectedOpt.value : '龙王庙镇/西木提村'
      const res: any = await getSafetyAssessmentFiveDimensional({ area: areaParam })
    let items: any[] = []
    if (res && Array.isArray(res.data)) {
      items = res.data
    } else if (res && res.data && Array.isArray(res.data)) {
      items = res.data
    } else if (Array.isArray(res)) {
      items = res
    } else if (res && typeof res === 'object') {
      items = res.data ? [res.data] : [res]
    }
    const mapped = items.slice(0, 5).map((it: any, idx: number) => ({
      name: it.modelName ?? `维度${idx + 1}`,
      value: Number(it.averageScore ?? 0),
      id: idx + 1,
    }))
    if (mapped.length) {
      list.value = mapped
      if (!chart && chartRef.value) {
        initchart()
      } else {
        chart?.setOption(buildOption(), true)
      }
    }
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error('获取五维评估失败:', e)
  }
}

const initchart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(buildOption())
  // startHighlightAnimation()
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  // 先拉取数据，避免图表初始化异常导致未发起请求
  fetchFiveDimensional()
  // 再初始化图表
  initchart()
  window.addEventListener('resize', handleResize)
})

watch(selectedOpt, () => {
  fetchFiveDimensional()
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
@import '@/styles/index.css';

.header-title {
  color: white;
  font-size: 16px;
  font-weight: 500;
  flex: 1;
  margin-left: 8px;
}

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
}

.gauge-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
  background: url('@/assets/household-inspection/indicator-con-bg.png') no-repeat center;
  background-size: cover;
}

.gauge-circle-bg {
  width: 72px;
  height: 60px;
  background: url('@/assets/household-inspection/indicator-bg.png') no-repeat center;
  background-size: cover;
  will-change: transform;
  /* animation: spin 5s linear infinite; */
}

.gauge-value {
  color: #fff;
  font-family: DINPro;
  font-size: 24px;
  font-weight: bold;
  z-index: 1;
}

.gauge-label {
  color: #66ffff;
  font-size: 14px;
  text-align: center;
}
</style>
