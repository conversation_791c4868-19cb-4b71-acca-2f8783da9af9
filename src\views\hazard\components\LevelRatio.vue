<template>
  <div class="panel-container-col">
    <div class="panel-header">重点防控点位比重</div>
    <div class="p-4 panel-content">
      <div class="status-indicators">
        <div class="status-item">
          <div class="status-value">{{ levelRatioData.level1 || '0%' }}</div>
          <div class="status-label">一级重大危险源</div>
        </div>
        <div class="status-item">
          <div class="status-value">{{ levelRatioData.level2 || '0%' }}</div>
          <div class="status-label">二级重大危险源</div>
        </div>
        <div class="status-item">
          <div class="status-value">{{ levelRatioData.level3 || '0%' }}</div>
          <div class="status-label">三级重大危险源</div>
        </div>
        <div class="status-item">
          <div class="status-value">{{ levelRatioData.level4 || '0%' }}</div>
          <div class="status-label">四级重大危险源</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getHazardLevelRatio } from '@/common/api/hazard'

interface LevelRatioData {
  level1: string
  level2: string
  level3: string
  level4: string
}

const levelRatioData = ref<LevelRatioData>({
  level1: '0%',
  level2: '0%',
  level3: '0%',
  level4: '0%'
})

const fetchLevelRatio = async () => {
  try {
    const res = await getHazardLevelRatio({})
    const data = Array.isArray(res?.data) ? res?.data : []
    console.log('重点防控点位比重数据:', data)
    
    // 级别映射
    const levelMap: Record<string, keyof LevelRatioData> = {
      '一级': 'level1',
      '二级': 'level2',
      '三级': 'level3',
      '四级': 'level4'
    }
    
    // 初始化默认值
    const ratios: LevelRatioData = {
      level1: '0%',
      level2: '0%',
      level3: '0%',
      level4: '0%'
    }
    
    // 根据level字段匹配数据
    data.forEach((item: any) => {
      const levelKey = levelMap[item.level]
      if (levelKey) {
        ratios[levelKey] = item.proportion || '0%'
      }
    })
    
    levelRatioData.value = ratios
  } catch (e) {
    console.error('获取重点防控点位比重失败:', e)
  }
}

onMounted(() => {
  fetchLevelRatio()
})
</script>

<style scoped>
@import '@/styles/index.css';

.status-indicators {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}

.status-item {
  position: relative;
  display: flex;
  width: 50%;
  height: 194px;
  flex-direction: column;
  justify-content: space-between;
}

.status-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -60px;
  margin-left: -60px;
  width: 120px;
  height: 120px;
  background: url('@/assets/old-pipe-network-renovation/percent-bg.png') no-repeat center;
  background-size: 100% 100%;
  z-index: 1;
  will-change: transform;
  animation: spin 5s linear infinite;
}

.status-value {
  margin-top: 80px;
  font-family: Noto Sans SC;
  font-size: 32px;
  font-weight: bold;
  line-height: 32px;
  text-align: center;
  color: #ffffff;
  white-space: nowrap;
}

.status-label {
  margin-bottom: 13px;
  font-family: NotoSansSC;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  text-align: center;
  letter-spacing: normal;
  color: #ffffff;
}
</style>
