<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">气瓶用户分析</div>
      <div class="header-dropdown"></div>
    </div>
    <div class="p-4 panel-content">
      <div class="equipment-alarm-container">
        <!-- 左侧统计卡片区域 -->
        <div class="user-overview-container">
          <div v-for="(item, index) in userStats" :key="index" class="user-card">
            <div class="card-content">
              <div class="card-icon">
                <img :src="item.icon" alt="" />
                <div v-if="item.secondIcon" class="second-icon">
                  <img :src="item.secondIcon" alt="" />
                </div>
              </div>
              <div class="card-info">
                <div class="card-title">{{ item.title }}</div>
                <div class="card-value-container">
                  <div class="card-value">{{ item.value }}</div>
                  <div class="card-unit">{{ item.unit }}</div>
                </div>
                <div class="card-compare">
                  同比：
                  <span class="card-compare-number">{{ item.compare ?? '--' }}</span>
                </div>
              </div>
            </div>
            <img :src="iconData.union" alt="" class="card-border-bottom" />
            <div class="card-side-lines">
              <img :src="iconData.vectorLeft" alt="" class="side-line left" />
              <img :src="iconData.vectorRight" alt="" class="side-line right" />
            </div>
          </div>
        </div>
        <!-- 右侧图表区域 -->
        <div class="chart-panel">
          <div class="chart-content">
            <div ref="chartRef" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { pzsyq_yhfx,pzsyq_qpytfx } from '@/common/api/bottledGas'


interface UserStat {
  type: string
  title: string
  value: string
  icon: string
  secondIcon?: string // 添加可选的 secondIcon 属性
  unit?: string // 添加可选的 unit 属性
  compare?: string // 添加可选的 compare 属性
}

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1

const userStats = ref<UserStat[]>([])
const xData = ref<string[]>([])
const chartOption = ref<any>()

// 图标数据配置 - 使用图片路径
const iconData = {
  totalUser: '/src/assets/battle-gas/total-user.svg',
  inUser: '/src/assets/battle-gas/in-user.svg',
  vectorLeft: '/src/assets/industry-icons/Vector-Left.svg',
  vectorRight: '/src/assets/industry-icons/Vector-right.svg',
  union: '/src/assets/industry-icons/Union.svg',
}

const fetchAndInit = async () => {
  try {
    const res = await pzsyq_yhfx()
    // 根据接口返回值赋值左侧卡片
    if (Array.isArray(res.data) && res.data.length > 0) {
      const d = res.data[0]
      userStats.value = [
        {
          type: 'total',
          title: '总用户数（人）',
          value: d['总用户数'] ?? '--',
          icon: iconData.totalUser,
          unit: '人',
          compare: (d['总用户较上月增长率'] ?? 0) + '%',
        },
        {
          type: 'pipeline',
          title: '活跃用户数（人）',
          value: d['活跃用户数'] ?? '--',
          icon: iconData.inUser,
          unit: '人',
          compare: (d['活跃用户较上月增长率'] ?? 0) + '%',
        }
      ]
    }

    // 新增：调用气瓶用途分析接口
    const resQpyt = await pzsyq_qpytfx()
    // 处理气瓶用途分析数据
    if (Array.isArray(resQpyt.data)) {
      // 过滤出有气瓶用途的项
      const chartData = resQpyt.data.filter(d => d['气瓶用途'])
      const categories = chartData.map(d => d['气瓶用途'])
      const totalUsers = chartData.map(d => d['用户总数'] ?? 0)
      const activeUsers = chartData.map(d => d['活跃用户数'] ?? 0)
      const activeRate = chartData.map(d => {
        const rate = d['活跃用户占比'] ?? 0
        return Math.round(rate * 10000) / 100 // 保留两位小数
      })

      chartOption.value = {
        color: ['#409fff', '#66ffff', '#ffd700'],
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          backgroundColor: 'rgba(0,32,64,0.95)',
          borderColor: '#409fff',
          borderWidth: 1,
          extraCssText: 'box-shadow:0 2px 12px rgba(0,0,0,0.3);border-radius:6px;',
          textStyle: { align: 'left', fontFamily: 'Noto Sans SC' },
          formatter: (params: any) => {
            let html = `<div style="min-width:120px;padding:6px 12px 6px 8px;">
              <div style="font-size:14px;color:#fff;font-weight:bold;">${params[0].name}</div>
              <div style="margin-top:2px;font-size:13px;color:#ffd700;">
                <span style="color:#409fff;">用户总数：${params[0].value}</span><br/>
                <span style="color:#66ffff;">活跃用户数：${params[1].value}</span><br/>
                <span style="color:#ffd700;">活跃用户占比：${params[2].value}%</span>
              </div>
            </div>`
            return html
          }
        },
        legend: {
          data: ['用户总数', '活跃用户数', '活跃用户占比'],
          textStyle: { color: '#fff' }
        },
        grid: {
          left: '8%',
          right: '5%',
          bottom: '0%',
          top: '25%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: { color: '#fff', fontSize: 12 },
          axisLine: { lineStyle: { color: '#fff' } },
        },
        yAxis: [
          {
            type: 'value',
            name: '人数',
            axisLabel: { color: '#fff', fontSize: 12 },
            splitLine: { show: true, lineStyle: { color: 'rgba(166, 207, 255, 0.4)', type: 'dashed' } },
            axisLine: { show: false },
            axisTick: { show: false },
          },
          {
            type: 'value',
            name: '活跃用户占比',
            min: 0,
            max: 100,
            axisLabel: { formatter: '{value}%', color: '#fff', fontSize: 12 },
            splitLine: { show: false },
            axisLine: { show: false },
            axisTick: { show: false },
            position: 'right'
          }
        ],
        series: [
          {
            name: '用户总数',
            type: 'bar',
            data: totalUsers,
            barWidth: 20,
            itemStyle: { color: '#409fff' }
          },
          {
            name: '活跃用户数',
            type: 'bar',
            data: activeUsers,
            barWidth: 20,
            itemStyle: { color: '#66ffff' }
          },
          {
            name: '活跃用户占比',
            type: 'line',
            yAxisIndex: 1,
            data: activeRate,
            smooth: true,
            itemStyle: { color: '#ffd700' },
            lineStyle: { color: '#ffd700' }
          }
        ]
      }
    }
    setTimeout(() => {
      initChart()
    }, 0)
  } catch (e) {
    // ...existing code...
  }
}

const initChart = () => {
  if (!chartRef.value || !chartOption.value) return
  chart = echarts.init(chartRef.value)
  chart.setOption(chartOption.value)
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = xData.value.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 2, // 使用主要的bar系列
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 2, // 在主要的bar系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 2, // 使用主要的bar系列
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  fetchAndInit()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.equipment-alarm-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.chart-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .chart-content {
    flex: 1;
    padding: 4px;

    .chart-container {
      width: 100%;
      height: 100%;
    }
  }
}

.user-overview-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 33.33%;
  flex-shrink: 0;
}

.user-card {
  position: relative;
  width: 100%;
  height: 104px;
  overflow: hidden;

  .card-content {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    height: 64px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
    gap: 16px;

    .card-icon {
      flex-shrink: 0;
      width: 48px;
      height: 48px;
      display: flex;
      position: relative;
      align-items: center;
      justify-content: center;

      > img {
        position: absolute;
        top: 0;
        left: 0;
        width: 48px;
        height: 48px;
        z-index: 1;
      }

      .second-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 24px;
        height: 24px;
        z-index: 2;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .card-info {
      flex: 1;
      height: 48px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;

      .card-title {
        width: 100%;
        height: 20px;
        white-space: nowrap;
        color: #66ffff;
        font-family: 'Noto Sans SC';
        font-size: 14px;
        line-height: 20px;
        text-align: left;
        margin-bottom: 4px;
      }

      .card-value-container {
        width: 100%;
        height: 24px;
        display: flex;
        flex-direction: row;
        justify-content: start;
        align-items: baseline;

        .card-value {
          color: #ffffff;
          font-family: 'DINPro';
          font-size: 18px;
          line-height: 24px;
          font-weight: bold;
          margin-right: 4px;
        }

        .card-unit {
          color: #ffffff;
          font-family: 'Noto Sans SC';
          font-size: 14px;
          line-height: 20px;
        }
      }
    }
  }

  .card-border-bottom {
    position: absolute;
    left: 30px;
    bottom: 0px;
    width: calc(100% - 60px);
    height: 12px;
  }

  .card-side-lines {
    .side-line {
      position: absolute;
      top: 0px;
      width: 5.5px;
      height: 115px;

      &.left {
        left: 0px;
      }

      &.right {
        right: 0px;
      }
    }
  }
}
.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}
.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}
/* 卡片同比样式 */
.card-compare {
  margin-top: 2px;
  color: #fff;
  font-size: 10px;

  font-weight: lighter;
}
.card-compare-number {
  font-size: 11px;
  font-weight: 300;
  color: #fff;
  margin-left: 2px;
}
</style>
