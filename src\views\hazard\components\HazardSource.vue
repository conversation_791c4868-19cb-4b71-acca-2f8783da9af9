<template>
  <div class="panel-container">
    <div class="panel-header">重点防控点位</div>
    <div class="p-4 panel-content">
      <div class="status-indicators">
        <div class="status-item">
          <div class="status-value">{{ hazardSourceData.key_prevention_points || 0 }}</div>
          <div class="status-label">重点防控点位</div>
        </div>
        <div class="status-item">
          <div class="status-value">{{ hazardSourceData.emergency_plans_count || 0 }}</div>
          <div class="status-label">相关应急预案</div>
        </div>
        <div class="status-item">
          <div class="status-value">{{ hazardSourceData.safety_evaluation_reports_count || 0 }}</div>
          <div class="status-label">安全评估报告</div>
        </div>
        <div class="status-item">
          <div class="status-value">{{ hazardSourceData.supervision_measures_count || 0 }}</div>
          <div class="status-label">相关监管措施</div>
        </div>
        <div class="status-item">
          <div class="status-value">{{ hazardSourceData.technical_data_count || 0 }}</div>
          <div class="status-label">相关技术资料</div>
        </div>
        <div class="status-item">
          <div class="status-value">{{ hazardSourceData.peripheral_monitoring_count || 0 }}</div>
          <div class="status-label">周边监控视频</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getHazardSource } from '@/common/api/hazard'

interface HazardSourceData {
  supervision_measures_count: number
  peripheral_monitoring_count: number
  technical_data_count: number
  safety_evaluation_reports_count: number
  emergency_plans_count: number
  key_prevention_points: number
}

const hazardSourceData = ref<HazardSourceData>({
  supervision_measures_count: 0,
  peripheral_monitoring_count: 0,
  technical_data_count: 0,
  safety_evaluation_reports_count: 0,
  emergency_plans_count: 0,
  key_prevention_points: 0
})

const fetchHazardSource = async () => {
  try {
    const res = await getHazardSource({})
    const data = Array.isArray(res?.data) ? res.data[0] : res?.data
    console.log(data)
    if (data) {
      hazardSourceData.value = {
        supervision_measures_count: Number(data.supervision_measures_count) || 0,
        peripheral_monitoring_count: Number(data.peripheral_monitoring_count) || 0,
        technical_data_count: Number(data.technical_data_count) || 0,
        safety_evaluation_reports_count: Number(data.safety_evaluation_reports_count) || 0,
        emergency_plans_count: Number(data.emergency_plans_count) || 0,
        key_prevention_points: Number(data.key_prevention_points) || 0
      }
    }
  } catch (e) {
    console.error('获取重点防控点位数据失败:', e)
  }
}

onMounted(() => {
  fetchHazardSource()
})
</script>

<style scoped>
@import '@/styles/index.css';

.status-indicators {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.status-item {
  width: 31%;
  height: 104px;
  background: url('@/assets/hazard/hazard-source-icon.png') no-repeat center bottom;
  background-size: 140px 70px;
}

.status-value {
  font-family: Noto Sans SC;
  font-size: 24px;
  font-weight: bold;
  line-height: 32px;
  text-align: center;
  color: #fff;
  white-space: nowrap;
}

.status-label {
  font-family: NotoSansSC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  text-align: center;
  letter-spacing: normal;
  color: #fff;
}

.status-unit {
  font-family: Noto Sans SC;
  font-size: 16px;
  font-weight: normal;
  line-height: 24px;
  text-align: center;
  color: #ffffff;
}
</style>
