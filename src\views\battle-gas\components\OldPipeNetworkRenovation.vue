<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header" style="position: relative">
      <!-- 横向卡片统计条 -->
      <div class="stat-bar-row">
        <div class="stat-bar-item">
          <span class="stat-bar-icon"></span>
          <span class="stat-bar-label">购气总单量</span>
          <span class="stat-bar-value">{{ totalGasOrder }}</span>
        </div>
        <div class="stat-bar-item">
          <span class="stat-bar-icon"></span>
          <span class="stat-bar-label">随瓶安检单量</span>
          <span class="stat-bar-value">{{ totalCheckOrder }}</span>
        </div>
      </div>
      <div class="header-title">随瓶安检分析</div>
      <div class="header-dropdown"></div>
      <img class="zy-bg" src="/src/assets/battle-gas/zy-bg.svg" alt="" />
    </div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { pzsyq_spajfx } from '@/common/api/bottledGas'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
let tooltipTimer: ReturnType<typeof setInterval> | null = null
let currentIndex = -1

// 顶部统计条数据
const totalGasOrder = ref(0)
const totalCheckOrder = ref(0)

const chartOption = ref<any>({
  animation: true,
  backgroundColor: 'transparent',
  grid: {
    left: '5%',
    right: '0%',
    top: '45%',
    bottom: '0%',
    containLabel: true,
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
      shadowStyle: {
        shadowColor: 'rgba(11, 46, 115, 0.3)',
        shadowBlur: 10,
      },
    },
    backgroundColor: 'rgba(11, 46, 115, 0.6)',
    borderColor: '#409FFF',
    backdropFilter: 'blur(4px)',
    textStyle: {
      color: '#fff',
    },
  },
  legend: {
    data: ['购气单量', '安检单量'],
    textStyle: {
      color: '#fff',
      fontSize: 12,
    },
    itemWidth: 20,
    itemHeight: 10,
    icon: 'rect',
    itemGap: 24,
    top: 70,
  },
  xAxis: {
    type: 'category',
    data: [],
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.45)',
      },
    },
    axisLabel: {
      color: '#fff',
      fontSize: 12,
    },
    axisTick: {
      show: false,
    },
  },
  yAxis: {
    type: 'value',
    name: '单位：单',
    nameTextStyle: {
      color: '#fff',
      fontSize: 12,
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.45)',
      },
    },
    axisLabel: {
      color: '#fff',
      fontSize: 12,
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.1)',
        type: 'dashed',
      },
    },
  },
  series: [
    {
      name: '购气单量',
      type: 'bar',
      data: [],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(255, 198, 26, 0.15)' },
          { offset: 1, color: 'rgba(255, 198, 26, 0.75)' },
        ]),
      },
      barWidth: 20,
      z: 2,
    },
    {
      name: '安检单量',
      type: 'bar',
      data: [],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(77, 203, 98, 0.15)' },
          { offset: 1, color: 'rgba(77, 203, 98, 0.75)' },
        ]),
      },
      barWidth: 20,
      z: 2,
    },
  ],
})

const initChart = () => {
  if (!chartRef.value) return
  chart = echarts.init(chartRef.value)
  chart.setOption(chartOption.value)
  startTooltipAnimation()
}

const startTooltipAnimation = () => {
  const dataCount = chartOption.value.xAxis.data.length
  if (!chart || dataCount === 0) return
  if (tooltipTimer) clearInterval(tooltipTimer)
  tooltipTimer = setInterval(() => {
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
    currentIndex = (currentIndex + 1) % dataCount
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  // 生命周期调用接口
  pzsyq_spajfx().then(res => {
    if (Array.isArray(res.data)) {
      // 横坐标
      chartOption.value.xAxis.data = res.data.map(item => item.area)
      // 柱状图数据
      chartOption.value.series[0].data = res.data.map(item => item['购气单量'] ?? 0)
      chartOption.value.series[1].data = res.data.map(item => item['安检单量'] ?? 0)
      // 顶部统计条
      totalGasOrder.value = res.data.reduce((sum, item) => sum + (item['购气单量'] ?? 0), 0)
      totalCheckOrder.value = res.data.reduce((sum, item) => sum + (item['安检单量'] ?? 0), 0)
      // 更新图表
      setTimeout(() => {
        initChart()
      }, 0)
    }
  })
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) clearInterval(tooltipTimer)
})
</script>

<style scoped>
@import '@/styles/index.css';

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
}
.zy-bg {
  position: absolute;
  right: 20px;
  top: 60px;
  width: 95%;
  height: 60px;
  z-index: 999;
}
.stat-bar-row {
  position: absolute;
  left: 55%;
  top: 50px;
  transform: translateX(-50%);
  width: 92%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 20;
  padding: 18px 32px 10px 32px;
}
.stat-bar-item {
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 220px;
}
.stat-bar-icon {
  display: inline-block;
  width: 18px;
  height: 6px;
  background: linear-gradient(90deg, #bfc7d1 0%, #e6eaf3 100%);

  margin-right: 8px;
}
.stat-bar-label {
  color: #9cecfc;
  font-size: 12px;
  font-weight: lighter;
  margin-right: 8px;
}
.stat-bar-value {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  margin-left: 2px;
  letter-spacing: 1px;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
