<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">气瓶年限分布</div>
      <div class="header-dropdown"></div>
    </div>
    <div class="p-4 panel-content">
      <div class="equipment-alarm-container">
        <!-- 多个图表区域 -->
        <div
          class="chart-panel"
          v-for="(chart, idx) in charts"
          :key="idx"
        >
          <div class="chart-content chart-section">
            <div class="rate-icon"></div>
            <div
              :ref="el => chartRefs[idx] = el"
              class="donut-chart chart-container"
            ></div>
            <div class="triangle"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { pzsyq_qpnxfb } from '@/common/api/bottledGas'

const chartRefs = ref<(Element | null)[]>([])
const charts = ref<any[]>([])

const buildOption = (groupData: any[]) => {
  const yAxisData = groupData.map((item: any) => item.age_group)
  const xData = groupData.map((item: any) => item.count)
  return {
    backgroundColor: 'transparent',
    grid: {
      left: '20px',
      right: '20px',
      top: 0,
      height: 80,
      bottom: 0,
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      backgroundColor: 'rgba(0,32,64,0.95)',
      borderColor: '#409fff',
      borderWidth: 1,
      extraCssText: 'box-shadow:0 2px 12px rgba(0,0,0,0.3);border-radius:6px;',
      textStyle: { align: 'left', fontFamily: 'Noto Sans SC' },
      formatter: (params: any) => {
        if (!params || !params.length) return ''
        const idx = params[0].dataIndex
        return `
          <div style="min-width:120px;padding:6px 12px 6px 8px;">
            <div style="font-size:14px;color:#fff;font-weight:bold;">${yAxisData[idx]}</div>
            <div style="margin-top:2px;font-size:13px;color:#ffd700;">
              <span style="font-weight:500;">${xData[idx]}</span>
              <span style="color:#b2e3ff;font-size:12px;margin-left:4px;">数量</span>
            </div>
          </div>
        `
      }
    },
    xAxis: {
      type: 'value',
      max: 100,
      axisTick: { show: false },
      axisLine: { show: false },
      splitLine: { show: false },
      axisLabel: { show: false },
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false },
        data: yAxisData,
      },
      {
        inverse: true,
        offset: 0,
        axisLabel: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
        data: xData.map(i => ({
          value: 0,
          key: i,
        })),
      },
    ],
    series: [
      {
        type: 'bar',
        barWidth: 20,
        data: xData,
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(140, 255, 255, 0.15)',
        },
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: 'rgba(64, 159, 255, 1)' },
              { offset: 1, color: 'rgba(71, 235, 235, 1)' },
            ],
          },
        },
        z: 2,
      },
      {
        type: 'pictorialBar',
        itemStyle: { normal: { color: '#0E3169' } },
        symbolRepeat: 'fixed',
        symbolMargin: 8,
        symbol: 'rect',
        symbolClip: true,
        symbolSize: [2, 20],
        symbolPosition: 'start',
        symbolOffset: [-2, 0],
        data: [100, 100],
        label: {
          show: true,
          position: 'right',
          offset: [20, 0],
          fontSize: 16,
          color: '#0E3169',
        },
        z: 4,
        zlevel: 1,
      },
      {
        type: 'scatter',
        symbolSize: 0,
        data: yAxisData.map((_, index) => [0, index]),
        label: {
          show: true,
          position: [0, -15],
          color: '#fff',
          fontSize: 14,
          align: 'left',
          verticalAlign: 'bottom',
          formatter: (params: any) => yAxisData[params.dataIndex],
        },
        z: 4,
      },
      {
        type: 'scatter',
        symbolSize: 0,
        data: xData.map((_, index) => [100, index]),
        label: {
          show: true,
          position: [0, -15],
          color: '#66FFFF',
          fontSize: 20,
          align: 'right',
          verticalAlign: 'bottom',
          formatter: (params: any) => `${xData[params.dataIndex]}%`,
        },
        z: 4,
      },
    ],
  }
}

const initCharts = () => {
  charts.value.forEach((group, idx) => {
    const el = chartRefs.value[idx]
    if (el) {
      const chartInstance = echarts.init(el)
      chartInstance.setOption(buildOption(group))
    }
  })
}

// 修改此方法为接口获取并分组处理
const fetchAndInit = async () => {
  try {
    const res = await pzsyq_qpnxfb()
    // 处理接口返回值，每个对象单独作为一个数组
    if (Array.isArray(res.data)) {
      charts.value = res.data.map(item => [item])
      setTimeout(() => {
        initCharts()
      }, 0)
    }
  } catch (e) {

  }
}

const handleResize = () => {
  charts.value.forEach((_, idx) => {
    const el = chartRefs.value[idx]
    if (el) {
      echarts.getInstanceByDom(el)?.resize()
    }
  })
}

onMounted(() => {
  fetchAndInit()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  charts.value.forEach((_, idx) => {
    const el = chartRefs.value[idx]
    if (el) {
      echarts.getInstanceByDom(el)?.dispose()
    }
  })
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.equipment-alarm-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 20px;
  overflow-x: hidden;

  /* 优化滚动条样式为透明 */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;

  &::-webkit-scrollbar {
    width: 8px;
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 8px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

.chart-panel {
  width: 100%; // 占满容器宽度
  display: flex;
  flex-direction: column;

  .chart-content {
    flex: 1;
    padding: 2px;

    // 新增底图和图标样式复用
    &.chart-section {
      display: flex;
      align-items: center;
      position: relative;
      width: 100%;
      height: 80px;
      background: linear-gradient(
        90deg,
        rgba(64, 159, 255, 0) 0%,
        rgba(64, 159, 255, 0.15) 50%,
        rgba(64, 159, 255, 0) 100%
      );
      box-sizing: border-box;
      border: 1px solid;
      border-image: linear-gradient(180deg, rgba(64, 159, 255, 0) 0%, #409fff 49%, rgba(64, 159, 255, 0) 100%) 1;
      border-radius: 12px;
      box-shadow: 0 4px 24px rgba(0,0,0,0.12);

      .rate-icon {
        margin-left: 20px;
        width: 48px;
        height: 48px;
        background: url('@/assets/evaluate/resource-rate-icon.png') no-repeat center center;
        background-size: contain;
      }

      .donut-chart {
        flex: 1;
        height: 60px;
        min-height: 60px;
        max-height: 80px;
      }

      .triangle {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 8px;

        &::before {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 8px;
          height: 8px;
          border-bottom-width: 1px;
          border-left-width: 1px;
          border-style: solid;
          border-color: #66ffff;
        }

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          right: 0;
          width: 8px;
          height: 8px;
          border-bottom-width: 1px;
          border-right-width: 1px;
          border-style: solid;
          border-color: #66ffff;
        }
      }
    }
  }
}

.user-overview-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 33.33%;
  flex-shrink: 0;
}

.user-card {
  position: relative;
  width: 100%;
  height: 104px;
  overflow: hidden;

  .card-content {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    height: 64px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
    gap: 16px;

    .card-icon {
      flex-shrink: 0;
      width: 48px;
      height: 48px;
      display: flex;
      position: relative;
      align-items: center;
      justify-content: center;

      > img {
        position: absolute;
        top: 0;
        left: 0;
        width: 48px;
        height: 48px;
        z-index: 1;
      }

      .second-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 24px;
        height: 24px;
        z-index: 2;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .card-info {
      flex: 1;
      height: 48px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;

      .card-title {
        width: 100%;
        height: 20px;
        white-space: nowrap;
        color: #66ffff;
        font-family: 'Noto Sans SC';
        font-size: 14px;
        line-height: 20px;
        text-align: left;
        margin-bottom: 4px;
      }

      .card-value-container {
        width: 100%;
        height: 24px;
        display: flex;
        flex-direction: row;
        justify-content: start;
        align-items: baseline;

        .card-value {
          color: #ffffff;
          font-family: 'DINPro';
          font-size: 18px;
          line-height: 24px;
          font-weight: bold;
          margin-right: 4px;
        }

        .card-unit {
          color: #ffffff;
          font-family: 'Noto Sans SC';
          font-size: 14px;
          line-height: 20px;
        }
      }
    }
  }

  .card-border-bottom {
    position: absolute;
    left: 30px;
    bottom: 0px;
    width: calc(100% - 60px);
    height: 12px;
  }

  .card-side-lines {
    .side-line {
      position: absolute;
      top: 0px;
      width: 5.5px;
      height: 115px;

      &.left {
        left: 0px;
      }

      &.right {
        right: 0px;
      }
    }
  }
}

.panel-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 480px; // 建议高度，可根据实际页面调整
  overflow: auto;    // 内容超出时整体滚动，避免遮盖
}

.status-indicators {
  width: 100%;
  height: 128px;
  display: flex;
  justify-content: space-between;
}

.status-item {
  display: flex;
  width: 49%;
  background: url('@/assets/evaluate/resource-ind-bg.png') no-repeat center center;
  background-size: 120px 52px;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(64, 159, 255, 0) 0%, #409fff 49%, rgba(64, 159, 255, 0) 100%) 1;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 28px;
  text-align: center;
  color: #fff;
  white-space: nowrap;
}

.status-label {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  text-align: center;
  letter-spacing: normal;
  color: #ffffff;
}

.chart-section {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  height: 80px;
  background: rgba(10, 30, 60, 0.85); /* 灰色panel风格 */
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(64, 159, 255, 0) 0%, #409fff 49%, rgba(64, 159, 255, 0) 100%) 1;
  /* 保留原有渐变 */
  /* background: linear-gradient(
    90deg,
    rgba(64, 159, 255, 0) 0%,
    rgba(64, 159, 255, 0.15) 50%,
    rgba(64, 159, 255, 0) 100%
  ); */

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    border-top-width: 1px;
    border-left-width: 1px;
    border-style: solid;
    border-color: #66ffff;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 8px;
    border-top-width: 1px;
    border-right-width: 1px;
    border-style: solid;
    border-color: #66ffff;
  }
}

.triangle {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 8px;

  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 8px;
    height: 8px;
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-style: solid;
    border-color: #66ffff;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 8px;
    height: 8px;
    border-bottom-width: 1px;
    border-right-width: 1px;
    border-style: solid;
    border-color: #66ffff;
  }
}

.rate-icon {
  margin-left: 20px;
  width: 48px;
  height: 48px;
  background: url('@/assets/evaluate/resource-rate-icon.png') no-repeat center center;
  background-size: contain;
}

.donut-chart {
  flex: 1;
  height: 60px;
  min-height: 60px;
  max-height: 80px;
}

.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}

.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}
</style>
