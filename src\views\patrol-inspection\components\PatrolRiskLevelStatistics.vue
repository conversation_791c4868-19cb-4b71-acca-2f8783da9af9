<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">巡检风险预警分级统计</div>
      <div class="header-dropdown">
        <div class="btn-group">
          <button
            class="period-btn"
            :class="{ active: selectedOpt === '城镇' }"
            @click="selectedOpt = '城镇'"
          >
            城镇
          </button>
          <button
            class="period-btn"
            :class="{ active: selectedOpt === '农村' }"
            @click="selectedOpt = '农村'"
          >
            农村
          </button>
        </div>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import { patrol_level } from '@/common/api/patrol' // 新增：导入接口

const chartRef = ref<HTMLElement>()
const selectedOpt = ref<string>('城镇')
let chart: echarts.ECharts | null = null
// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1

// 图表配置响应式数据
let chartOption = {
  backgroundColor: 'transparent',
  title: {
    left: '49%',
    top: '38%',
    textAlign: 'center',
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    subtextStyle: {
      color: '#fff',
      fontSize: 24,
    },
  },
  legend: {
    show: true,
    orient: 'vertical',
    right: '0%',
    bottom: '10%',
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    itemWidth: 12,
    itemHeight: 8,
    icon: 'rect',
  },
  series: [
    {
      type: 'pie',
      radius: ['36.1%', '74.1%'],
      center: ['50%', '50%'],
      silent: true,
      data: [{ value: 1, itemStyle: { color: 'rgba(153, 213, 255, 0.15)' } }],
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
    },
    {
      type: 'pie',
      radius: ['41.7%', '68.5%'],
      center: ['50%', '50%'],
      data: [
        { value: 0, name: '', itemStyle: { color: '#409FFF' } },
        { value: 0, name: '', itemStyle: { color: '#66FFFF' } },
        { value: 0, name: '', itemStyle: { color: '#FFC61A' } },
        { value: 0, name: '', itemStyle: { color: '#FF4C4D' } },
      ],
      label: {
        show: true,
        color: '#fff',
        formatter: `{percent|{d}%}\n{value|{c}}个`,
        rich: {
          percent: {
            fontSize: 20,
            color: '#fff',
          },
          value: {
            fontSize: 12,
            color: '#fff',
          },
        },
      },
      labelLine: {
        show: true,
      },
    },
  ],
}

// 新增：获取巡检风险预警分级统计数据
const fetchRiskLevelData = async (type: string) => {
  try {
    const param = { type }

    // 将带签名的请求体传给接口
    const res = await patrol_level(param)

    // 可能的返回结构：
    // 1) { msg, success, data: [{ risk_count, level, percentage }, ...] }
    // 2) 直接返回数组 [{ risk_count, level, percentage }, ...]
    const body = res?.data ?? res

    if (body && Array.isArray(body)) {
      // 预定义颜色数组，确保颜色循环
      const colors = ['#409FFF', '#66FFFF', '#FFC61A', '#FF4C4D']
      let colorIndex = 0

      // 转换API数据为图表需要的格式
      const transformedData = body.map((item: any) => {
        let name = ''
        let color = colors[colorIndex % colors.length]
        colorIndex++

        // 根据预警级别映射名称
        switch (item.level) {
          case '一般':
            name = '一般'
            break
          case '高等':
            name = '高等'
            break
          case 'warning-levle-1':
            name = '一级预警'
            break
          case 'warning-levle-2':
            name = '二级预警'
            break
          case 'warning-levle-3':
            name = '三级预警'
            break
          case 'warning-levle-4':
            name = '四级预警'
            break
          default:
            name = item.level || '未知级别'
        }

        return {
          value: Number(item.risk_count) || 0,
          name: name,
          itemStyle: { color: color },
          percentage: Number(item.percentage) || 0
        }
      })

      // 如果有数据则更新图表配置
      if (transformedData.length > 0) {
        // 计算总数
        const totalCount = transformedData.reduce((sum, item) => sum + item.value, 0)

        // 更新图表配置
        chartOption.title.subtext = totalCount.toString()
        chartOption.series[1].data = transformedData

        // 重新渲染图表
        updateChart()
      }
    } else {
      // 无有效数据时不做变更
      console.warn('巡检风险预警分级统计接口返回格式不符，未更新数据', res)
    }
  } catch (err) {
    console.error('获取巡检风险预警分级统计失败', err)
  }
}

// 新增：更新图表
const updateChart = () => {
  if (!chart) return
  chart.setOption(chartOption)
}

// 使用响应式的图表配置，已在上面的 chartOption 中定义

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(chartOption)
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = chartOption.series[1].data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  // 在生命周期中调用接口并更新视图
  fetchRiskLevelData(selectedOpt.value)
  window.addEventListener('resize', handleResize)
})

// 监听按钮状态变化，重新请求新数据
watch(selectedOpt, (newType) => {
  fetchRiskLevelData(newType)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}

.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
