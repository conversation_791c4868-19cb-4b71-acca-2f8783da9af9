import HmacSHA256 from '@/config/hmac_sha256'
import HashMap from '@/config/hashMap'

const DEFAULT_SIGN_KEY = '9900J7T2J91992' // 网站给定的固定 key

// 生成当前时间戳（毫秒）
export function createTimestamp(): number {
  // 按图片要求使用 new Date().getTime()
  return new Date().getTime()
}

// 构建待签名字符串，使用 HashMap 且格式与截图一致：
// const hashMap = new HashMap()
// hashMap.put('param', data)
// hashMap.put('timestamp', time)
// const str = hashMap.toString()
export function buildSignString(param: any, timestamp: number): string {
  const hashMap = new HashMap()
  // 按照图片：param 对应调用 api 时的传参 data
  hashMap.put('param', param)
  hashMap.put('timestamp', timestamp)
  const str = hashMap.toString()
  return str
}

// 使用 hmac_sha256 工具签名（返回与现有实现兼容的字符串）
export function signString(str: string, key: string): string {
  const signer = new HmacSHA256()
  return signer.sign(str, key)
}

// 新增：按截图调用方式一次性返回 time 与 sign（以及中间字符串 str）
// 参数名采用 data，与示例一致；默认 key 为网站给定值
export function generateSignAndTime(data: any = {}, key: string = DEFAULT_SIGN_KEY) {
  const time = createTimestamp()
  const str = buildSignString(data, time)
  const sign = signString(str, key)
  return {
    sign,
    time,
    str,
  }
}

// 组合一个带 timestamp 与 sign 的请求体，默认使用网站 key
export function createSignedPayload(param: any = {}, key: string = DEFAULT_SIGN_KEY) {
  const timestamp = createTimestamp()
  const signStr = buildSignString(param, timestamp)
  const sign = signString(signStr, key)
  return {
    param,
    timestamp,
    sign,
  }
}
