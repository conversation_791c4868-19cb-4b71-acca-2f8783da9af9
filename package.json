{"name": "gas-screen-new", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "build-force": "vite build", "preview": "vite preview", "format": "pnpx prettier . --write", "lint": "pnpx oxlint@latest"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@element-plus/icons-vue": "^2.3.2", "@tailwindcss/postcss": "^4.1.11", "@tanstack/vue-table": "^8.21.3", "@vueuse/core": "^13.6.0", "autofit.js": "^3.2.8", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "embla-carousel-vue": "^8.6.0", "lucide-vue-next": "^0.533.0", "pinia": "^3.0.3", "reka-ui": "^2.4.0", "shadcn-vue": "^2.2.0", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.6", "vue": "^3.5.17", "vue-router": "4", "vue-sonner": "^2.0.2"}, "devDependencies": {"@types/node": "^24.1.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "prettier": "3.6.2", "sass": "^1.92.0", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}