#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    client_max_body_size 1024M;
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    client_header_buffer_size 512k;
    large_client_header_buffers 4 512k;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;
    gzip on;
    gzip_static on;
    gzip_min_length  5k;
    gzip_buffers     4 16k;
    gzip_http_version 1.0;
    gzip_comp_level 7;
    gzip_types       text/plain application/javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
    gzip_vary on;
  #upstream tomcat-server {
   #    server fsti-soc-web-tomcat:8081 weight=1;
  #} 
	server {
		listen    80;
		location / {
      root   /usr/share/nginx/html;
      try_files $uri $uri/ /index.html;
      index  index.html index.htm;
    }

    location /prod-api/ {
      client_max_body_size 2048m;
      proxy_pass https://city189.cn:3600;
      rewrite "^/prod-api/(.*)$" /gasPlatform/$1 break;
    }

    location /service-api/ {
      client_max_body_size 2048m;
      proxy_pass http://************:31307;
      rewrite "^/service-api/(.*)$" /$1 break;
      
      # 代理请求头设置
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
      
      # 超时设置
      proxy_connect_timeout 300s;
      proxy_send_timeout 300s;
      proxy_read_timeout 300s;
      
      # 缓冲区设置
      proxy_buffering on;
      proxy_buffer_size 64k;
      proxy_buffers 8 64k;
      proxy_busy_buffers_size 128k;
      
      # 错误处理
      proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
      proxy_next_upstream_tries 2;
    }
  }
}