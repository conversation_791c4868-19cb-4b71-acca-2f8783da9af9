<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">场站监控画面</div>
    </div>
    <div class="flex p-4 panel-content">
      <div class="pr-4 grow">
        <div class="header-dropdown">
          <Combobox by="label" class="">
            <ComboboxAnchor class="w-full dropdown-btn">
              <div class="relative items-center w-full">
                <ComboboxInput
                  class=""
                  :display-value="val => val?.label ?? ''"
                  placeholder="请输入监控画面查询点位"
                  @input="onSearchInput"
                />
                <span class="absolute inset-y-0 flex items-center justify-center px-3 start-0">
                  <Search class="size-4 text-muted-foreground" />
                </span>
              </div>
            </ComboboxAnchor>

            <ComboboxList class="w-[296px] bg-[#409fff]/15 border-none">
              <ComboboxEmpty>No framework found.</ComboboxEmpty>

              <ComboboxGroup>
                <ComboboxItem
                  v-for="framework in filteredOptions"
                  :key="framework.value"
                  :value="framework"
                  class="text-[#409fff]"
                >
                  {{ framework.label }}

                  <ComboboxItemIndicator>
                    <Check :class="cn('ml-auto h-4 w-4')" />
                  </ComboboxItemIndicator>
                </ComboboxItem>
              </ComboboxGroup>
            </ComboboxList>
          </Combobox>
        </div>
        <!-- 每行最多 2 个按钮，使用 grid 两列，外层加滚动容器 -->
        <div class="video-list-wrapper">
          <ul class="grid grid-cols-2 gap-2 py-4 w-full">
            <li
              v-for="video in videoList"
              :key="video.id"
              @click="handleChangeVideo(video)"
              :class="[
                'w-full h-8 leading-8 text-center text-sm border border-[#409fff] bg-[#409fff]/15 rounded-xs cursor-pointer',
                { 'border-[#FFC61A] bg-[#FFC61A]/15': video.id === videoInfo.id },
              ]"
            >
              {{ truncate(video.title, 8) }}
            </li>
          </ul>
        </div>
      </div>
      <div class="relative w-[400px] h-[224px] overflow-hidden rounded" @click="handleShowDialog(videoInfo)">
        <video
          class="object-contain w-full h-full"
          :src="videoInfo.url"
          disablepictureinpicture
          muted
          loop
          autoplay
          playsinline
        ></video>
        <div class="absolute bottom-0 flex items-center video-label flex-start">
          <MapPin class="w-4 h-4 ml-3" />
          <div class="ml-1 text-xs">{{ videoInfo.title }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { MapPin, Search, Check } from 'lucide-vue-next'
import { cn } from '@/lib/utils'
import {
  Combobox,
  ComboboxAnchor,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxInput,
  ComboboxItem,
  ComboboxItemIndicator,
  ComboboxList,
} from '@/components/ui/combobox'
import { zdjc_czjkhm } from '@/common/api/runMonitor' // 新增：导入场站监控画面接口
import { createSignedPayload } from '@/common/utils/auth' // 新增：导入签名工具

const emit = defineEmits<{
  (e: 'click', value: any): void
}>()

// 搜索关键词
const searchQuery = ref<string>('')

// 新增：mock 地址列表与随机选择函数
const mockUrls = ['/mock/stream.webm', '/mock/stream2.webm', '/mock/stream3.webm']
const getRandomMockUrl = () => mockUrls[Math.floor(Math.random() * mockUrls.length)]

// 下拉选项与视频列表（响应式）
const options = ref<{ label: string; value: string }[]>([])
const videoList = ref<any[]>([])

// 映射后的当前选中视频
const videoInfo = ref<any>({
  id: '',
  title: '',
  address: '',
  url: getRandomMockUrl(), // 默认使用随机 mock 地址作为兜底
})

// 过滤后的下拉选项（按搜索关键词过滤）
const filteredOptions = computed(() => {
  if (!searchQuery.value) return options.value
  const q = searchQuery.value.toLowerCase()
  return options.value.filter(o => (o.label ?? '').toLowerCase().includes(q))
})

const handleChangeVideo = (video: any) => {
  videoInfo.value = video
}

const handleShowDialog = (video: any) => {
  emit('click', video)
}

// 截断显示文本，超过 len 显示 "xxxxx..."
const truncate = (s: string, len = 8) => {
  if (!s) return ''
  return s.length > len ? s.slice(0, len) + '...' : s
}

const onSearchInput = (e: any) => {
  // ComboboxInput 可能直接 emit 字符串或 input event，容错处理
  const v = typeof e === 'string' ? e : (e?.target?.value ?? '')
  searchQuery.value = String(v)
}

// 请根据实际项目把 key 放到安全的配置文件或环境变量中，这里为演示使用占位符
const API_KEY = '9900J7T2J91992'

// 新增：从后端获取监控画面列表并映射到 options / videoList
const fetchVideoList = async () => {
  try {
    const signed = createSignedPayload({}, API_KEY)
    const res = await zdjc_czjkhm(signed)
    const body = res?.data ?? res

    let list: any[] | null = null
    if (Array.isArray(body)) {
      list = body
    } else if (Array.isArray(body?.data)) {
      list = body.data
    } else if (Array.isArray(body?.result)) {
      list = body.result
    }

    if (Array.isArray(list) && list.length > 0) {
      // 映射后端字段到组件所需结构，兼容 video_name / channel_code 等命名
      const mapped = list.map((it: any) => {
        const id = it.channel_code ?? it.channelCode ?? it.channel_id ?? it.channelId ?? String(it.video_name ?? '')
        const title = it.video_name ?? it.title ?? it.name ?? ''
        // url 未提供时使用随机 mock 地址作为兜底
        const url = it.playUrl ?? it.streamUrl ?? it.url ?? getRandomMockUrl()
        const address = it.address ?? it.location ?? ''
        return { id: String(id), title: String(title), address: String(address), url: String(url), _raw: it }
      })

      // 设置下拉选项与视频列表
      options.value = mapped.map(m => ({ label: m.title || m.address || m.id, value: m.id }))
      videoList.value = mapped

      // 选中首个视频，确保有 url（mapped 已处理）
      videoInfo.value = mapped[0] || videoInfo.value
    } else {
      console.warn('场站监控画面接口未返回列表，使用本地默认数据', res)
    }
  } catch (err) {
    console.error('获取场站监控画面失败', err)
  }
}

onMounted(() => {
  // 调用接口获取监控画面列表
  fetchVideoList()
})
</script>

<style scoped>
@import '@/styles/index.css';

/* 新增：视频列表滚动容器 */
.video-list-wrapper {
  max-height: 192px; /* 可根据面板高度调整 */
  overflow-y: auto;
  padding-right: 6px; /* 给滚动条留出些空间，避免遮挡内容 */
}

/* WebKit 浏览器滚动条样式 */
.video-list-wrapper::-webkit-scrollbar {
  width: 8px;
}
.video-list-wrapper::-webkit-scrollbar-track {
  background: transparent;
}
.video-list-wrapper::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 4px;
}
.video-list-wrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.12);
}

/* Firefox 滚动条细微适配 */
.video-list-wrapper {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.08) transparent;
}

/* ...existing styles... */
.dropdown-btn {
  border: 1px solid #409fff;
  color: #409fff;
  border-radius: 4px;
  background: rgba(64, 159, 255, 0.15);
  box-sizing: border-box;
  font-size: 12px;
  height: auto;
  outline: none;
}

.video-label {
  width: 448px;
  height: 24px;
  background: rgba(16, 34, 54, 0.6);
}
</style>
