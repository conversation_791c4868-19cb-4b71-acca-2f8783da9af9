<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">入户安检率分析</div>
      <div class="header-dropdown">
        <div class="btn-group">
          <button
            class="period-btn"
            :class="{ active: selectedOpt === 'administrative' }"
            @click="selectedOpt = 'administrative'"
          >
            本年
          </button>
          <button
            class="period-btn"
            :class="{ active: selectedOpt === 'enterprise' }"
            @click="selectedOpt = 'enterprise'"
          >
            去年
          </button>
        </div>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import { ndgdqrhajlfx } from '@/common/api/indoor'

const chartRef = ref<HTMLElement>()
const selectedOpt = ref<string>('administrative')
let chart: echarts.ECharts | null = null
let tooltipTimer: ReturnType<typeof setInterval> | null = null
let currentIndex = -1

const categories = ref<string[]>([])
const totalUsers = ref<number[]>([])
const activeUsers = ref<number[]>([])
const activeRate = ref<number[]>([])

const chartOption = ref<any>({
  color: ['#409fff', '#66ffff', '#ffd700'],
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'shadow' },
    backgroundColor: 'rgba(0,32,64,0.95)',
    borderColor: '#409fff',
    borderWidth: 1,
    extraCssText: 'box-shadow:0 2px 12px rgba(0,0,0,0.3);border-radius:6px;',
    textStyle: { align: 'left', fontFamily: 'Noto Sans SC' },
    formatter: (params: any) => {
      let html = `<div style="min-width:120px;padding:6px 12px 6px 8px;">
        <div style="font-size:14px;color:#fff;font-weight:bold;">${params[0].name}</div>
        <div style="margin-top:2px;font-size:13px;color:#ffd700;">
          <span style="color:rgba(0,255,128,0.7);">用户总数：${params[0].value}</span><br/>
          <span style="color:rgba(64,159,255,0.7);">活跃用户数：${params[1].value}</span><br/>
          <span style="color:#ffd700;">活跃用户占比：${params[2].value}%</span>
        </div>
      </div>`
      return html
    },
  },
  legend: {
    data: ['用户总数', '活跃用户数', '活跃用户占比'],
    textStyle: { color: '#fff' },
  },
  grid: {
    left: '8%',
    right: '5%',
    bottom: '0%',
    top: '25%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: categories.value,
    axisLabel: { color: '#fff', fontSize: 12 },
    axisLine: { lineStyle: { color: '#fff' } },
  },
  yAxis: [
    {
      type: 'value',
      name: '人数',
      axisLabel: { color: '#fff', fontSize: 12 },
      splitLine: {
        show: true,
        lineStyle: { color: 'rgba(166, 207, 255, 0.4)', type: 'dashed' },
      },
      axisLine: { show: false },
      axisTick: { show: false },
    },
    {
      type: 'value',
      name: '活跃用户占比',
      min: 0,
      max: 100,
      axisLabel: { formatter: '{value}%', color: '#fff', fontSize: 12 },
      splitLine: { show: false },
      axisLine: { show: false },
      axisTick: { show: false },
      position: 'right',
    },
  ],
  series: [
    {
      name: '用户总数',
      type: 'bar',
      data: totalUsers.value,
      barWidth: 20,
      itemStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(0,255,128,0.7)' },   // 半透明绿
            { offset: 1, color: 'rgba(0,255,128,0.2)' }
          ]
        }
      }
    },
    {
      name: '活跃用户数',
      type: 'bar',
      data: activeUsers.value,
      barWidth: 20,
      itemStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(64,159,255,0.7)' },  // 半透明蓝
            { offset: 1, color: 'rgba(64,159,255,0.2)' }
          ]
        }
      }
    },
    {
      name: '活跃用户占比',
      type: 'line',
      yAxisIndex: 1,
      data: activeRate.value,
      smooth: true,
      itemStyle: { color: '#ffd700' },
      lineStyle: { color: '#ffd700' },
    },
  ],
})

const initChart = () => {
  if (!chartRef.value) return
  chart = echarts.init(chartRef.value)
  chart.setOption(chartOption.value)
  startTooltipAnimation()
}

const startTooltipAnimation = () => {
  const dataCount = categories.value.length
  if (!chart || dataCount === 0) return
  if (tooltipTimer) clearInterval(tooltipTimer)
  tooltipTimer = setInterval(() => {
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
    currentIndex = (currentIndex + 1) % dataCount
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000)
}

const handleResize = () => {
  chart?.resize()
}

const fetchChartData = async () => {
  const res = await ndgdqrhajlfx({ yearType: selectedOpt.value })
  if (res?.data && Array.isArray(res.data)) {
    // 本年/去年切换
    if (selectedOpt.value === 'administrative') {
      // 本年-城镇
      categories.value = res.data.map((item: any) => item.district)
      totalUsers.value = res.data.map((item: any) => item.urbanUserNumber ?? 0)
      activeUsers.value = res.data.map((item: any) => 0) // 无活跃用户数字段，设为0
      activeRate.value = res.data.map((item: any) => item.thisYearUrbanRate ?? 0)
    } else {
      // 去年-城镇
      categories.value = res.data.map((item: any) => item.district)
      totalUsers.value = res.data.map((item: any) => item.urbanUserNumber ?? 0)
      activeUsers.value = res.data.map((item: any) => 0)
      activeRate.value = res.data.map((item: any) => item.lastYearUrbanRate ?? 0)
    }
    chartOption.value.xAxis.data = categories.value
    chartOption.value.series[0].data = totalUsers.value
    chartOption.value.series[1].data = activeUsers.value
    chartOption.value.series[2].data = activeRate.value
    chart?.setOption(chartOption.value)
  }
}

onMounted(async () => {
  await fetchChartData()
  initChart()
  window.addEventListener('resize', handleResize)
})

watch(selectedOpt, async () => {
  await fetchChartData()
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) clearInterval(tooltipTimer)
})
</script>

<style scoped>
@import '@/styles/index.css';

.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}

.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
