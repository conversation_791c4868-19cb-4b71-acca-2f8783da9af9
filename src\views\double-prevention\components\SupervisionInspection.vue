<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">风险增长趋势</div>
      <div class="header-dropdown"></div>
    </div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { scyf_fxzzqs } from '@/common/api/doublePrevention'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1

const data = ref([
  {
    label: '第一季度',
    value: [
      { name: '低风险', value: 0 },
      { name: '一般风险', value: 0 },
      { name: '较大风险', value: 0 },
      { name: '重大风险', value: 0 },
    ],
  },
])
const color = ['#66FFFF', '#FFF04C', '#FF791A', '#FF4C4D']

// 获取风险增长趋势数据
const fetchRiskTrend = async () => {
  try {
    const response = await scyf_fxzzqs()
    if (response && response.data && response.data.length > 0) {
      // 转换接口数据为图表所需格式
      data.value = response.data.map(item => ({
        label: item.quarter || '',
        value: [
          { name: '低风险', value: item.low_risk || 0 },
          { name: '一般风险', value: item.general_risk || 0 },
          { name: '较大风险', value: item.greater_risk || 0 },
          { name: '重大风险', value: item.significant_risk || 0 },
        ],
      }))
      // 数据更新后重新渲染图表
      updateChart()
    }
  } catch (error) {
    console.error('获取风险增长趋势数据失败:', error)
  }
}

const getOption = () => ({
  color,
  grid: {
    top: 40,
    left: 24,
    bottom: 30,
    right: 8,
  },
  legend: {
    top: 0,
    inactiveBorderWidth: 0,
    textStyle: {
      color: '#fff',
    },
  },
  tooltip: {
    show: true,
    trigger: 'axis',
    axisPointer: {
      lineStyle: {
        color: '#FFF04C',
      },
    },
    backgroundColor: 'rgba(11, 46, 115, 0.6)',
    borderColor: '#409FFF',
    backdropFilter: 'blur(4px)',
    textStyle: {
      color: '#fff',
    },
    formatter: (p: any) => {
      if (p[0].dataIndex === 0 || p[0].dataIndex === data.value.length + 1) {
        return ''
      } else {
        return (
          p[0].axisValue +
          p
            .map(
              (i: any) =>
                `<br/><div style="display:inline-block;margin-right:4px;width:12px;height:12px;background:${i.color};border-radius: 50%;"></div><div style="display:inline-block;">${i.name}: ${i.value}</div>`,
            )
            .join('')
        )
      }
    },
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#fff',
        type: 'solid',
        opacity: 0.3,
      },
    },
    boundaryGap: false,
    data: ['', ...data.value.map(i => i.label), ''],
    axisLabel: {
      color: '#fff',
    },
  },
  yAxis: {
    type: 'value',
    name: '单位：个',
    nameTextStyle: {
      color: '#fff',
      align: 'left',
    },
    splitLine: {
      lineStyle: {
        color: '#fff',
        opacity: 0.3,
        type: 'dashed',
      },
    },
    axisLabel: {
      color: '#fff',
    },
  },
  series: data.value[0].value.map((item, index) => ({
    name: item.name,
    type: 'line',
    smooth: 0.5,
    symbol: 'none',
    symbolSize: 8,
    lineStyle: {
      color: color[index],
      width: 1,
    },
    itemStyle: {
      color: (p: any) => {
        if (p.dataIndex !== 0 && p.dataIndex !== data.value.length + 1) return color[index]
      },
      borderColor: '#fff',
      borderWidth: 1,
    },
    areaStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          { offset: 0, color: color[index] + '4D' },
          { offset: 1, color: color[index] + '00' },
        ],
      },
    },
    data: [
      data.value.reduce((a, b) => a + b.value[index].value, 0) / data.value.length,
      ...data.value.map(i => i.value[index].value),
      data.value.reduce((a, b) => a + b.value[index].value, 0) / data.value.length,
    ],
  })),
})

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  chart.setOption(getOption())
  startTooltipAnimation()
}

const updateChart = () => {
  if (!chart) return
  chart.setOption(getOption())
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const option = getOption()
  const dataCount = option.xAxis.data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  fetchRiskTrend()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
