<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Table,
  TableBody,
  // TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { getLatestAccidents, type LatestAccident } from '@/common/api/emergency'
import { toast } from 'vue-sonner'

const sourceData = ref<
  Array<{
    invoice: string
    paymentStatus: string
    totalAmount: string
    paymentMethod: string
  }>
>([])

const loading = ref(false)

// 获取最新事故列表数据
const fetchLatestAccidents = async () => {
  try {
    loading.value = true
    const response = await getLatestAccidents()

    if (response.data && Array.isArray(response.data.newestAccidentVOList)) {
      sourceData.value = response.data.newestAccidentVOList.map((item: LatestAccident) => ({
        invoice: item.name || '未知事故',
        paymentStatus: item.accidentTime || '未知时间',
        totalAmount: '', // API中没有金额字段，保持为空
        paymentMethod: item.cause || '未知原因',
      }))
    }
  } catch (error) {
    console.error('获取最新事故列表数据失败:', error)
    toast.error('获取最新事故列表数据失败')
    // 使用默认数据作为fallback
    sourceData.value = [
      {
        invoice: '门站水淹',
        paymentStatus: '2025-07-28 14:54:21',
        totalAmount: '$250.00',
        paymentMethod: '天气',
      },
      {
        invoice: '腐蚀高压管道微泄露',
        paymentStatus: '2025-07-02 15:25:50',
        totalAmount: '$150.00',
        paymentMethod: '腐蚀',
      },
      {
        invoice: '施工掘出管线',
        paymentStatus: '2025-05-23 10:23:15',
        totalAmount: '$350.00',
        paymentMethod: '施工',
      },
      {
        invoice: '网线刮断',
        paymentStatus: '2025-04-12 08:15:44',
        totalAmount: '$450.00',
        paymentMethod: '天气',
      },
      {
        invoice: '低压管道微泄漏',
        paymentStatus: '2025-02-21 14:42:44',
        totalAmount: '$550.00',
        paymentMethod: '腐蚀',
      },
    ]
  } finally {
    loading.value = false
  }
}

const scrollList = computed(() => [...sourceData.value, ...sourceData.value])
const tableContainerRef = ref<HTMLElement | null>(null)
const scrollTimer = ref<any>(null)
const scrollTop = ref(0)
const rowHeight = 48 // 每行高度为40px
const transitionEnabled = ref(true)

const startScrolling = () => {
  stopScrolling()
  scrollTimer.value = setInterval(() => {
    scrollTop.value += rowHeight

    if (scrollTop.value >= sourceData.value.length * rowHeight) {
      transitionEnabled.value = false
      scrollTop.value = 0
      setTimeout(() => {
        transitionEnabled.value = true
      }, 50)
    }
  }, 3000)
}

const stopScrolling = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value)
    scrollTimer.value = null
  }
}

onMounted(() => {
  fetchLatestAccidents()
  startScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.addEventListener('mouseenter', stopScrolling)
    container.addEventListener('mouseleave', startScrolling)
  }
})

onUnmounted(() => {
  stopScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.removeEventListener('mouseenter', stopScrolling)
    container.removeEventListener('mouseleave', startScrolling)
  }
})
</script>

<template>
  <div class="panel-container-col">
    <div class="panel-header">最新事故信息</div>
    <div class="p-4 panel-content">
      <Table class="w-full table-fixed">
        <colgroup>
          <col style="width: 40%" />
          <col style="width: auto" />
          <col style="width: 20%" />
        </colgroup>
        <TableHeader>
          <TableRow class="border-[#99D5FF]/30 hover:bg-transparent">
            <TableHead class="font-bold text-white">事故名称</TableHead>
            <TableHead class="font-bold text-white">发生时间</TableHead>
            <TableHead class="font-bold text-white">事故原因</TableHead>
          </TableRow>
        </TableHeader>
      </Table>
      <div class="overflow-hidden" :style="{ height: `${7 * rowHeight}px` }" ref="tableContainerRef">
        <Table class="w-full table-fixed">
          <colgroup>
            <col style="width: 40%" />
            <col style="width: auto" />
            <col style="width: 20%" />
          </colgroup>
          <TableBody
            :style="{
              transform: `translateY(-${scrollTop}px)`,
              transition: transitionEnabled ? 'transform 0.5s ease' : 'none',
            }"
          >
            <TableRow
              v-for="(item, index) in scrollList"
              :key="index"
              class="border-[#99D5FF]/30 hover:bg-[#99D5FF]/30"
              :style="{ height: `${rowHeight}px` }"
            >
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.invoice }}</TableCell>
              <TableCell>{{ item.paymentStatus }}</TableCell>
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.paymentMethod }}</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  </div>
</template>
<style scoped>
@import '@/styles/index.css';
</style>
