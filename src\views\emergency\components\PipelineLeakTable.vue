<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Table,
  TableBody,
  // TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { getPipelineLeakAlarms, type PipelineLeakAlarm } from '@/common/api/emergency'
import { toast } from 'vue-sonner'

const emit = defineEmits<{
  (e: 'click', value: any): void
}>()

const handleShowDialog = (record: any) => {
  emit('click', record)
}

const sourceData = ref<
  Array<{
    id: string
    time: string
    duration: string
    position: { lng: number; lat: number }
    device: string
  }>
>([])

const loading = ref(false)

// 获取管道泄露告警数据
const fetchPipelineLeakAlarms = async () => {
  try {
    loading.value = true
    const response = await getPipelineLeakAlarms({ pageNum: 1, pageSize: 10 })

    if (response.data && response.data.rows && Array.isArray(response.data.rows)) {
      sourceData.value = response.data.rows.map((item: PipelineLeakAlarm, index: number) => ({
        id: item.id || `LEAK${String(index + 1).padStart(3, '0')}`,
        time: item.deviceIdPosition?.[0]?.alarmTime || item.createdAt || '未知时间',
        duration: item.alarmDuration || '未知',
        position: {
          lng: item.deviceIdPosition?.[0]?.devicePositionX || 115.915,
          lat: item.deviceIdPosition?.[0]?.devicePositionY || 39.055,
        },
        device: item.deviceIdPosition?.[0]?.deviceNumber || '管网泄漏探测器',
      }))
    }
  } catch (error) {
    console.error('获取管道泄露告警数据失败:', error)
    toast.error('获取管道泄露告警数据失败')
    // 使用默认数据作为fallback
    sourceData.value = [
      {
        id: 'INV001',
        time: '2024-08-06 10:30:00',
        duration: '30分钟',
        position: { lng: 115.915, lat: 39.055 },
        device: '管网泄漏探测器-01',
      },
      {
        id: 'INV002',
        time: '2024-08-06 11:05:00',
        duration: '15分钟',
        position: { lng: 115.92, lat: 39.06 },
        device: '管网泄漏探测器-02',
      },
      {
        id: 'INV003',
        time: '2024-08-06 12:15:00',
        duration: '45分钟',
        position: { lng: 115.91, lat: 39.05 },
        device: '管网泄漏探测器-03',
      },
    ]
  } finally {
    loading.value = false
  }
}

const scrollList = computed(() => [...sourceData.value, ...sourceData.value])
const tableContainerRef = ref<HTMLElement | null>(null)
const scrollTimer = ref<any>(null)
const scrollTop = ref(0)
const rowHeight = 48 // 每行高度为40px
const transitionEnabled = ref(true)

const startScrolling = () => {
  stopScrolling()
  scrollTimer.value = setInterval(() => {
    scrollTop.value += rowHeight

    if (scrollTop.value >= sourceData.value.length * rowHeight) {
      transitionEnabled.value = false
      scrollTop.value = 0
      setTimeout(() => {
        transitionEnabled.value = true
      }, 50)
    }
  }, 3000)
}

const stopScrolling = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value)
    scrollTimer.value = null
  }
}

onMounted(() => {
  fetchPipelineLeakAlarms()
  startScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.addEventListener('mouseenter', stopScrolling)
    container.addEventListener('mouseleave', startScrolling)
  }
})

onUnmounted(() => {
  stopScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.removeEventListener('mouseenter', stopScrolling)
    container.removeEventListener('mouseleave', startScrolling)
  }
})
</script>
<template>
  <div class="panel-container">
    <div class="panel-header">管道泄漏告警</div>
    <div class="p-4 panel-content">
      <Table class="w-full table-fixed">
        <colgroup>
          <col style="width: 40%" />
          <col style="width: auto" />
          <col style="width: 20%" />
        </colgroup>
        <TableHeader>
          <TableRow class="border-[#99D5FF]/30 hover:bg-transparent">
            <TableHead class="font-bold text-white">管道编号</TableHead>
            <TableHead class="font-bold text-white">告警时间</TableHead>
            <TableHead class="font-bold text-white">操作</TableHead>
          </TableRow>
        </TableHeader>
      </Table>
      <div class="overflow-hidden" :style="{ height: `${4 * rowHeight}px` }" ref="tableContainerRef">
        <Table class="w-full table-fixed">
          <colgroup>
            <col style="width: 40%" />
            <col style="width: auto" />
            <col style="width: 20%" />
          </colgroup>
          <TableBody
            :style="{
              transform: `translateY(-${scrollTop}px)`,
              transition: transitionEnabled ? 'transform 0.5s ease' : 'none',
            }"
          >
            <TableRow
              v-for="(item, index) in scrollList"
              :key="index"
              class="border-[#99D5FF]/30 hover:bg-[#99D5FF]/30"
              :style="{ height: `${rowHeight}px` }"
            >
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.id }}</TableCell>
              <TableCell>{{ item.time }}</TableCell>
              <TableCell class="text-[#99D5FF] cursor-pointer" @click="handleShowDialog(item)">测算</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  </div>
</template>
<style scoped>
@import '@/styles/index.css';
</style>
