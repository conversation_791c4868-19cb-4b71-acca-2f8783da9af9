import request from '@/utils/request'

/**
 * 通用响应结构
 */
export interface ApiResponse<T = any> {
  code?: number
  message?: string
  msg?: string
  success?: boolean
  pageInfo?: any
  data?: T | T[]
}

// 请求体接口（根据实际字段调整）
export interface DeviceOverviewPayload {
  // ...根据接口实际需要添加字段
  [key: string]: any
}

// 重点防控点位上报数据
export interface HazardReportData {
  supervision_measures_count: number
  peripheral_monitoring_count: number
  key_prevention_points: number
  emergency_plans_count: number
  safety_evaluation_reports_count: number
  technical_data_count: number
}

// 重点防控点位比重
export interface LevelRatioData {
  proportion: string
  count: number
  part_type: number // 1:"重点保护区域" 2:"重点危险物放置区域" 3:"其他类型"
}

// 企业安全管理人员
export interface CorpSafetyManagerData {
  count: number
  township: string
  qualification_category: string
  issue_date: string
  name: string
}

// 施工台账
export interface ConstructionLedgerData {
  id: string
  project_id: string
  project_name: string
  construction_unit: string
  construction_type: string
  construction_adress: string
  plan_start_date: string
  plan_end_date: string
  construction_supervisor: string
  supervisor_phone: string
  longitude: number
  latitude: number
  report_proactively: boolean
  construction_report: boolean
  security_agreement_signed: boolean
  enterprise_inform_situation: boolean
  gas_side_people_phone: string
  gas_side_people: string
  file_url: string
  create_time: string
  create_by: string
  update_time: string
  update_by: string
}

// 企业重点防控点位排行
export interface CorpHazardRankData {
  proportion: string
  level: string
  count: number
  enterprise: string
  hazard_count: number
}

// 获取重点防控点位上报数据(饼图)
export const getHazardReport = (payload: DeviceOverviewPayload = {}) => {
  return request.post<ApiResponse<HazardReportData>>(
    `/service-api/exposureAPIS/path/major/report/FE626D1AB769CF4393E91139C4D06E54`,
    payload,
    {
      params: { applicationName: 'fxrq' }
    }
  )
}

// 获取重点防控点位上报数据(柱状图)
export const getHazardReportZhu = (payload: DeviceOverviewPayload = {}) => {
  return request.post<ApiResponse<HazardReportData>>(
    `/service-api/exposureAPIS/path/major/rank/B39A5994F1285AF234C6A895B04BF1EA`,
    payload,
    {
      params: { applicationName: 'fxrq' }
    }
  )
}

// 获取重点防控点位比重
export const getHazardLevelRatio = (payload: DeviceOverviewPayload = {}) => {
  return request.post<ApiResponse<LevelRatioData[]>>(
    `/service-api/exposureAPIS/path/major/rate/B39A5994F1285AF2FD7CD7B1AF04B526`,
    payload,
    {
      params: { applicationName: 'fxrq' }
    }
  )
}

// 获取企业安全管理人员
export const getCorpSafetyManager = (payload: DeviceOverviewPayload = {}) => {
  return request.post<ApiResponse<CorpSafetyManagerData[]>>(
    `/service-api/exposureAPIS/path/safety/management/D8E1F37433F834C7EE3AA94F7C74917BC3D65CC180C68427`,
    payload,
    {
      params: { applicationName: 'fxrq' }
    }
  )
}

// 获取企业重点防控点位排行
export const getCorpHazardRank = (payload: DeviceOverviewPayload = {}) => {
  return request.post<ApiResponse<CorpHazardRankData[]>>(
    `/service-api/exposureAPIS/path/enterprise/rank/EA97062B9DFC8C9DFE659CF47863AC97`,
    payload,
    {
      params: { applicationName: 'fxrq' }
    }
  )
}

// 获取施工台账
export const getConstructionLedger = (payload: DeviceOverviewPayload = {}) => {
  return request.post<ApiResponse<ConstructionLedgerData[]>>(
    `/service-api/exposureAPIS/path/construction/iedger/87927693D3B1F5855B00BBFEEED47464F24C7C6BB75F3990`,
    payload,
    {
      params: { applicationName: 'fxrq' }
    }
  )
}

// 获取重点防控点位
export const getHazardSource = (payload: DeviceOverviewPayload = {}) => {
  return request.post<ApiResponse<HazardReportData[]>>(
    `/service-api/exposureAPIS/path/major/point/19D8CF19F0A40AD1F9F7935D4DABE16C`,
    payload,
    {
      params: { applicationName: 'fxrq' }
    }
  )
}

// 获取重点防控点位周边监控
export const getSurroundingVideo = (payload: DeviceOverviewPayload = {}) => {
  return request.post<ApiResponse<any[]>>(
    `/service-api/exposureAPIS/path/enterprise/rank/EA97062B9DFC8C9DFE659CF47863AC97`,
    payload,
    {
      params: { applicationName: 'fxrq' }
    }
  )
}