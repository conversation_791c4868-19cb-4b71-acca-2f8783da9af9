### 应急管理 API 文档

接口名称 接口地址 入参说明 返回参数说明

- 应急资源 https://city189.cn:3612/prod-api/emResources/command/getResourcesNumbe 无入参

teamCount：应急队伍warehouseCount:物资仓库carCount:抢修车辆hospitalCount：医院fireFacilityCount：消防设施

- 最新资源入库 https://city189.cn:3612/prod-api/emResources/command/getNewstGoods 无入参

entryTime：入库时间name：标题warehouseName：所属仓库名称

- 应急预案分级统计 https://city189.cn:3612/prod-api/emResources/command/getPlanCountByType 无入参

level：等级number：数量

- 本年应急演练次数排行 https://city189.cn:3612/prod-api/emResources/command/getDrillCountByType 无入参

enterpriseName：企业名number：次数

- 管道泄露告警 https://gasscreen.city189.cn/gasModels/alarm/list?pageNum=1&pageSize=10 入参 "startDate": "开始日期", "endDate": "结束日期", "gasSectionNumber": "管段编号", "enterpriseName": "企业名称", "dealStatus": "处置状态", "pageNum": "页码", "pageSize": "页面大小" id：编号

出参deviceIdPosition（数组）：设备编号和位置 "deviceNumber": 设备编号, "devicePositionX": x坐标 "devicePositionY": y坐标 "alarmTime": 告警时间

leakPosition（数组）：漏点坐标

leakData（对象）：泄露数据 "leakPosition": 漏点坐标 "enterpriseId": 关联企业id, "phoneNumber": 联系方式, "pipeLine": 所属管线, "pipeSection": 所属管段, "beginSection": 开始管段坐标, "endSection": 结束管段坐标 explosionData（对象）：爆炸数据 "rtnExplosionReportStruct": 爆炸数据 "impactRadius": 影响半径, "affectedPopulation": 影响人口, "maxExplosionPressure": 最大压力, "pressureRiseRate": 压力上升速率, "populationDensity": 人口密度, "burnEnergy": 燃烧热值 "dataList": 画图数据坐标 "positionP": 压力坐标 "positionR": 位置坐标

diffusionData（对象）：扩散数据 "valueQ": 泄漏速率 "u": 风速 "windDirection": 风向角度 "stabilityClass": 环境稳定等级 "conditionStatus": 环境类型 "conditionStatusName": 环境类型名称 "url": 扩散图片存储地址 "area": 扩散面积 "length": 扩散距离 "range": 爆炸极限 "positionRightUp": 图片右上角坐标 "positionLeftDown": 图片左下角坐标 "affectedSection": 受影响管段 "id": 记录id "beginPosition": 管段开始坐标 "endPosition": 管段结束坐标 "fid": 管段id, "layer": 企业 "layerName": 企业简称 "enterpriseId": 关联企业id

emergencyData（对象）：应急数据 "reCar": 车辆 "id": "carNumber": 车牌 "carPerson": 姓名 "carPhone": 联系方式 "position": 坐标 "reFireFacility": 消防 "id": "name": 名称 "longitude": 坐标 "latitude": 坐标 "reTeam": 队伍 "id":  
 "teamName": 队伍名称 "teamPeople": 人员 "teamPhone": 联系方式 "position": 坐标 "reHospital": 医院 "id":  
 "name": 医院名称 "position": 坐标

userNature：用户类型

dealStatus：处置状态

alarmStatus：告警状态

alarmDuration：告警时长

enterprise（对象）：企业信息 "enterpriseId": 企业id "enterpriseName": 企业名称

handleList（数组）：处置情况gasAlarmMsgId：告警关联id person：处置人phone：电话content：内容pic：图片地址handleTime：处置时间

createdAt：创建时间

- 管道泄漏发生趋势 https://gasscreen.city189.cn/gasModels/alarm/leakTrend 无入参

month：月份monthNum：月份数据quarte：季度quarterNum：季度数据

- 最新事故列表 https://city189.cn:3612/prod-api/emResources/command/getNewestAccident 无入参

accidentTime：发生时间name：事故名称cause：事故原因
