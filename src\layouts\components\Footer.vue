<template>
  <div class="wrapper">
    <div class="flex justify-center gap-10 pt-3 pb-1">
      <div v-for="(item, idx) in eventList" :key="idx" class="flex-none scroll-item" @click="handleClick(item.url)">
        <div class="flex items-center">
          <img :src="item.img" alt="图片" class="w-[145px] h-[66px] object-cover" />
          <div class="flex flex-col pl-2 grow">
            <p class="text-title">{{ item.title }}</p>
          </div>
        </div>
      </div>
      <div class="flex-none scroll-item" @click="handleClick('add')">
        <div class="flex items-center">
          <div class="w-[145px] h-[66px] leading-[66px] text-center text-4xl bg-gray-400 text-black">+</div>
          <div class="flex flex-col pl-2 grow">
            <p class="text-title">新增网址</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <PipelineLeakDialog :open="showPipelineLeakDialog" @close="showPipelineLeakDialog = false" />
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import GasLeakImg from '@/assets/footer/gas-leak.png'
import PipelineLeakDialog from '@/views/emergency/PipelineLeakDialog.vue'
import type { AlarmInfo } from '@/types/alarmDialog.ts'
import type { EventInfo } from '@/types/footer.ts'

// 接收传入事件
const emit = defineEmits<{
  (e: 'onWarning', value: { isShow: boolean; id: string }): void
}>()

const statusMap: Record<number, string> = {
  0: '待处理',
  1: '处理中',
  2: '已处理',
}
const eventList = ref<EventInfo[]>([
  {
    title: '居民燃气泄漏',
    url: 'https://baidu.com',
    img: GasLeakImg,
  },
  {
    title: '居民燃气泄漏',
    url: 'https://baidu.com',
    img: GasLeakImg,
  },
  {
    title: '管道燃气泄漏',
    url: 'https://baidu.com',
    img: GasLeakImg,
  },
])
const showPipelineLeakDialog = ref(false)
const showAlarmDetailDialog = ref(false)
const alarmDetailInfo = ref<AlarmInfo | null>(null)

// 处理点击事件
const handleClick = (val: string) => {
  // 如果是url则跳转
  if (val !== 'add') {
    window.open(val, '_blank')
    return
  }
  // 打开新增网址对话框
}

onMounted(() => {
  // connectWS()
})

onUnmounted(() => {
  // 组件卸载时断开 WebSocket 连接
  // if (wsService) {
  //   wsService.disconnect()
  // }
})
</script>
<style scoped>
.wrapper {
  position: absolute;
  bottom: 24px;
  left: 24px;
  right: 24px;
  z-index: 100;
  border-radius: 8px;
  background: linear-gradient(180deg, rgba(11, 46, 115, 0) 0%, rgba(11, 46, 115, 0.6) 100%);
  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(
      180deg,
      rgba(153, 213, 255, 0) 0%,
      rgba(153, 213, 255, 0) 15%,
      rgba(153, 213, 255, 0.45) 100%
    )
    1;
  backdrop-filter: blur(8px);
}
.content {
  position: relative;
  margin: 0 auto;
  width: 100%;
  height: 100%;
  padding: 8px 32px;
  box-sizing: border-box;
  background: rgba(42, 56, 77, 0.6);
}
.scroll-btn {
  width: 16px;
  height: 16px;
  cursor: pointer;
  background-size: 100% 100%;
  border: none;
}
.left-btn {
  left: 8px;
  background: url(@/assets/footer/scroll-icon.png) no-repeat center;
}
.right-btn {
  transform: rotateZ(180deg);
  transform-origin: center;
  right: 8px;
  background: url(@/assets/footer/scroll-icon.png) no-repeat center;
}
.scroll-item {
  padding: 9px 20px 9px 35px;
  width: 410px;
  height: 84px;
  color: #fff;
  background:
    linear-gradient(90deg, rgba(64, 159, 255, 0.45) 5%, rgba(64, 159, 255, 0) 50%),
    linear-gradient(90deg, rgba(26, 102, 255, 0.6) 0%, rgba(26, 102, 255, 0) 98%);
  border-radius: 0 0 0 10px;
  box-sizing: border-box;
  cursor: pointer;
}
.text-title {
  font-family: Source Han Sans;
  font-size: 18px;
  font-weight: bold;
  line-height: 26px;
  letter-spacing: normal;
  background:
    linear-gradient(180deg, rgba(119, 168, 217, 0) 0%, rgba(119, 168, 217, 0) 20%, rgba(119, 168, 217, 0.6) 82%),
    #ffffff;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  text-shadow: 0px 0px 4px rgba(35, 79, 140, 0.6);
}
</style>
