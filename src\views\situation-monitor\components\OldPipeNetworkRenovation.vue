<template>
  <div class="panel-container">
    <div class="panel-header">供需分析</div>
    <div class="legend-container">
      <div class="legend-item">
        <span class="legend-color blue-series"></span>
        <span class="legend-text">供给</span>
      </div>
      <div class="legend-item">
        <span class="legend-color orange-series"></span>
        <span class="legend-text">需求</span>
      </div>
    </div>
    <div class="panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { gxfx } from '@/common/api/situationMonitor'

const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 新增：保存接口数据
const chartData = ref({
  gj_gdjm: 0,
  gj_pzqjm: 0,
  gj_gsy: 0,
  xq_gdjm: 0,
  xq_pzqjm: 0,
  xq_gsy: 0,
})

const initChart = () => {
  if (!chartRef.value) return
  chartInstance = echarts.init(chartRef.value)

  // 用接口数据生成图表数据
  const list = [
    { name: '管道居民\n用户', value: chartData.value.gj_gdjm },
    { name: '瓶装气居民\n用户', value: chartData.value.gj_pzqjm },
    { name: '工商业\n用户', value: chartData.value.gj_gsy },
  ]
  const list2 = [
    { name: '管道居民\n用户', value: chartData.value.xq_gdjm },
    { name: '瓶装气居民\n用户', value: chartData.value.xq_pzqjm },
    { name: '工商业\n用户', value: chartData.value.xq_gsy },
  ]
  const datas = ['管道居民\n用户', '瓶装气居民\n用户', '工商业\n用户']
  const maxValue = Math.max(
    chartData.value.gj_gdjm,
    chartData.value.gj_pzqjm,
    chartData.value.gj_gsy,
    chartData.value.xq_gdjm,
    chartData.value.xq_pzqjm,
    chartData.value.xq_gsy
  )
  const values = [maxValue, maxValue, maxValue]

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'rgba(255, 255, 255, 0.2)',
      borderWidth: 1,
      textStyle: { color: '#fff', fontSize: 12 },
      formatter: function (params: any) {
        if (params && params.length > 0) {
          let result = params[0].name + '<br/>'
          const blueValue = list.find(item => item.name === params[0].name)?.value || 0
          result += '<span style="color: #4DCB62;">●</span> 供给: ' + blueValue + '<br/>'
          const orangeValue = list2.find(item => item.name === params[0].name)?.value || 0
          result += '<span style="color: #FF791A;">●</span> 需求: ' + orangeValue
          return result
        }
        return ''
      },
    },
    xAxis: {
      splitLine: { show: false },
      axisLabel: { show: true, fontSize: 12, color: '#fff' },
      axisTick: { show: true },
      axisLine: { show: false },
    },
    grid: {
      containLabel: true,
      left: '3%',
      top: '5%',
      right: '8%',
      bottom: '5%',
    },
    yAxis: [
      {
        data: datas,
        inverse: true,
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false },
        axisLabel: { fontSize: 12, color: '#fff', lineHeight: 18 },
      },
    ],
    series: [
      {
        name: '供给底图',
        type: 'pictorialBar',
        itemStyle: { color: 'rgba(64, 159, 255, 0.3)', borderColor: 'transparent', borderWidth: 0 },
        symbolRepeat: 'fixed',
        symbolMargin: 3,
        symbol: 'rect',
        symbolClip: true,
        symbolSize: [7, 12],
        symbolPosition: 'start',
        symbolOffset: [0, -12],
        data: values,
        z: 2,
        silent: true,
        animationEasing: 'elasticOut',
      },
      {
        name: '需求底图',
        type: 'pictorialBar',
        itemStyle: { color: 'rgba(64, 159, 255, 0.3)', borderColor: 'transparent', borderWidth: 0 },
        symbolRepeat: 'fixed',
        symbolMargin: 3,
        symbol: 'rect',
        symbolClip: true,
        symbolSize: [7, 12],
        symbolPosition: 'start',
        symbolOffset: [0, 6],
        data: values,
        z: 2,
        silent: true,
        animationEasing: 'elasticOut',
      },
      {
        type: 'bar',
        barWidth: 15,
        silent: true,
        itemStyle: { color: 'transparent', borderColor: 'transparent' },
        data: list,
        z: 1,
      },
      {
        type: 'pictorialBar',
        animationDuration: 0,
        symbolRepeat: 'fixed',
        symbol: 'rect',
        symbolSize: [4, 15],
        symbolMargin: 2,
        symbolBoundingData: 2000,
        itemStyle: { color: 'transparent' },
        data: values,
        z: 0,
        animationEasing: 'elasticOut',
      },
      {
        type: 'pictorialBar',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0, 0, 0, 1,
            [
              { offset: 0, color: '#4DCB62' },
              { offset: 1, color: '#C0FFB3' },
            ],
            false,
          ),
          borderColor: 'transparent',
          borderWidth: 0,
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}',
          color: '#4DCB62',
          fontSize: 12,
          fontWeight: 'bold',
          offset: [8, -12],
        },
        symbolRepeat: 'fixed',
        symbolMargin: 3,
        symbol: 'rect',
        symbolClip: true,
        symbolSize: [7, 12],
        symbolPosition: 'start',
        symbolOffset: [0, -12],
        data: list,
        z: 3,
        animationEasing: 'elasticOut',
      },
      {
        type: 'bar',
        barGap: '-140%',
        data: values,
        barWidth: 16,
        itemStyle: {
          color: 'transparent',
          borderColor: 'transparent',
          borderWidth: 0,
          label: { show: false, position: 'top' },
        },
        z: 0,
      },
      {
        type: 'bar',
        barWidth: 30,
        itemStyle: { color: 'transparent', borderColor: 'transparent' },
        data: list2,
        z: 1,
      },
      {
        type: 'pictorialBar',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0, 0, 0, 1,
            [
              { offset: 0, color: '#FF791A' },
              { offset: 1, color: '#FFC61A' },
            ],
            false,
          ),
          borderColor: 'transparent',
          borderWidth: 0,
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}',
          color: '#FF791A',
          fontSize: 12,
          fontWeight: 'bold',
          offset: [8, 6],
        },
        symbolRepeat: 'fixed',
        symbolMargin: 3,
        symbol: 'rect',
        symbolClip: true,
        symbolSize: [7, 12],
        symbolPosition: 'start',
        symbolOffset: [0, 6],
        data: list2,
        z: 3,
        animationEasing: 'elasticOut',
      },
      {
        name: '外框2',
        type: 'bar',
        data: values,
        barWidth: 16,
        itemStyle: {
          color: 'transparent',
          borderColor: 'transparent',
          borderWidth: 0,
          label: { show: false, position: 'top' },
        },
        z: 0,
      },
    ],
  }

  chartInstance.setOption(option)
}

const handleResize = () => {
  chartInstance?.resize()
}

onMounted(() => {
  // 新增：调用供需分析接口并渲染图表
  gxfx().then(res => {
    if (Array.isArray(res.data) && res.data.length > 0) {
      const d = res.data[0]
      chartData.value.gj_gdjm = Number(d.gj_gdjm ?? 0)
      chartData.value.gj_pzqjm = Number(d.gj_pzqjm ?? 0)
      chartData.value.gj_gsy = Number(d.gj_gsy ?? 0)
      chartData.value.xq_gdjm = Number(d.xq_gdjm ?? 0)
      chartData.value.xq_pzqjm = Number(d.xq_pzqjm ?? 0)
      chartData.value.xq_gsy = Number(d.xq_gsy ?? 0)
      initChart()
    }
    console.log('gxfx response:', res)
  }).catch(err => {
    console.error('gxfx error:', err)
  })

  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
@import '@/styles/index.css';

.panel-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* .panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
} */

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 0.5px;
}

.legend-container {
  position: absolute;
  top: 60px;
  right: 300px;
  display: flex;
  gap: 12px;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background 0.3s ease;
}

.legend-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.legend-color {
  width: 16px;
  height: 8px;
  border-radius: 2px;
}

.legend-color.blue-series {
  background: linear-gradient(135deg, #4dcb62 0%, #c0ffb3 100%);
}

.legend-color.orange-series {
  background: linear-gradient(135deg, #ff791a 0%, #ffc61a 100%);
}

.legend-text {
  color: rgba(255, 255, 255, 0.85);
  font-size: 12px;
  font-weight: 500;
}

.panel-content {
  padding: 16px;
  height: calc(100% - 55px);
  background: rgba(0, 0, 0, 0.02);
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 280px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .panel-header {
    flex-direction: column;
    gap: 12px;
    padding: 14px 16px;
  }

  .legend-container {
    gap: 12px;
  }

  .panel-content {
    padding: 16px;
    height: calc(100% - 85px);
  }

  .chart-container {
    min-height: 280px;
  }

  .header-title {
    font-size: 15px;
  }

  .legend-text {
    font-size: 11px;
  }
}
</style>
