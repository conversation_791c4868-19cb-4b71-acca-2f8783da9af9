<template>
  <div class="panel-container">
    <div class="panel-header-row">
      <div class="panel-header">入户安检总览</div>
      <div class="header-dropdown">
        <div class="btn-group">
          <button
            class="period-btn"
            :class="{ active: selectedOpt === 'administrative' }"
            @click="selectedOpt = 'administrative'"
          >
            本年
          </button>
          <button
            class="period-btn"
            :class="{ active: selectedOpt === 'enterprise' }"
            @click="selectedOpt = 'enterprise'"
          >
            去年
          </button>
        </div>
      </div>
    </div>
    <div class="industry-grid">
      <div v-for="item in industryData" :key="item.id" class="industry-card">
        <!-- 数值区域 -->
        <div class="card-value-section" v-if="item.id !== 3&& item.id !== 6">
          <div class="value-number">{{ item.value }}</div>
          <div v-if="item.unit" class="value-unit">{{ item.unit }}</div>
        </div>

        <!-- 图效果 -->
        <div class="cylinder-container">
          <img v-if="item.id !== 3 && item.id !== 6" src="/src/assets/indoor-icon/aj-icon.svg" alt="" />
          <div v-else class="ring-container">
            <div class="ring-wrapper"></div>
            <div class="ring-center-text">
              <div class="value-number">{{ item.value }}</div>
              <div class="value-unit">{{ item.unit }}</div>
            </div>
          </div>
        </div>

        <!-- 标题 -->
        <div class="card-title">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { rhajzl } from '@/common/api/indoor'

const selectedOpt = ref<string>('administrative')
interface IndustryItem {
  id: number
  name: string
  value: any
  unit: string
  icon: string
}
const industryData = ref<IndustryItem[]>([]) // 明确类型为 IndustryItem 数组

const fetchData = async () => {
  try {
    const res = await rhajzl({ yearType: selectedOpt.value })
    const d = res?.data[0] || {}
console.log(d,11111);

    // 按钮选择态对应数据
    if (selectedOpt.value === 'administrative') {
      // 本年
      industryData.value = [
        {
          id: 1,
          name: '城镇居民阶段安检',
          value: d.thisYearUrbanResidentPatrolCount ?? 0,
          unit: '',
          icon: '/src/assets/indoor-icon/aj-icon.svg',
        },
        {
          id: 2,
          name: '城镇居民阶段已检',
          value: d.thisYearUrbanResidentPatrolCount ?? 0,
          unit: '',
          icon: '/src/assets/indoor-icon/aj-icon.svg',
        },
        {
          id: 3,
          name: '城镇居民阶段安检率',
          value: Math.round((d.thisYearUrbanPatrolRate ?? 0) * 100),
          unit: '%',
          icon: '/src/assets/indoor-icon/aj-icon.svg',
        },
        {
          id: 4,
          name: '农村居民阶段安检',
          value: d.thisYearRuralResidentPatrolCount ?? 0,
          unit: '',
          icon: '/src/assets/indoor-icon/aj-icon.svg',
        },
        {
          id: 5,
          name: '农村居民阶段已检',
          value: d.thisYearRuralResidentPatrolCount ?? 0,
          unit: '',
          icon: '/src/assets/indoor-icon/aj-icon.svg',
        },
        {
          id: 6,
          name: '农村居民阶段安检率',
          value: Math.round((d.thisYearRuralPatrolRate ?? 0) * 100),
          unit: '%',
          icon: '/src/assets/indoor-icon/aj-icon.svg',
        },
      ]
    } else {
      // 去年
      industryData.value = [
        {
          id: 1,
          name: '城镇居民阶段安检',
          value: d.urbanResidentUserNumber ?? 0,
          unit: '',
          icon: '/src/assets/indoor-icon/aj-icon.svg',
        },
        {
          id: 2,
          name: '城镇居民阶段已检',
          value: d.lastYearUrbanResidentPatrolCount ?? 0,
          unit: '',
          icon: '/src/assets/indoor-icon/aj-icon.svg',
        },
        {
          id: 3,
          name: '城镇居民阶段安检率',
          value: Math.round((d.lastYearUrbanPatrolRate ?? 0) * 100),
          unit: '%',
          icon: '/src/assets/indoor-icon/aj-icon.svg',
        },
        {
          id: 4,
          name: '农村居民阶段安检',
          value: d.ruralResidentUserNumber ?? 0,
          unit: '',
          icon: '/src/assets/indoor-icon/aj-icon.svg',
        },
        {
          id: 5,
          name: '农村居民阶段已检',
          value: d.lastYearRuralResidentPatrolCount ?? 0,
          unit: '',
          icon: '/src/assets/indoor-icon/aj-icon.svg',
        },
        {
          id: 6,
          name: '农村居民阶段安检率',
          value: Math.round((d.lastYearRuralPatrolRate ?? 0) * 100),
          unit: '%',
          icon: '/src/assets/indoor-icon/aj-icon.svg',
        },
      ]
    }
  } catch (err) {
    console.error('rhajzl接口调用失败:', err)
  }
}

onMounted(fetchData)
watch(selectedOpt, fetchData)
</script>
<style lang="scss" scoped>
@import '@/styles/index.css';

.panel-header-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px 0 10px;
  margin-bottom: 8px;
}

.panel-header {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}

.header-dropdown {
  display: flex;
  align-items: center;
}

.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}

.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}

.industry-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: auto auto;
}

.industry-card {
  width: 100%;
  height: 120px; // 增加卡片高度
  background: transparent;
  border: none;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 16px 0; // 增加内边距
  overflow: visible;
}

.card-value-section {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: baseline;
  margin-bottom: 4px;
  z-index: 10;

  .value-number {
    color: #ffffff;
    font-family: 'Noto Sans SC', sans-serif;
    font-size: 22px; // 增大字体
    line-height: 1;
    text-align: center;
    font-weight: bold;
    margin-right: 2px;
    margin-bottom: -24px;
  }

  .value-unit {
    color: #ffffff;
    font-family: 'Noto Sans SC', sans-serif;
    font-size: 14px; // 增大字体
    line-height: 1;
    text-align: center;
  }
}

.cylinder-container {
  position: relative;
  width: 80px; // 放大图标
  height: 80px;
  display: flex;
margin-top: -10px;
  align-items: center;
  justify-content: center;

  img {
    width: 80px;
    height: 80px;
  }

  .ring-container {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .ring-wrapper {
    width: 80px;
    height: 80px;
    background: url('/src/assets/indoor-icon/greenRing.svg') no-repeat;
    background-size: 100% 100%;
    position: relative;
  }

  .ring-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    animation: rotate 2s linear infinite;
    transform-origin: center center;
  }

  .ring-center-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: row;
    align-items: baseline;
    justify-content: center;
    z-index: 10;

    .value-number {
      color: #ffffff;
      font-family: 'Noto Sans SC', sans-serif;
      font-size: 22px; // 增大字体
      line-height: 1;
      text-align: center;
      font-weight: bold;
      margin-right: 2px;
    }

    .value-unit {
      color: #ffffff;
      font-family: 'Noto Sans SC', sans-serif;
      font-size: 14px; // 增大字体
      line-height: 1;
      text-align: center;
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

.card-title {
  color: #ffffff;
  font-family: 'Noto Sans SC', sans-serif;
  font-size: 14px; // 增大字体
  line-height: 1.2;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  margin-top: 10px;
  margin-bottom: 0;
}
</style>
