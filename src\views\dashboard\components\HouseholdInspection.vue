<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">入户安检</div>
      <div class="header-dropdown">
        <Select v-model:model-value="selectedOpt">
          <SelectTrigger class="text-sm dropdown-btn" size="sm">
            <SelectValue placeholder="统计周期" />
          </SelectTrigger>
          <SelectContent class="text-[#99D5FF]">
            <SelectGroup>
              <SelectItem value="2025">2025年</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div class="gauge-container">
        <div class="carousel">
          <div class="relative top-[-20px]" style="--i: 0">
            <div class="text-center">
              <span class="gauge-value">{{ householdData.households_plan }}</span>
              <span class="text-base">个</span>
            </div>
            <div class="gauge-label">安检计划</div>
            <div class="gauge-circle-bg"></div>
          </div>
          <div class="relative top-[20px]" style="--i: 1">
            <div class="text-center">
              <span class="gauge-value">{{ householdData.households_plan_finish }}</span>
              <span class="text-base">个</span>
            </div>
            <div class="gauge-label">完成计划</div>
            <div class="gauge-circle-bg"></div>
          </div>
          <div class="relative top-[-40px]" style="--i: 2">
            <div class="text-center">
              <span class="gauge-value">{{ completionRate }}</span>
              <span class="text-base">%</span>
            </div>
            <div class="gauge-label">安检完成率</div>
            <div class="gauge-circle-bg"></div>
          </div>
          <div class="relative top-[20px]" style="--i: 3">
            <div class="text-center">
              <span class="gauge-value">{{ householdData.patrol_troubles }}</span>
              <span class="text-base">个</span>
            </div>
            <div class="gauge-label">现存入户安检隐患</div>
            <div class="gauge-circle-bg"></div>
          </div>
          <div class="relative top-[-20px]" style="--i: 4">
            <div class="text-center">
              <span class="gauge-value">{{ formatUserCount(householdData.patrol_plan_users) }}</span>
              <span class="text-base">{{ getUserUnit(householdData.patrol_plan_users) }}</span>
            </div>
            <div class="gauge-label">安检用户</div>
            <div class="gauge-circle-bg"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  // SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { getHouseholdInspection, type HouseholdInspectionData } from '@/common/api/dashboard'

const selectedOpt = ref<string>('2025')

// 入户安检数据
const householdData = ref<HouseholdInspectionData>({
  households_plan: 0,
  households_plan_finish: 0,
  patrol_troubles: 0,
  patrol_plan_users: 0,
})

// 计算完成率
const completionRate = computed(() => {
  if (householdData.value.households_plan === 0) return '0.00'
  const rate = (householdData.value.households_plan_finish / householdData.value.households_plan) * 100
  return rate.toFixed(2)
})

// 格式化用户数量
const formatUserCount = (count: number) => {
  if (count >= 10000) {
    return (count / 10000).toFixed(2)
  }
  return count.toString()
}

// 获取用户数量单位
const getUserUnit = (count: number) => {
  if (count >= 10000) {
    return '万户'
  }
  return '户'
}

// 获取入户安检数据
const fetchHouseholdInspection = async () => {
  try {
    const response = await getHouseholdInspection({ year: selectedOpt.value })
    if (response && response.data) {
      const data = Array.isArray(response.data) ? response.data[0] : response.data
      if (data) {
        householdData.value = {
          households_plan: data.households_plan || 0,
          households_plan_finish: data.households_plan_finish || 0,
          patrol_troubles: data.patrol_troubles || 0,
          patrol_plan_users: data.patrol_plan_users || 0,
        }
      }
    }
  } catch (error) {
    console.error('获取入户安检数据失败:', error)
  }
}

onMounted(() => {
  fetchHouseholdInspection()
})
</script>

<style scoped>
@import '@/styles/index.css';

.header-title {
  color: white;
  font-size: 16px;
  font-weight: 500;
  flex: 1;
  margin-left: 8px;
}

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
}

.gauge-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100%;
  padding: 0 20px;
  background: url('@/assets/household-inspection/indicator-con-bg.png') no-repeat center;
  background-size: cover;
  perspective: 1000px; /* 设置透视效果 */
}

.carousel {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d; /* 保持 3D 变换 */
  /* 旋转动画 */
  animation: rotate-carousel 20s infinite linear;
}

.carousel > div {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%) rotateY(calc(var(--i) * 72deg)) translateZ(260px); /* 定位到圆环上 */
}

.gauge-circle-bg {
  width: 72px;
  height: 60px;
  background: url('@/assets/household-inspection/indicator-bg.png') no-repeat center;
  background-size: cover;
}

@keyframes rotate-carousel {
  from {
    transform: rotateY(0deg);
  }
  to {
    transform: rotateY(360deg);
  }
}

.gauge-value {
  color: #fff;
  font-family: DINPro;
  font-size: 24px;
  font-weight: bold;
  z-index: 1;
}

.gauge-label {
  color: #66ffff;
  font-size: 14px;
  text-align: center;
}
</style>
