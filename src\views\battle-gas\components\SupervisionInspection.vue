<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">气瓶类型分布</div>
    </div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { pzsyq_qplxfb } from '@/common/api/bottledGas'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
let tooltipTimer: ReturnType<typeof setInterval> | null = null
let currentIndex = -1

// 颜色数组
const colorList = [
  '#409FFF', // 蓝
  '#66FFFF', // 青
  '#FFC61A', // 黄
  '#FF7F50', // 橙
  '#7CFFB2', // 绿
  '#FF66B2', // 粉
  '#A366FF', // 紫
  '#FF6666', // 红
]

// 动态 option
const option = ref<any>({
  backgroundColor: 'transparent',
  title: {
    left: '49%',
    top: '38%',
    textAlign: 'center',
    text: '气瓶类型分布',
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    subtext: '',
    subtextStyle: {
      color: '#fff',
      fontSize: 24,
    },
  },
  legend: {
    show: true,
    orient: 'vertical',
    right: '0%',
    bottom: '10%',
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    itemWidth: 12,
    itemHeight: 8,
    icon: 'rect',
  },
  tooltip: { // 新增悬浮框
    show: true,
    trigger: 'item',
    formatter: (params: any) => {
      // params: {name, value, percent}
      return `
        <div>
          <strong>${params.name}</strong><br/>
          数量：${params.value} 个<br/>
          占比：${params.percent}%
        </div>
      `
    }
  },
  series: [
    {
      type: 'pie',
      radius: ['36.1%', '74.1%'],
      center: ['50%', '50%'],
      silent: true,
      data: [{ value: 1, itemStyle: { color: 'rgba(153, 213, 255, 0.15)' } }],
      label: { show: false },
      labelLine: { show: false },
    },
    {
      type: 'pie',
      radius: ['41.7%', '68.5%'],
      center: ['50%', '50%'],
      data: [],
      label: {
        show: true,
        color: '#fff',
        formatter: `{percent|{d}%}\n{value|{c}}个`,
        rich: {
          percent: { fontSize: 20, color: '#fff' },
          value: { fontSize: 12, color: '#fff' },
        },
      },
      labelLine: { show: true },
    },
  ],
})

const fetchChartData = async () => {
  const res = await pzsyq_qplxfb()
  const dataArr = Array.isArray(res.data) ? res.data : []
  option.value.title.text = '气瓶类型分布'
  // 分配不同颜色
  option.value.series[1].data = dataArr.map((item, idx) => ({
    value: item['num'] ?? 0,
    name: item['device_type_name'] ?? '--',
    itemStyle: { color: colorList[idx % colorList.length] },
  }))
  option.value.legend.data = dataArr.map(item => item['device_type_name'] ?? '--')
  option.value.title.subtext = dataArr.reduce((sum, item) => sum + (Number(item['num']) || 0), 0)
  renderChart()
}

const renderChart = () => {
  if (!chartRef.value) return
  if (!chart) chart = echarts.init(chartRef.value)
  chart.setOption(option.value, true)
  startTooltipAnimation()
}

const startTooltipAnimation = () => {
  const dataCount = option.value.series[1].data.length
  if (!chart || dataCount === 0) return
  if (tooltipTimer) clearInterval(tooltipTimer)
  tooltipTimer = setInterval(() => {
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 1,
      dataIndex: currentIndex,
    })
    currentIndex = (currentIndex + 1) % dataCount
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 1,
      dataIndex: currentIndex,
    })
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 1,
      dataIndex: currentIndex,
    })
  }, 3000)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  fetchChartData()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) clearInterval(tooltipTimer)
})
</script>

<style scoped>
@import '@/styles/index.css';

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
