<template>
  <div class="panel-container-col">
    <div class="panel-header">应急资源</div>
    <div class="flex flex-wrap justify-between gap-4 p-4">
      <div v-for="(item, idx) in indList" :key="idx" class="flex items-center justify-center flex-none gap-3 indicator">
        <div class="indicator-left">
          <span class="w-7 h-7 indicator-icon" :class="`icon-${item.id + 1}`"></span>
        </div>
        <div>
          <span class="text-[#66FFFF] text-sm">{{ item.name }}</span>
          <p>
            <span class="text-xl font-bold text-white">{{ item.value }}</span>
            <span class="text-sm text-white">{{ item.unit }}</span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getEmergencyResources, type EmergencyResourceData } from '@/common/api/emergency'
import { toast } from 'vue-sonner'

const indList = ref([
  { name: '应急监控', value: 0, unit: '个', id: 0 },
  { name: '应急队伍', value: 0, unit: '支', id: 1 },
  { name: '物资仓库', value: 0, unit: '个', id: 2 },
  { name: '抢修车辆', value: 0, unit: '辆', id: 3 },
  { name: '医院', value: 0, unit: '个', id: 4 },
  { name: '消防设施', value: 0, unit: '个', id: 5 },
])

const loading = ref(false)

// 获取应急资源数据
const fetchEmergencyResources = async () => {
  try {
    loading.value = true
    const response = await getEmergencyResources()

    if (response.data) {
      const data = response.data as EmergencyResourceData
      // 更新各项指标数据
      indList.value[1].value = data.teamCount || 0 // 应急队伍
      indList.value[2].value = data.warehouseCount || 0 // 物资仓库
      indList.value[3].value = data.carCount || 0 // 抢修车辆
      indList.value[4].value = data.hospitalCount || 0 // 医院
      indList.value[5].value = data.fireFacilityCount || 0 // 消防设施

      // 应急监控暂时使用总和或固定值，因为API文档中没有对应字段
      indList.value[0].value =
        data.teamCount + data.warehouseCount + data.carCount + data.hospitalCount + data.fireFacilityCount || 57152
    }
  } catch (error) {
    console.error('获取应急资源数据失败:', error)
    toast.error('获取应急资源数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchEmergencyResources()
})
</script>
<style scoped>
@import '@/styles/index.css';

.indicator {
  width: 348px;
  height: 119px;
  background: linear-gradient(
    270deg,
    rgba(64, 159, 255, 0) 0%,
    rgba(64, 159, 255, 0.15) 50%,
    rgba(64, 159, 255, 0) 100%
  );
  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(0deg, rgba(64, 159, 255, 0) 0%, rgba(64, 159, 255, 0.6) 49%, rgba(64, 159, 255, 0) 100%)
    1;
}
.indicator-left {
  width: 56px;
  height: 48px;
  display: flex;
  justify-content: center;
  background: url('@/assets/emergency/ind-icon-bg.png') no-repeat center bottom;
  background-size: 56px 28px;
}
.indicator-icon {
  display: inline-block;
  width: 28px;
  height: 28px;
  background-size: contain;
}
.icon-1 {
  background-image: url('@/assets/emergency/ind-1-1.png');
}
.icon-2 {
  background-image: url('@/assets/emergency/ind-1-2.png');
}
.icon-3 {
  background-image: url('@/assets/emergency/ind-2-1.png');
}
.icon-4 {
  background-image: url('@/assets/emergency/ind-2-2.png');
}
.icon-5 {
  background-image: url('@/assets/emergency/ind-3-1.png');
}
.icon-6 {
  background-image: url('@/assets/emergency/ind-3-2.png');
}
</style>
