<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">投诉类型分布</div>
      <div class="header-dropdown">
        <div class="btn-group">
          <button class="period-btn" :class="{ active: selectedType === 'complaint' }" @click="handleTypeChange('complaint')">
            投诉类型
          </button>
          <button class="period-btn" :class="{ active: selectedType === 'problem' }" @click="handleTypeChange('problem')">
            问题类型
          </button>
        </div>
      </div>
    </div>
    <div class="panel-content p-4">
      <div class="type-distribution-wrapper">
        <!-- 左侧饼图 -->
        <div class="chart-section">
          <div ref="container" class="chart-container"></div>
        </div>
        <!-- 右侧自定义图例 -->
        <div class="legend-section">
          <div 
            v-for="item in currentChartData" 
            :key="item.name" 
            class="legend-item"
          >
            <div class="legend-content">
              <div class="legend-left">
                <span class="legend-dot" :style="{ backgroundColor: item.color }"></span>
                <span class="legend-name">{{ item.name }}</span>
              </div>
              <span class="legend-value">{{ item.percentage }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { tsjsc_tslxfb } from '@/common/api/complaint'

const container = ref<HTMLDivElement | null>(null)
let chart: echarts.ECharts | null = null
const selectedType = ref<string>('complaint')

// 定义颜色方案
const colors = ['#5CB3FF', '#4A9EFF', '#66FFCC', '#FFD966', '#FF6B6B']

// 定义数据类型
interface ChartDataItem {
  name: string
  value: number
  color: string
  percentage?: string
}

// 不同类型的数据
const dataMap = ref<{
  complaint: ChartDataItem[]
  problem: ChartDataItem[]
}>({
  complaint: [],
  problem: []
})

// 加载数据
const loadData = async (classify: 'tslx' | 'wtlx') => {
  try {
    const response = await tsjsc_tslxfb({ classify })
    if (response.success && response.data && response.data.length > 0) {
      const data = response.data.map((item, index) => {
        // 处理百分比字符串，如 "50.00%" 或数字
        let ratioValue = 0
        if (typeof item.ratio === 'string') {
          ratioValue = parseFloat(item.ratio.replace('%', ''))
        } else {
          ratioValue = item.ratio ? parseFloat(item.ratio.toString()) * 100 : 0
        }
        return {
          name: item.type || '',
          value: item.num || 0,
          color: colors[index % colors.length],
          percentage: ratioValue.toFixed(1)
        }
      })
      
      if (classify === 'tslx') {
        dataMap.value.complaint = data
      } else {
        dataMap.value.problem = data
      }
      
      updateChart()
    }
  } catch (error) {
    console.error('加载投诉类型分布数据失败:', error)
  }
}

// 当前显示的数据
const currentChartData = computed(() => {
  const key = selectedType.value === 'complaint' ? 'complaint' : 'problem'
  return dataMap.value[key]
})

const init = () => {
  if (!container.value) return
  chart = echarts.init(container.value)
  updateChart()
}

// 调整颜色亮度的辅助函数
const adjustBrightness = (color: string, amount: number) => {
  const hex = color.replace('#', '')
  const r = Math.max(0, Math.min(255, parseInt(hex.substring(0, 2), 16) + amount))
  const g = Math.max(0, Math.min(255, parseInt(hex.substring(2, 4), 16) + amount))
  const b = Math.max(0, Math.min(255, parseInt(hex.substring(4, 6), 16) + amount))
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
}

// 为每个颜色创建径向渐变配置
const getGradientColor = (baseColor: string) => {
  const darkerColor = adjustBrightness(baseColor, -40)
  return {
    type: 'radial',
    x: 0.5,
    y: 0.5,
    r: 0.8,
    colorStops: [
      { offset: 0, color: baseColor },
      { offset: 1, color: darkerColor }
    ]
  }
}

const updateChart = () => {
  if (!chart) return
  const data = currentChartData.value
  
  // 如果没有数据，不更新图表
  if (!data || data.length === 0) return
  
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(11, 46, 115, 0.9)',
      borderColor: '#409FFF',
      borderWidth: 1,
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      formatter: '{b}: {c}% ({d}%)'
    },
    series: [
      // 内环 - 渐变颜色
      {
        name: '类型',
        type: 'pie',
        radius: ['45%', '75%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        itemStyle: {
          borderWidth: 5,
          borderColor: 'rgba(11, 46, 115, 0.8)'
        },
        emphasis: {
          label: {
            show: false
          },
          itemStyle: {
            shadowBlur: 15,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.6)'
          },
          scale: true,
          scaleSize: 4
        },
        data: data.map(item => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: getGradientColor(item.color)
          }
        }))
      },
      // 外环 - 半透明渐变版本（宽度较窄）
      {
        name: '外环',
        type: 'pie',
        radius: ['75%', '95%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        silent: true,
        itemStyle: {
          borderWidth: 4,
          borderColor: 'rgba(11, 46, 115, 0.8)'
        },
        data: data.map(item => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 0.8,
              colorStops: [
                { offset: 0, color: item.color + '50' },
                { offset: 1, color: adjustBrightness(item.color, -40) + '40' }
              ]
            }
          }
        }))
      }
    ]
  }
  
  chart.setOption(option)
}

const handleTypeChange = (type: string) => {
  selectedType.value = type
  const classify = type === 'complaint' ? 'tslx' : 'wtlx'
  loadData(classify)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  init()
  loadData('tslx') // 默认加载投诉类型数据
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
@import '@/styles/index.css';

.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}

.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}

.type-distribution-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.chart-section {
  flex: 1;
  height: 100%;
  min-width: 0;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 200px;
}

.legend-section {
  width: 240px;
  display: flex;
  flex-direction: column;
  gap: 7px;
  padding: 0;
}

.legend-item {
  position: relative;
  padding: 8px 12px;
  background: rgba(64, 159, 255, 0.08);
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  
  // 左侧边框 - 纯色不渐变
  border-left: 2px solid rgba(64, 159, 255, 0.5);
  
  // 上边框 - 从左到右渐变透明
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to right, rgba(64, 159, 255, 0.6), rgba(64, 159, 255, 0));
  }
  
  // 下边框 - 从左到右渐变透明
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to right, rgba(64, 159, 255, 0.6), rgba(64, 159, 255, 0));
  }
  
  &:hover {
    background: rgba(64, 159, 255, 0.12);
    border-left-color: rgba(64, 159, 255, 0.8);
    transform: translateX(2px);
    
    &::before,
    &::after {
      background: linear-gradient(to right, rgba(64, 159, 255, 0.9), rgba(64, 159, 255, 0));
    }
  }
}

.legend-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.legend-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.legend-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-name {
  color: #fff;
  font-size: 14px;
  font-weight: 300;
  white-space: nowrap;
}

.legend-value {
  color: #fff;
  font-size: 16px;
  font-weight: 400;
  font-family: 'DINPro', sans-serif;
}
</style>
