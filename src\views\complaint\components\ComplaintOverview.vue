<template>
  <div class="panel-container">
    <div class="panel-header">投诉事件总览</div>
    <div class="panel-content p-4">
      <div class="grid grid-cols-3 gap-4">
        <div v-for="item in items" :key="item.key" class="flex items-center gap-3 p-3 bg-white/0 rounded">
          <img :src="item.icon" alt="" class="w-15 h-15" />
          <div class="flex-1">
            <div class="text-sm text-[#66FFFF]/80">{{ item.label }}</div>
              <div class="text-2xl text-white font-semibold">{{ item.value }}</div>
              <div class="text-xs text-white/60">{{ item.sub }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import complaintsIcon from '@/assets/complaint/complaints-num.png'
import acceptanceIcon from '@/assets/complaint/acceptance-num.png'
import completedIcon from '@/assets/complaint/completed-num.png'
import transferIcon from '@/assets/complaint/transfer-num.png'
import returnVisitIcon from '@/assets/complaint/return-visit-num.png'
import archiveIcon from '@/assets/complaint/archive-num.png'
import { tsjsc_tssjzl } from '@/common/api/complaint'

// 定义API响应数据类型
interface OverviewData {
  ztsl?: number // 总投诉量
  byljtsl?: number // 本月累计条数
  slsl?: number // 受理数量
  sll?: string | number // 受理率
  bjsl?: number // 办结数量
  wcl?: string | number // 完成率
  zbsl?: number // 转办数量
  hfsl?: number // 回访数量
  gdsl?: number // 归档数量
}

const overviewData = ref<OverviewData>({})

// 计算展示的数据项
const items = computed(() => [
  { 
    key: 'complaints', 
    label: '投诉量', 
    value: overviewData.value.ztsl || 0, 
    sub: `本月累计 ${overviewData.value.byljtsl || 0} 条`, 
    icon: complaintsIcon 
  },
  { 
    key: 'acceptance', 
    label: '受理量', 
    value: overviewData.value.slsl || 0, 
    sub: `受理率 ${overviewData.value.sll || 0}%`, 
    icon: acceptanceIcon 
  },
  { 
    key: 'completed', 
    label: '办结量', 
    value: overviewData.value.bjsl || 0, 
    sub: `完成率 ${overviewData.value.wcl || 0}%`, 
    icon: completedIcon 
  },
  { 
    key: 'transfer', 
    label: '转办量', 
    value: overviewData.value.zbsl || 0, 
    sub: '', 
    icon: transferIcon 
  },
  { 
    key: 'return', 
    label: '回访量', 
    value: overviewData.value.hfsl || 0, 
    sub: '', 
    icon: returnVisitIcon 
  },
  { 
    key: 'archive', 
    label: '归档量', 
    value: overviewData.value.gdsl || 0, 
    sub: '', 
    icon: archiveIcon 
  },
])

// 加载数据
const loadData = async () => {
  try {
    const response = await tsjsc_tssjzl()
    if (response.success && response.data) {
      overviewData.value = response.data[0]
    }
  } catch (error) {
    console.error('加载投诉事件总览数据失败:', error)
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
@import '@/styles/index.css';
</style>
