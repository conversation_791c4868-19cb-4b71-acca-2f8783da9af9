import request from '@/utils/request'
import requestOur from '@/utils/request_our'
import axios from 'axios'
// 请求体接口
export interface DoublePreventionPayload {
  [key: string]: any
}

// 风险概况响应数据接口
export interface RiskOverviewResponse {
  code?: number
  message?: string
  data?: {
    general_risk?: number // 一般风险
    significant_risk?: number // 重大风险
    low_risk?: number // 低风险
    greater_risk?: number // 较大风险
  }
}

// 风险增长趋势响应数据接口
export interface RiskTrendResponse {
  code?: number
  message?: string
  data?: Array<{
    quarter?: string // 季度
    general_risk?: number // 一般风险
    significant_risk?: number // 重大风险
    low_risk?: number // 低风险
    greater_risk?: number // 较大风险
  }>
}

// 风险点位清单响应数据接口
export interface RiskPointListResponse {
  code?: number
  message?: string
  data?: Array<{
    id?: number
    district?: string // 所属区域
    report_time?: string // 上报日期
    risk_name?: string // 风险名称
    enterprise_name?: string // 所属企业
  }>
}

/**
 * 双重预防-风险概况
 */
export function scyf_fxgk(payload: DoublePreventionPayload = {}): Promise<RiskOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/scyf_fxgk/BD69D575558D4A0510572864F44AAA7F',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 双重预防-风险增长趋势
 */
export function scyf_fxzzqs(payload: DoublePreventionPayload = {}): Promise<RiskTrendResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/scyf_fxzzqs/BD69D575558D4A05A38BCA291F85EBDB4DB02F93383C0102',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 双重预防-风险点位清单
 * @param payload - 入参：{ enterprise: 企业名称 }
 */
export function scyf_fxdwqd(payload: { enterprise?: string } = {}): Promise<RiskPointListResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/scyf_fxdwqd/BD69D575558D4A051125D9649CC8E1F64DB02F93383C0102',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

// 隐患来源与整改统计响应数据接口
export interface HazardSourceResponse {
  code?: number
  message?: string
  data?: {
    households_not_rectified?: number // 入户安检未整改数量
    patrol_rectified?: number // 巡查巡检已整改数量
    patrol_not_rectified?: number // 巡查巡检未整改数量
    households_rectified?: number // 入户安检已整改数量
  }
}

// 企业隐患与整改统计响应数据接口
export interface EnterpriseHazardResponse {
  code?: number
  message?: string
  data?: Array<{
    name?: string // 企业名称
    total?: number // 隐患总量
    rectified?: number // 整改总量
  }>
}

// 隐患治理列表响应数据接口
export interface HazardManagementListResponse {
  code?: number
  message?: string
  data?: Array<{
    id?: number
    area?: string // 所属区域
    report_time?: string // 上报日期
    hidden_trouble_name?: string // 隐患名称
    enterprise_name?: string // 所属企业
  }>
}

/**
 * 双重预防-隐患来源与整改统计
 */
export function scyf_yhlyyzgtj(payload: DoublePreventionPayload = {}): Promise<HazardSourceResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/scyf_yhlyyzgtj/BD69D575558D4A050CFB3AEE253B7F7B30DC18C463273BC2',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 双重预防-企业隐患与整改统计（本月）
 */
export function scyf_byqyyhyzgtj(payload: DoublePreventionPayload = {}): Promise<EnterpriseHazardResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/scyf_byqyyhyzgtj/BD69D575558D4A05D1C4DF900A1153D587AC37682BA12380',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 双重预防-企业隐患与整改统计（本年）
 */
export function scyf_bnqyyhyzgtj(payload: DoublePreventionPayload = {}): Promise<EnterpriseHazardResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/scyf_bnqyyhyzgtj/BD69D575558D4A05EBA6D07CFCEFCCA687AC37682BA12380',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 双重预防-隐患治理列表
 * @param payload - 入参：{ enterprise: 企业名称 }
 */
export function scyf_yhzllb(payload: { enterprise?: string } = {}): Promise<HazardManagementListResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/fxrq/scyf_yhzllb/BD69D575558D4A05A35E9D8DF49EF23B4DB02F93383C0102',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

// 风险详情响应数据接口
export interface RiskDetailResponse {
  code?: number
  msg?: string
  data?: {
    id?: number
    name?: string // 风险名称
    enterpriseId?: number // 企业ID
    type?: string // 风险类型
    level?: string // 风险等级
    district?: string // 所属区域
    discoveryTime?: string // 发现时间
    status?: string // 状态
    processTime?: string // 处理时间
    measures?: string // 处理措施
  }
}

/**
 * 双重预防-风险详情
 * @param id - 风险ID
 */
export function getRiskDetail(id: number | string): Promise<RiskDetailResponse> {
  // return requestOur.get(`/prod-api/gasPlatform/prevention/risk/supervision/detail/${id}`)
  return axios.get(`https://city189.cn:3611/prod-api/gasPlatform/prevention/risk/supervision/detail/${id}`)
}

