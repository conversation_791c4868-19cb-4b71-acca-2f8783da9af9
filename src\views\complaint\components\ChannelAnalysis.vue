<template>
  <div class="panel-container">
    <div class="panel-header">投诉渠道分析</div>
    <div class="panel-content p-4">
      <div class="channel-analysis-wrapper">
        <!-- 左侧饼图 -->
        <div class="chart-section">
          <div ref="container" class="chart-container"></div>
        </div>
        <!-- 右侧自定义图例 -->
        <div class="legend-section">
          <div 
            v-for="item in chartData" 
            :key="item.name" 
            class="legend-item"
          >
            <div class="legend-content">
              <div class="legend-left">
                <span class="legend-dot" :style="{ backgroundColor: item.color }"></span>
                <span class="legend-name">{{ item.name }}</span>
              </div>
              <span class="legend-value">{{ item.percentage }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { onMounted, onBeforeUnmount, ref, computed } from 'vue'
import { tsjsc_tsqdfx } from '@/common/api/complaint'

const container = ref<HTMLDivElement | null>(null)
let chart: echarts.ECharts | null = null

// 定义颜色方案
const colors = ['#5CB3FF', '#4A9EFF', '#66FFCC', '#FFD966', '#FF6B6B']

// 定义数据类型
interface ChartDataItem {
  name: string
  value: number
  color: string
  percentage?: string
}

// 图表数据
const chartData = ref<ChartDataItem[]>([])

// 计算百分比
const totalValue = computed(() => chartData.value.reduce((sum, item) => sum + item.value, 0))

// 加载数据
const loadData = async () => {
  try {
    const response = await tsjsc_tsqdfx()
    if (response.success && response.data && response.data.length > 0) {
      chartData.value = response.data.map((item, index) => {
        const value = item.num || 0
        // 处理百分比字符串，如 "26.00%" 或数字
        let ratioValue = 0
        if (typeof item.ratio === 'string') {
          ratioValue = parseFloat(item.ratio.replace('%', ''))
        } else {
          ratioValue = item.ratio ? parseFloat(item.ratio.toString()) : 0
        }
        return {
          name: item.source || '', // 使用 source 字段作为渠道名称
          value: value,
          color: colors[index % colors.length],
          percentage: ratioValue.toFixed(1)
        }
      })
      // 重新初始化图表
      init()
    }
  } catch (error) {
    console.error('加载投诉渠道分析数据失败:', error)
  }
}

const init = () => {
  if (!container.value) return
  chart = echarts.init(container.value)

  // 调整颜色亮度的辅助函数
  const adjustBrightness = (color: string, amount: number) => {
    const hex = color.replace('#', '')
    const r = Math.max(0, Math.min(255, parseInt(hex.substring(0, 2), 16) + amount))
    const g = Math.max(0, Math.min(255, parseInt(hex.substring(2, 4), 16) + amount))
    const b = Math.max(0, Math.min(255, parseInt(hex.substring(4, 6), 16) + amount))
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
  }

  // 为每个颜色创建径向渐变配置
  const getGradientColor = (baseColor: string) => {
    const darkerColor = adjustBrightness(baseColor, -40)
    return {
      type: 'radial',
      x: 0.5,
      y: 0.5,
      r: 0.8,
      colorStops: [
        { offset: 0, color: baseColor },
        { offset: 1, color: darkerColor }
      ]
    }
  }

  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(11, 46, 115, 0.9)',
      borderColor: '#409FFF',
      borderWidth: 1,
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      formatter: '{b}: {c} ({d}%)'
    },
    series: [
      // 内环 - 渐变颜色
      {
        name: '渠道',
        type: 'pie',
        radius: ['30%', '75%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        itemStyle: {
          borderWidth: 3,
          borderColor: 'rgba(11, 46, 115, 0.8)'
        },
        emphasis: {
          label: {
            show: false
          },
          itemStyle: {
            shadowBlur: 15,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.6)'
          },
          scale: true,
          scaleSize: 5
        },
        data: chartData.value.map(item => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: getGradientColor(item.color)
          }
        }))
      },
      // 外环 - 半透明渐变版本（宽度较窄）
      {
        name: '外环',
        type: 'pie',
        radius: ['75%', '95%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        silent: true,
        itemStyle: {
          borderWidth: 2,
          borderColor: 'rgba(11, 46, 115, 0.8)'
        },
        data: chartData.value.map(item => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 0.8,
              colorStops: [
                { offset: 0, color: item.color + '50' },
                { offset: 1, color: adjustBrightness(item.color, -40) + '40' }
              ]
            }
          }
        }))
      }
    ]
  }

  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  loadData()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
@import '@/styles/index.css';

.channel-analysis-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.chart-section {
  flex: 1;
  height: 100%;
  min-width: 0;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 200px;
}

.legend-section {
  width: 240px;
  display: flex;
  flex-direction: column;
  gap: 7px;
  padding: 0;
}

.legend-item {
  position: relative;
  padding: 8px 12px;
  background: rgba(64, 159, 255, 0.08);
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  
  // 左侧边框 - 纯色不渐变
  border-left: 2px solid rgba(64, 159, 255, 0.5);
  
  // 上边框 - 从左到右渐变透明
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to right, rgba(64, 159, 255, 0.6), rgba(64, 159, 255, 0));
  }
  
  // 下边框 - 从左到右渐变透明
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to right, rgba(64, 159, 255, 0.6), rgba(64, 159, 255, 0));
  }
  
  &:hover {
    background: rgba(64, 159, 255, 0.12);
    border-left-color: rgba(64, 159, 255, 0.8);
    transform: translateX(2px);
    
    &::before,
    &::after {
      background: linear-gradient(to right, rgba(64, 159, 255, 0.9), rgba(64, 159, 255, 0));
    }
  }
}

.legend-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.legend-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.legend-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-name {
  color: #fff;
  font-size: 14px;
  font-weight: 300;
  white-space: nowrap;
}

.legend-value {
  color: #fff;
  font-size: 16px;
  font-weight: 400;
  font-family: 'DINPro', sans-serif;
}
</style>
