<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">预警事件月度分析</div>
      <!-- <div class="header-dropdown">
        <Select v-bind:model-value="selectedOpt">
          <SelectTrigger class="text-sm dropdown-btn" size="sm">
            <SelectValue placeholder="选择月份" />
          </SelectTrigger>
          <SelectContent class="text-[#99D5FF]">
            <SelectGroup>
              <SelectItem value="1">一月</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div> -->
    </div>
    <div class="p-4 panel-content">
      <div class="w-full h-full">
        <div ref="chartRef" class="chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  // SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { getSafetyAssessmentWarningEventsMonthlyAnalysis } from '@/common/api/safetyAssessment'

// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1
const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

const selectedOpt = ref('')

const months = ref<string[]>([])
const highRisk = ref<number[]>([])
const mediumRisk = ref<number[]>([])
const lowRisk = ref<number[]>([])
const higherRisk = ref<number[]>([])
const totalCount = ref<number[]>([])

// 构建图表配置
const buildOption = () => {
  return {
    color: ['#FF6B6B', '#FFD166', '#4ECDC4', '#FFA500'], // 高/较高/中/低
    grid: {
      top: '15%',
      left: 40,
      right: 20,
      bottom: 0,
      containLabel: true,
    },
    legend: {
      data: ['高风险', '较高风险', '中风险', '低风险'],
      textStyle: { color: '#fff', fontSize: 14 },
      itemWidth: 20,
      itemHeight: 10,
      icon: 'rect',
      itemGap: 24,
      top: 0,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { lineStyle: { color: '#47EBEB' } },
      backgroundColor: 'rgba(11, 46, 115, 0.6)',
      borderColor: '#409FFF',
      backdropFilter: 'blur(4px)',
      textStyle: { color: '#fff' },
    },
    xAxis: {
      type: 'category',
      data: months.value,
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { color: '#ffffff', fontSize: 12 },
    },
    yAxis: {
      type: 'value',
      name: '单位：件',
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { color: '#ffffff', fontSize: 12 },
      splitLine: { show: true, lineStyle: { color: 'rgba(255, 255, 255, 0.1)', type: 'dashed' } },
    },
    series: [
      {
        name: '高风险',
        type: 'bar',
        stack: 'total',
        data: highRisk.value,
        itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(255, 107, 107, 0.8)' }, { offset: 1, color: 'rgba(255, 107, 107, 0.3)' }]) },
        barWidth: 20,
      },
      {
        name: '较高风险',
        type: 'bar',
        stack: 'total',
        data: higherRisk.value,
        itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(255, 166, 0, 0.8)' }, { offset: 1, color: 'rgba(255, 166, 0, 0.3)' }]) },
      },
      {
        name: '中风险',
        type: 'bar',
        stack: 'total',
        data: mediumRisk.value,
        itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(78, 205, 196, 0.8)' }, { offset: 1, color: 'rgba(78, 205, 196, 0.3)' }]) },
      },
      {
        name: '低风险',
        type: 'bar',
        stack: 'total',
        data: lowRisk.value,
        itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(153, 178, 255, 0.8)' }, { offset: 1, color: 'rgba(153, 178, 255, 0.3)' }]) },
      },
    ],
  }
}

// 拉取并适配接口数据（入参：startTime/endTime，格式示例“2025年09月”）
const fetchAnalysis = async () => {
  try {
    const fmt = (d: Date) => `${d.getFullYear()}年${String(d.getMonth() + 1).padStart(2, '0')}月`
    const now = new Date()
    const start = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const params = { startTime: fmt(start), endTime: fmt(now) }

    const res: any = await getSafetyAssessmentWarningEventsMonthlyAnalysis(params)
    const list: any[] = Array.isArray(res?.data) ? res.data : Array.isArray(res) ? res : []

    months.value = list.map(it => String(it?.monthDay ?? ''))
    // 注意字段名按照给定文档使用（包含“hign/higner”拼写）
    highRisk.value = list.map(it => Number(it?.hignRiskCount ?? 0))
    higherRisk.value = list.map(it => Number(it?.hignerRiskCount ?? 0))
    mediumRisk.value = list.map(it => Number(it?.mediumRiskCount ?? 0))
    lowRisk.value = list.map(it => Number(it?.lowRiskCount ?? 0))
    totalCount.value = list.map(it => Number(it?.count ?? 0))

    chart?.setOption(buildOption(), true)
    startTooltipAnimation()
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error('获取预警事件月度分析失败:', e)
  }
}

/* 使用 buildOption 动态生成配置（见上） */

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(buildOption())
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = months.value.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  fetchAnalysis()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  // 清理所有定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>
