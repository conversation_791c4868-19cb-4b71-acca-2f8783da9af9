<template>
  <div class="panel-container">
    <div class="panel-header">风险概况</div>
    <div class="industry-grid">
      <div v-for="(item, idx) in industryData" :key="item.id" class="industry-card">
        <!-- 数值区域 -->
        <div class="card-value-section" v-if="item.type !== 'rate'">
          <div class="value-number">{{ item.value }}</div>
          <div v-if="item.unit" class="value-unit">{{ item.unit }}</div>
        </div>

        <!-- 图效果 -->
        <div class="cylinder-container">
          <img v-if="item.type !== 'rate'" :src="`/src/assets/double-prevention/${item.icon}`" alt="" />
          <div v-else class="ring-container">
            <div class="ring-wrapper">
              <img :src="`/src/assets/double-prevention/${item.ringImg}`" alt="" class="ring-img" />
            </div>
            <div class="ring-center-text">
              <div class="value-number">{{ item.value }}</div>
              <div class="value-unit">{{ item.unit }}</div>
            </div>
          </div>
        </div>

        <!-- 标题 -->
        <div class="card-title" :class="`card-title-color${idx + 1}`">
          <img :src="`/src/assets/double-prevention/title-bg${idx + 1}.svg`" alt="" class="card-title-bg" />
          <span class="card-title-text">{{ item.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { scyf_fxgk } from '@/common/api/doublePrevention'

const industryData = ref([
  {
    id: 3,
    name: '低风险（个）',
    value: 0,
    unit: '',
    type: 'rate',
    ringImg: 'blueRing.png',
    icon: '',
  },
  {
    id: 4,
    name: '一般风险（个）',
    value: 0,
    unit: '',
    type: 'rate',
    ringImg: 'yellowRing.png',
    icon: '',
  },
  {
    id: 5,
    name: '较大风险（个）',
    value: 0,
    unit: '',
    type: 'rate',
    ringImg: 'orangeRing.png',
    icon: '',
  },
  {
    id: 6,
    name: '重大风险（个）',
    value: 0,
    unit: '',
    type: 'rate',
    ringImg: 'redRing.png',
    icon: '',
  },
])

// 获取风险概况数据
const fetchRiskOverview = async () => {
  try {
    const response = await scyf_fxgk()
    if (response && response.data) {
      const data = response.data
      // 更新数据
      industryData.value[0].value = data.low_risk || 0
      industryData.value[1].value = data.general_risk || 0
      industryData.value[2].value = data.greater_risk || 0
      industryData.value[3].value = data.significant_risk || 0
    }
  } catch (error) {
    console.error('获取风险概况数据失败:', error)
  }
}

onMounted(() => {
  fetchRiskOverview()
})
</script>
<style lang="scss" scoped>
@import '@/styles/index.css';

.industry-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 16px;
  padding: 30px 20px 20px 20px;
}

.industry-card {
  width: 100%;
  max-width: 200px;
  height: 200px;
  background: transparent;
  border: none;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 20px 0;
  overflow: visible;
}

.card-value-section {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: baseline;
  margin-bottom: 5px;
  z-index: 10;

  .value-number {
    color: #ffffff;
    font-family: 'Noto Sans SC', sans-serif;
    font-size: 24px;
    line-height: 1;
    text-align: center;
    font-weight: bold;
    margin-right: 2px;
    margin-bottom: -60px;
  }

  .value-unit {
    color: #ffffff;
    font-family: 'Noto Sans SC', sans-serif;
    font-size: 16px;
    line-height: 1;
    text-align: center;
  }
}

.cylinder-container {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 5px 0;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 120px;
    height: 120px;
    object-fit: contain;
    display: block;
  }

  .ring-container {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .ring-wrapper {
    width: 120px;
    height: 120px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: rotate-ring 6s linear infinite;
  }

  .ring-img {
    width: 120px;
    height: 120px;
    object-fit: contain;
    display: block;
    pointer-events: none;
  }

  .ring-center-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: row;
    align-items: baseline;
    justify-content: center;
    z-index: 10;

    .value-number {
      color: #ffffff;
      font-family: 'Noto Sans SC', sans-serif;
      font-size: 24px;
      line-height: 1;
      text-align: center;
      font-weight: bold;
      margin-right: 2px;
    }

    .value-unit {
      color: #ffffff;
      font-family: 'Noto Sans SC', sans-serif;
      font-size: 16px;
      line-height: 1;
      text-align: center;
    }
  }

  @keyframes rotate-ring {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}

.card-title {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-family: 'Noto Sans SC', sans-serif;
  font-size: 12px;
  line-height: 1.2;
  font-weight: 350;
  text-align: center;
  white-space: nowrap;
  margin-top: auto;
  margin-left: 10px;
  margin-bottom: 0;

  min-height: 32px;
}

.card-title-bg {
  position: absolute;
  left: 45%;
  top: 50%;
  width: 120%;
  height: 120%;
  transform: translate(-50%, -50%);
  z-index: 0;
  pointer-events: none;
}

.card-title-text {
  position: relative;
  z-index: 1;
  padding: 0 8px;
}

/* 4种不同颜色，可根据实际需要调整 */
.card-title-color1 {
  color: #66ffff;
}
.card-title-color2 {
  color: #fff04c;
}
.card-title-color3 {
  color: #ff791a;
}
.card-title-color4 {
  color: #ff4c4d;
}
</style>
