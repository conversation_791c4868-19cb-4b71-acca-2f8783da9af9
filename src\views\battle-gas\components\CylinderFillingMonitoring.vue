<template>
  <div class="panel-container">
    <div class="panel-header">气瓶状态分析</div>
    <div class="industry-grid">
      <div v-for="item in industryData" :key="item.id" class="industry-card">
        <!-- 数值区域 -->
        <div class="card-value-section">
          <div class="value-number">{{ item.value }}</div>
          <div v-if="item.unit" class="value-unit">{{ item.unit }}</div>
        </div>
        <!-- 图效果 -->
        <div class="cylinder-container">
          <img
            :src="item._hovered ? '/src/assets/battle-gas/select.svg' : '/src/assets/battle-gas/unselect.svg'"
            alt=""
            :class="['cylinder-img', { selected: item._hovered }]"
            @mouseenter="item._hovered = true"
            @mouseleave="item._hovered = false"
          />
        </div>
        <!-- 标题 -->
        <div class="card-title">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { reactive, onMounted, onUnmounted } from 'vue'
import { pzsyq_qpztfx } from '@/common/api/bottledGas'

interface IndustryItem {
  id: number
  name: string
  value: any
  unit: string
  _hovered: boolean
}

const industryData = reactive<IndustryItem[]>([])

let carouselTimer: ReturnType<typeof setInterval> | null = null
let currentIndex = 0

const startCarousel = () => {
  if (carouselTimer) clearInterval(carouselTimer)
  carouselTimer = setInterval(() => {
    industryData.forEach((item, idx) => {
      item._hovered = idx === currentIndex
    })
    currentIndex = (currentIndex + 1) % industryData.length
  }, 2000)
}

onMounted(() => {
  // 接口获取数据并替换 industryData
  pzsyq_qpztfx().then(res => {
    if (Array.isArray(res.data) && res.data.length > 0) {
      const obj = res.data[0]
      // 清空原数据
      industryData.splice(0, industryData.length)
      Object.keys(obj).forEach((key, idx) => {
        industryData.push({
          id: idx + 1,
          name: key,
          value: obj[key],
          unit: '',
          _hovered: false,
        })
      })
    }
    startCarousel()
  })
})

onUnmounted(() => {
  if (carouselTimer) clearInterval(carouselTimer)
})
</script>
<style lang="scss" scoped>
@import '@/styles/index.css';

.industry-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr; // 横向4个模块
  gap: 16px;
  padding: 50px 20px 20px 30px;
}

.industry-card {
  width: 100%;
  max-width: 200px;
  height: 200px;
  background: transparent;
  border: none;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 20px 0;
  overflow: visible;
}

.card-value-section {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: baseline;
  margin-bottom: 5px;
  z-index: 10;

  .value-number {
    color: #ffffff;
    font-family: 'Noto Sans SC', sans-serif;
    font-size: 24px;
    line-height: 1;
    text-align: center;
    font-weight: bold;
    margin-right: 2px;
    margin-bottom: -60px;
  }

  .value-unit {
    color: #ffffff;
    font-family: 'Noto Sans SC', sans-serif;
    font-size: 16px;
    line-height: 1;
    text-align: center;
  }
}

.cylinder-img {
  transition:
    filter 0.3s,
    opacity 0.3s,
    transform 0.3s;
}
.cylinder-img.selected {
  filter: drop-shadow(0 0 16px #49d1af) brightness(1.1);
  opacity: 1;
  transform: scale(1.08);
  z-index: 2;
}
.cylinder-img:hover {
  filter: drop-shadow(0 0 8px #49d1af) brightness(1.1);
  opacity: 0.95;
}
.cylinder-container {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 5px 0;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 120px;
    height: 120px;
  }

  .ring-container {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .ring-wrapper {
    width: 120px;
    height: 120px;
    background: url('/src/assets/indoor-icon/greenRing.svg') no-repeat;
    background-size: 100% 100%;
    position: relative;
  }

  .ring-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    animation: rotate 2s linear infinite;
    transform-origin: center center;
  }

  .ring-center-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: row;
    align-items: baseline;
    justify-content: center;
    z-index: 10;

    .value-number {
      color: #ffffff;
      font-family: 'Noto Sans SC', sans-serif;
      font-size: 24px;
      line-height: 1;
      text-align: center;
      font-weight: bold;
      margin-right: 2px;
    }

    .value-unit {
      color: #ffffff;
      font-family: 'Noto Sans SC', sans-serif;
      font-size: 16px;
      line-height: 1;
      text-align: center;
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

.card-title {
  color: #ffffff;
  font-family: 'Noto Sans SC', sans-serif;
  font-size: 12px;
  line-height: 1.2;
  font-weight: lighter;
  text-align: center;
  white-space: nowrap;
  margin-top: -90px;
}
</style>
