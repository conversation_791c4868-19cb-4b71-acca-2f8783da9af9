<template>
  <Dialog :open="props.open" @update:open="handleUpdateOpen">
    <DialogOverlay class="bg-[#2A384D]/20 backdrop-blur-sm" />
    <DialogContent
      class="sm:max-w-[800px] p-0 bg-[#2A384D]/60 rounded-sm border-[#47EBEB] backdrop-blur-sm outline-none"
    >
      <DialogHeader>
        <DialogTitle class="h-12 text-white pl-6 leading-12 title-bg">设备详情</DialogTitle>
        <DialogDescription as="div" class="p-6 text-sm text-white">
          <div class="device-detail-box">
            <div class="content-grid">
              <div class="label">行政区划：</div>
              <div class="value">{{ displayedData.address ?? '--' }}</div>
              <div class="label">企业名称：</div>
              <div class="value">{{ displayedData.name ?? '--' }}</div>
              <div class="label">用户姓名：</div>
              <div class="value">{{ displayedData.user ?? '--' }}</div>
              <div class="label">燃气用户号：</div>
              <div class="value">{{ displayedData.gas_user_number ?? '--' }}</div>
            </div>
          </div>
        </DialogDescription>
      </DialogHeader>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogOverlay,
} from '@/components/ui/dialog'

const props = defineProps<{
  open: boolean
  data?: Record<string, any> | null
}>()
const emit = defineEmits<{
  (e: 'close'): void
}>()

// 直接使用 computed 读取 props.data，保证随父组件更新而响应
const displayedData = computed(() => props.data ?? {})

const handleUpdateOpen = (open: boolean) => {
  if (!open) {
    emit('close')
  }
}
</script>

<style scoped>
.title-bg {
  background: url('@/assets/dialog/title-bg-1200.png') no-repeat 0 0;
  background-size: cover;
  font-family: MStiffHei PRC;
}
.device-detail-box {
  padding: 12px;
  border-radius: 6px;
}
.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.left-title {
  color: #e6f4ff;
  font-weight: 600;
  font-size: 14px;
}
.right-company {
  color: #e6f4ff;
  font-size: 13px;
}
.content-grid {
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: 10px 18px;
  /* 改为顶部对齐，保证左侧 label 与右侧 value 第一行齐平 */
  align-items: flex-start;
}
.label {
  color: #99d5ff;
  font-weight: 600;
}
.value {
  color: #e6f4ff;
  word-break: break-word;
}
@media (max-width: 800px) {
  .content-grid {
    grid-template-columns: 1fr;
    /* 在单列布局下保持顶部对齐 */
    align-items: flex-start;
  }
  .header-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
