<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <link rel="preload" href="/fonts/MStiffHeiPRC-Bold.woff2" as="font" type="font/woff2" crossorigin />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script src="/app-config.js"></script>
    <script>
      (function () {
        try {
          var cfg = window.APP_CONFIG && window.APP_CONFIG.assistant;
          if (!cfg || cfg.enabled === false) return;
          var src = cfg.baseUrl + (cfg.id ? ('?id=' + encodeURIComponent(cfg.id)) : '');
          var s = document.createElement('script');
          s.async = true;
          s.defer = true;
          s.id = 'sqlbot-assistant-float-script-' + (cfg.id || 'default');
          s.src = src;
          document.head.appendChild(s);
        } catch (e) {
          console && console.warn && console.warn('assistant script load failed', e);
        }
      })();
    </script>
    <title>肥乡区城乡燃气监管信息平台</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
