<template>
  <div class="panel-container-col">
    <div class="panel-header">管道气站点设备监测</div>
    <div class="p-4 panel-content">
      <div class="status-indicators">
        <div class="status-item animate-pulse">
          <div class="status-value">{{ companyA }}</div>
          <div class="status-label">{{ companyLabelA }}</div>
        </div>
        <div class="status-item animate-pulse">
          <div class="status-value">{{ companyB }}</div>
          <div class="status-label">{{ companyLabelB }}</div>
        </div>
        <div class="status-item animate-pulse">
          <div class="status-value">{{ companyC }}</div>
          <div class="status-label">{{ companyLabelC }}</div>
        </div>
      </div>
      <div class="chart-section">
        <div ref="chartRef" class="donut-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { czsb_gdqzdsbjc, czsb_gdqzdsbjc_xq } from '@/common/api/runMonitor'
import { createSignedPayload } from '@/common/utils/auth'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
let currentIndex = -1

// 三个公司统计的响应式值（模板中显示）
const companyA = ref<number>(8) // 中石油昆仑燃气
const companyB = ref<number>(8) // 新奥燃气
const companyC = ref<number>(7) // 华港燃气
// 新增：三个公司的显示名称，优先使用接口返回的 type 字段
const companyLabelA = ref<string>('')
const companyLabelB = ref<string>('')
const companyLabelC = ref<string>('')
const rawDeviceMonitorDetail = ref<any>(null) // 新增：存储管道气站点设备监测详情的原始返回数据
// 请根据实际项目把 key 放到安全的配置文件或环境变量中，这里为演示使用占位符
const API_KEY = '9900J7T2J91992'

// 已删除本地 mock 数据，颜色保留用于后端数据渲染
let color = ['#47EBEB', '#99B2FF', '#FFC61A']

// 修改：默认 option 不再依赖本地 mock，xAxis/series 初始为空，tooltip 更稳健
const option = {
  color,
  grid: {
    top: 40,
    left: 24,
    bottom: 30,
    right: 8,
  },
  legend: {
    top: 0,
    inactiveBorderWidth: 0,
    textStyle: {
      color: '#fff',
    },
  },
  tooltip: {
    show: true,
    trigger: 'axis',
    axisPointer: {
      lineStyle: {
        color: '#47EBEB',
      },
    },
    backgroundColor: 'rgba(11, 46, 115, 0.6)',
    borderColor: '#409FFF',
    backdropFilter: 'blur(4px)',
    textStyle: {
      color: '#fff',
    },
    formatter: (p: any) => {
      if (!p || !p.length) return ''
      const axisVal = p[0].axisValue ?? ''
      return (
        axisVal +
        p
          .map((i: any) => {
            const name = i.seriesName ?? i.name ?? ''
            return `<br/><div style="display:inline-block;margin-right:4px;width:12px;height:12px;background:${i.color};border-radius: 50%;"></div><div style="display:inline-block;">${name}: ${i.value}</div>`
          })
          .join('')
      )
    },
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#fff',
        type: 'solid',
        opacity: 0.3,
      },
    },
    boundaryGap: false,
    data: [], // 运行时由接口填充
  },
  yAxis: {
    type: 'value',
    name: '单位：Mpa',
    nameTextStyle: {
      color: '#fff',
      align: 'left',
    },
    splitLine: {
      lineStyle: {
        color: '#fff',
        opacity: 0.3,
        type: 'dashed',
      },
    },
    axisLabel: {
      color: '#fff',
    },
  },
  series: [], // 初始无数据，接口返回后填充
}

const initchart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  chart.setOption(option)
  startHighlightAnimation()
}

// 修改：高亮轮播从图表当前 option 动态获取数据长度，避免引用已删除的 mock
const startHighlightAnimation = () => {
  if (!chart) return

  // 从当前图表 option 获取首个系列的数据长度（容错处理）
  const currentOpt: any = chart.getOption ? (chart.getOption() as any) : null
  const dataCount =
    currentOpt && Array.isArray(currentOpt.series) && currentOpt.series[0] && Array.isArray(currentOpt.series[0].data)
      ? currentOpt.series[0].data.length
      : 0

  if (dataCount === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮（如果存在）
    if (currentIndex >= 0) {
      chart?.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: currentIndex,
      })
    }

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

// 新增：调用管道气站点设备监测详情接口并按 date/type 聚合，更新图表
const fetchDeviceMonitorDetail = async () => {
  try {
    const signed = createSignedPayload({}, API_KEY)
    const res = await czsb_gdqzdsbjc_xq(signed)
    const body = res?.data ?? res
    rawDeviceMonitorDetail.value = body

    // 提取数组
    let list: any[] | null = null
    if (Array.isArray(body)) list = body
    else if (Array.isArray(body?.data)) list = body.data
    else if (Array.isArray(body?.result)) list = body.result

    if (!Array.isArray(list) || list.length === 0) {
      console.debug('管道气站点设备监测详情未返回数组，已保存原始返回：', body)
      return
    }

    const getDate = (it: any) => String(it.date ?? it.day ?? it.datetime ?? '').trim()
    const getType = (it: any) => String(it.type ?? it.name ?? '').trim()
    const getNum = (it: any) => Number(it.num ?? it.value ?? 0)

    // 去重并排序日期
    const dates = Array.from(new Set(list.map(getDate))).filter(Boolean)
    dates.sort((a, b) => new Date(a.replace(/\//g, '-')).getTime() - new Date(b.replace(/\//g, '-')).getTime())

    // 去重类型
    const types = Array.from(new Set(list.map(getType))).filter(Boolean)

    // 构建 series（每个 type 一条线）
    const series = types.map((t: string, idx: number) => {
      const values = dates.map((d: string) =>
        list
          .filter((it: any) => getDate(it) === d && getType(it) === t)
          .reduce((s: number, it: any) => s + getNum(it), 0),
      )
      return {
        name: t,
        type: 'line',
        smooth: 0.5,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: { color: color[idx % color.length] },
        itemStyle: { color: color[idx % color.length], borderColor: '#fff', borderWidth: 1 },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: color[idx % color.length] + '4D' },
              { offset: 1, color: color[idx % color.length] + '00' },
            ],
          },
        },
        data: values,
      }
    })

    // 生成 newOpt 并刷新图表（直接用 dates）
    const newOpt: any = JSON.parse(JSON.stringify(option))
    newOpt.xAxis = newOpt.xAxis || {}
    newOpt.xAxis.data = dates
    newOpt.series = series

    // 修正 tooltip
    newOpt.tooltip = {
      show: true,
      trigger: 'axis',
      axisPointer: { lineStyle: { color: color[0] } },
      backgroundColor: 'rgba(11, 46, 115, 0.6)',
      borderColor: '#409FFF',
      backdropFilter: 'blur(4px)',
      textStyle: { color: '#fff' },
      formatter: (p: any) => {
        if (!p || !p.length) return ''
        return (
          (p[0].axisValue ?? '') +
          p
            .map(
              (i: any) =>
                `<br/><div style="display:inline-block;margin-right:4px;width:12px;height:12px;background:${i.color};border-radius: 50%;"></div><div style="display:inline-block;">${i.seriesName}: ${i.value}</div>`,
            )
            .join('')
        )
      },
    }

    // 打印 dates 便于调试
    console.log('管道气站点设备监测详情 横坐标（dates）:', dates)

    chart?.setOption(newOpt, true)
    console.debug('管道气站点设备监测详情已应用到图表，types:', types)
  } catch (err) {
    console.error('获取管道气站点设备监测详情失败', err)
  }
}

// 新增：调用后端接口并更新三个公司统计值
const fetchAndApplyDeviceMonitor = async () => {
  try {
    const signed = createSignedPayload({}, API_KEY)
    const res = await czsb_gdqzdsbjc(signed)
    const body = res?.data ?? res

    let list: any[] | null = null
    if (Array.isArray(body)) {
      list = body
    } else if (Array.isArray(body?.data)) {
      list = body.data
    } else if (Array.isArray(body?.result)) {
      list = body.result
    }

    if (Array.isArray(list) && list.length > 0) {
      // 优先处理后端提供的 { type, num } 结构（使用 type 显示 label，num 显示数值）
      const hasTypeNum = list.every(
        i => i && (i.type !== undefined || i.name !== undefined) && (i.num !== undefined || i.value !== undefined),
      )

      if (hasTypeNum) {
        // 清空标记，后续按匹配或顺序填充未匹配项
        const filled = { A: false, B: false, C: false }
        // 先按 type 完整匹配已知公司名称
        list.forEach((it: any) => {
          const t = (it.type ?? it.name ?? '').toString()
          const n = Number(it.num ?? it.value ?? 0)
          if (t.includes('中石油') || t.includes('昆仑')) {
            companyA.value = n
            companyLabelA.value = t
            filled.A = true
          } else if (t.includes('新奥')) {
            companyB.value = n
            companyLabelB.value = t
            filled.B = true
          } else if (t.includes('华港')) {
            companyC.value = n
            companyLabelC.value = t
            filled.C = true
          }
        })
        // 将未匹配的项按顺序填充到剩余槽位（并使用 type 作为 label）
        const unmatched = list.filter(it => {
          const t = (it.type ?? it.name ?? '').toString()
          return !(t.includes('中石油') || t.includes('昆仑') || t.includes('新奥') || t.includes('华港'))
        })
        let uIndex = 0
        if (!filled.A && unmatched[uIndex]) {
          companyA.value = Number(unmatched[uIndex].num ?? unmatched[uIndex].value ?? companyA.value)
          companyLabelA.value = String(unmatched[uIndex].type ?? unmatched[uIndex].name ?? companyLabelA.value)
          uIndex++
        }
        if (!filled.B && unmatched[uIndex]) {
          companyB.value = Number(unmatched[uIndex].num ?? unmatched[uIndex].value ?? companyB.value)
          companyLabelB.value = String(unmatched[uIndex].type ?? unmatched[uIndex].name ?? companyLabelB.value)
          uIndex++
        }
        if (!filled.C && unmatched[uIndex]) {
          companyC.value = Number(unmatched[uIndex].num ?? unmatched[uIndex].value ?? companyC.value)
          companyLabelC.value = String(unmatched[uIndex].type ?? unmatched[uIndex].name ?? companyLabelC.value)
          uIndex++
        }
      } else {
        // 兼容原有逻辑：优先按名字匹配字段（兼容后端返回的 name/title/key）
        const findValueByName = (arr: any[], candidates: string[]) => {
          for (const c of candidates) {
            const it = arr.find(i => (i.name ?? i.title ?? i.label ?? i.type ?? '').toString().includes(c))
            if (it) return Number(it.value ?? it.num ?? it.count ?? it.total ?? 0)
          }
          return undefined
        }

        const a = findValueByName(list, ['中石油', '昆仑', 'zhongshiyou', 'kunlun'])
        const b = findValueByName(list, ['新奥', 'xinao'])
        const c = findValueByName(list, ['华港', 'huagang'])

        if (a !== undefined) companyA.value = a
        if (b !== undefined) companyB.value = b
        if (c !== undefined) companyC.value = c

        // 若没有按名找到，则按顺序取前三项作为兜底（仅更新数值，不改变默认标签）
        if (a === undefined || b === undefined || c === undefined) {
          const first3 = list.slice(0, 3)
          if (first3[0]) companyA.value = Number(first3[0].value ?? first3[0].num ?? first3[0].count ?? companyA.value)
          if (first3[1]) companyB.value = Number(first3[1].value ?? first3[1].num ?? first3[1].count ?? companyB.value)
          if (first3[2]) companyC.value = Number(first3[2].value ?? first3[2].num ?? first3[2].count ?? companyC.value)
        }
      }
    } else if (body && typeof body === 'object') {
      // 有时后端会返回单个对象包含统计字段，尝试直接映射常见字段名或嵌套结构
      companyA.value = Number(body?.zhongshiyou ?? body?.zhongshiyou_count ?? companyA.value)
      companyB.value = Number(body?.xinao ?? body?.xinao_count ?? companyB.value)
      companyC.value = Number(body?.huagang ?? body?.huagang_count ?? companyC.value)
      // 对象模式下保留默认标签，或可根据字段名替换（如需要）
    } else {
      console.warn('管道气站点设备监测接口返回格式未知，未更新统计', res)
    }
  } catch (err) {
    console.error('获取管道气站点设备监测失败', err)
  }
}

onMounted(() => {
  initchart()
  // 在生命周期中调用接口并更新视图
  fetchAndApplyDeviceMonitor()
  // 新增：调用详情接口并渲染图表
  fetchDeviceMonitorDetail()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.panel-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-indicators {
  width: 100%;
  height: 104px;
  display: flex;
  justify-content: space-around;
}

.status-item {
  display: flex;
  width: 31%;
  background: url('@/assets/run-monitor/device-overview-icon.png') no-repeat center center;
  background-size: 120px 60px;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(64, 159, 255, 0) 0%, #409fff 49%, rgba(64, 159, 255, 0) 100%) 1;
}

.status-value {
  font-family: YouSheBiaoTiHei;
  font-size: 24px;
  font-weight: bold;
  line-height: 28px;
  text-align: center;
  color: #fff;
  white-space: nowrap;
}

.status-label {
  font-family: NotoSansSC;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  text-align: center;
  letter-spacing: normal;
  color: #fff;
}

.chart-section {
  width: 100%;
  height: 258px;
}

.donut-chart {
  width: 100%;
  height: 100%;
}
</style>
