<template>
  <div class="panel-container-half-lr">
    <div class="panel-header">区域车辆数排行</div>
    <div class="p-4 panel-content list">
      <div v-for="(item, i) in items" :key="i" class="item">
        <div class="item-head">
          <div class="label">{{ item.label }}</div>
          <div class="val">{{ item.value }}</div>
        </div>
        <div class="bar">
          <div class="fill" :style="{ width: `${item.percent}%` }">
            <span class="split"></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { yscljsc_qyclsph } from '@/common/api/transportVehicles'

const rawData = ref<any[]>([])

// 计算最大值用于百分比计算
const maxValue = computed(() => {
  if (rawData.value.length === 0) return 1
  return Math.max(...rawData.value.map(item => item.area_num || 0))
})

// 处理后的列表数据
const items = computed(() => {
  return rawData.value.map(item => ({
    label: item.area || '这是对应内容',
    value: item.area_num || 0,
    percent: maxValue.value > 0 ? Math.round((item.area_num / maxValue.value) * 100) : 0,
  }))
})

// 获取区域车辆数排行数据
const fetchRegionalVehicleRanking = async () => {
  try {
    const response = await yscljsc_qyclsph()
    if (response.data && Array.isArray(response.data)) {
      rawData.value = response.data
    } else {
      // 如果没有数据，使用默认值
      rawData.value = Array.from({ length: 8 }).map((_, i) => ({
        area: '这是对应内容',
        area_num: 33340 - i * 2000,
      }))
    }
  } catch (error) {
    console.error('获取区域车辆数排行失败:', error)
    // 出错时使用默认值
    rawData.value = Array.from({ length: 8 }).map((_, i) => ({
      area: '这是对应内容',
      area_num: 33340 - i * 2000,
    }))
  }
}

onMounted(() => {
  fetchRegionalVehicleRanking()
})
</script>

<style scoped>
@import '@/styles/index.css';
.list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 4px;
}
/* 自定义滚动条样式 */
.list::-webkit-scrollbar {
  width: 4px;
}
.list::-webkit-scrollbar-track {
  background: rgba(64, 159, 255, 0.1);
  border-radius: 2px;
}
.list::-webkit-scrollbar-thumb {
  background: rgba(153, 213, 255, 0.4);
  border-radius: 2px;
}
.list::-webkit-scrollbar-thumb:hover {
  background: rgba(153, 213, 255, 0.6);
}
.item-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2px;
  gap: 8px;
}
.label {
  color: #e7f2ff;
  font-size: 14px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.val {
  color: #ffc61a;
  font-weight: 700;
  font-size: 14px;
  flex-shrink: 0;
  white-space: nowrap;
}
.bar {
  width: 100%;
  height: 18px;
  background: rgba(64, 159, 255, 0.18);
  border: 1px solid rgba(64, 159, 255, 0.35);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}
.fill {
  display: block;
  height: calc(100% - 4px);
  margin: 2px 0;
  background: linear-gradient(90deg, #ffe27a 0%, #ffc61a 100%);
  border-radius: 2px;
  position: relative;
  transition: width 0.4s ease;
}
.split {
  position: absolute;
  right: 0;
  top: -3px;
  width: 3px;
  height: calc(100% + 6px);
  background: #ffc61a;
}
</style>
