<template>
  <div class="w-[154px] p-4 layers-wrapper">
    <div class="flex flex-col gap-2">
      <div class="text-sm layer-group" v-for="(group, groupName) in localLayers" :key="String(groupName)">
        <div class="flex items-center space-x-2">
          <CustomCheckbox
            :id="String(groupName)"
            v-model="group.checked"
            :disabled="!!group.disabled"
            @update:model-value="() => !group.disabled && toggleGroup(String(groupName))"
          />
          <label
            :for="String(groupName)"
            class="flex items-center gap-2 text-sm font-medium leading-none cursor-pointer select-none"
            :style="{ color: getTextColor(group.checked, !!group.disabled) }"
          >
            <div class="checkbox-icon" :style="getIconStyle(group.icon, group.checked !== false)"></div>
            {{ group.label }}
          </label>
        </div>
        <div class="pl-6 mt-2 space-y-2" v-if="group.children">
          <div class="flex items-center space-x-2" v-for="item in group.children" :key="item.id">
            <CustomCheckbox
              :id="item.id"
              v-model="item.checked"
              :disabled="!!item.disabled"
              @update:model-value="() => !item.disabled && handleChildChange(String(groupName), item.id)"
            />
            <label
              :for="item.id"
              class="text-sm leading-none cursor-pointer select-none"
              :style="{ color: getTextColor(item.checked, !!item.disabled) }"
            >
              {{ item.label }}
            </label>
          </div>
        </div>
      </div>
    </div>
    <div class="layers-wrapper-bottom"></div>
  </div>
</template>
<script setup lang="ts">
import { watch, computed, ref } from 'vue'
import CustomCheckbox from '@/components/ui/checkbox/CustomCheckbox.vue'
import type { Layers, LayerChangeEvent } from '@/types/mapLayers'

// 导入图标资源
import gasIcon from '@/assets/map/gas-icon.png'
import activeGasIcon from '@/assets/map/active-gas-icon.png'
import pipelineIcon from '@/assets/map/pipeline-icon.png'
import activePipelineIcon from '@/assets/map/active-pipeline-icon.png'
import stationIcon from '@/assets/map/station-icon.png'
import activeStationIcon from '@/assets/map/active-station-icon.png'
import warningIcon from '@/assets/map/warning-icon.png'
import activeWarningIcon from '@/assets/map/active-warning-icon.png'
import alarmIcon from '@/assets/map/alarm-icon.png'
import activeAlarmIcon from '@/assets/map/active-alarm-icon.png'
import resourceIcon from '@/assets/map/resource-icon.png'
import activeResourceIcon from '@/assets/map/active-resource-icon.png'
import eventIcon from '@/assets/map/event-icon.png'
import activeEventIcon from '@/assets/map/active-event-icon.png'
import falloutIcon from '@/assets/map/fallout-icon.png'
import activeFalloutIcon from '@/assets/map/active-fallout-icon.png'
import buildingIcon from '@/assets/map/building-icon.png'
import activeBuildingIcon from '@/assets/map/active-building-icon.png'
import vehicleIcon from '@/assets/map/vehicle-icon.png'
import activeVehicleIcon from '@/assets/map/active-vehicle-icon.png'
import monitorIcon from '@/assets/map/monitor-icon.png'
import activeMonitorIcon from '@/assets/map/active-monitor-icon.png'
import industryIcon from '@/assets/map/industry-icon.png'
import activeIndustryIcon from '@/assets/map/active-industry-icon.png'
import supervisionMattersIcon from '@/assets/map/supervision-matters-icon.png'
import activeSupervisionMattersIcon from '@/assets/map/active-supervision-matters-icon.png'
import complaintHeatIcon from '@/assets/map/complaint-heat-icon.png'
import activeComplaintHeatIcon from '@/assets/map/active-complaint-heat-icon.png'

// 图标配置
const ICON_CONFIG = {
  supervisionMatters: {
    default: supervisionMattersIcon,
    active: activeSupervisionMattersIcon,
  },
  complaintHeat: {
    default: complaintHeatIcon,
    active: activeComplaintHeatIcon,
  },
  vehicle: {
    default: vehicleIcon,
    active: activeVehicleIcon,
  },
  monitor: {
    default: monitorIcon,
    active: activeMonitorIcon,
  },
  gas: {
    default: gasIcon,
    active: activeGasIcon,
  },
  pipeline: {
    default: pipelineIcon,
    active: activePipelineIcon,
  },
  station: {
    default: stationIcon,
    active: activeStationIcon,
  },
  warning: {
    default: warningIcon,
    active: activeWarningIcon,
  },
  alarm: {
    default: alarmIcon,
    active: activeAlarmIcon,
  },
  resource: {
    default: resourceIcon,
    active: activeResourceIcon,
  },
  event: {
    default: eventIcon,
    active: activeEventIcon,
  },
  fallout: {
    default: falloutIcon,
    active: activeFalloutIcon,
  },
  building: {
    default: buildingIcon,
    active: activeBuildingIcon,
  },
  industry: {
    default: industryIcon,
    active: activeIndustryIcon,
  },
} as const

// 颜色配置
const COLORS = {
  checked: '#FFD966',
  unchecked: '#66FFFF',
  disabled: '#9CA3AF', // 灰色用于禁用状态
} as const

const props = defineProps<{
  modelValue: Layers
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: Layers): void
  (e: 'layer-change', value: LayerChangeEvent): void
}>()

// 用于跟踪上一次的状态，避免深拷贝
const previousState = ref<Map<string, boolean>>(new Map())

const localLayers = computed({
  get: () => props.modelValue,
  set: value => {
    emit('update:modelValue', value)
  },
})

// 生成状态键
const generateStateKey = (groupName: string, itemId?: string) => {
  return itemId ? `${groupName}-${itemId}` : groupName
}

// 初始化状态跟踪
const initializeState = (newLayers: Layers) => {
  const newState = new Map<string, boolean>()
  Object.entries(newLayers).forEach(([groupName, group]) => {
    newState.set(generateStateKey(groupName), group.checked === true)
    if (group.children) {
      group.children.forEach(child => {
        newState.set(generateStateKey(groupName, child.id), child.checked)
      })
    }
  })
  previousState.value = newState
}

// 优化的监听逻辑 - 使用更高效的状态跟踪
watch(
  () => props.modelValue,
  newLayers => {
    // 每次 modelValue 变化时，重新构建 previousState
    initializeState(newLayers)
  },
  { deep: true, immediate: true },
)

// 优化的组切换函数（跳过被禁用的组或子项）
const toggleGroup = (groupName: keyof Layers) => {
  const newLayers = { ...localLayers.value }
  const group = newLayers[groupName]

  if (!group || group.disabled) {
    return
  }

  if (!group.children) {
    localLayers.value = newLayers
    return
  }

  const newCheckedState = group.checked === 'indeterminate' ? true : group.checked

  // 批量更新子项状态，但跳过 disabled 子项
  group.children.forEach(child => {
    if (!child.disabled) {
      child.checked = newCheckedState
    }
  })

  localLayers.value = newLayers

  if (group.checked !== 'indeterminate') {
    emit('layer-change', {
      groupId: String(groupName),
      checked: group.checked,
    })
  }
}

// 优化的子项变化处理函数（跳过被禁用的子项）
const handleChildChange = (groupName: keyof Layers, itemId: string) => {
  const newLayers = { ...localLayers.value }
  const group = newLayers[groupName]

  if (!group || !group.children) {
    localLayers.value = newLayers
    return
  }

  const item = group.children.find(child => child.id === itemId)
  if (!item || item.disabled) {
    // 如果子项被禁用，不进行处理
    return
  }

  // 触发子项变化事件
  emit('layer-change', {
    groupId: String(groupName),
    itemId: item.id,
    checked: item.checked,
  })

  // 统计仅未禁用的子项
  const enabledChildren = group.children.filter(child => !child.disabled)
  const checkedCount = enabledChildren.filter(child => child.checked).length
  const totalCount = enabledChildren.length

  // 使用更简洁的逻辑确定组状态，若没有可用子项则置 false
  if (totalCount === 0) {
    group.checked = false
  } else {
    group.checked = checkedCount === 0 ? false : checkedCount === totalCount ? true : 'indeterminate'
  }

  localLayers.value = newLayers
}

// 获取图标样式的计算函数
const getIconStyle = (iconType: string, isActive: boolean) => {
  const config = ICON_CONFIG[iconType as keyof typeof ICON_CONFIG]
  if (!config) {
    console.warn(`Icon config not found for type: ${iconType}`)
    return {
      width: '16px',
      height: '16px',
      display: 'inline-block',
      backgroundColor: '#ccc', // 占位颜色，便于调试
    }
  }

  const iconUrl = isActive ? config.active : config.default
  return {
    backgroundImage: `url("${iconUrl}")`,
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center',
    backgroundSize: 'contain',
    width: '16px',
    height: '16px',
    display: 'inline-block',
  }
}

// 获取文本颜色的计算函数，增加 disabled 支持
const getTextColor = (checked: boolean | 'indeterminate', disabled = false) => {
  if (disabled) return COLORS.disabled
  return checked === false ? COLORS.unchecked : COLORS.checked
}
</script>

<style scoped>
@import '@/styles/index.css';
.checkbox-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.layer-group {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
  color: #66ffff;
}

.layer-group:last-child {
  border-bottom: none;
}

/* 禁用时样式提示 */
label[for] {
  transition: opacity 0.15s;
}
label[for][style*='#9CA3AF'] {
  opacity: 0.7;
}
</style>
