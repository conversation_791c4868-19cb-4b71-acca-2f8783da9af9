<template>
  <div class="panel-container">
    <div class="panel-header">重点防控点位上报数据</div>
    <div class="p-4 panel-content">
      <div class="flex gap-6">
        <!-- 左侧3D环形图 -->
        <div class="chart-section">
          <div ref="chartRef" class="donut-chart"></div>
          <!-- 图例 -->
          <!-- <div class="legend-section">
            <div class="legend-item">
              <div class="legend-color" style="background-color: #55d0e0"></div>
              <span class="legend-text">2020-2022</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background-color: #f7b731"></div>
              <span class="legend-text">2022-2023</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background-color: #5a9fd4"></div>
              <span class="legend-text">2024-2025</span>
            </div>
          </div> -->
        </div>

        <!-- 右侧数据列表 -->
        <div class="data-list-section">
          <div v-for="(item, index) in dataList" :key="index">
            <div class="flex items-center justify-between">
              <div class="data-row-label">{{ item.label }}</div>
              <div class="data-row-value">{{ item.value }}</div>
            </div>
            <div class="progress-section">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: item.progress + '%' }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
import { getPie3D } from '@/utils/echartsTools'
import { getHazardReport, getHazardReportZhu } from '@/common/api/hazard'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

// 右侧数据列表
const dataList = ref<any[]>([])

// 饼图数据
const pieData = ref<any[]>([])

// part_type映射
const partTypeMap: Record<number, { name: string; color: string }> = {
  1: { name: '重点保护区域', color: '#55d0e0' },
  2: { name: '重点危险物放置区域', color: '#f7b731' },
  3: { name: '其他类型', color: '#5a9fd4' }
}

// 获取饼图数据
const fetchHazardReport = async () => {
  try {
    const res = await getHazardReport({})
    const data = Array.isArray(res?.data) ? res.data : []
    console.log('饼图数据:', data)
    
    pieData.value = data.map((item: any) => {
      const typeInfo = partTypeMap[item.part_type] || { name: '未知类型', color: '#999' }
      return {
        name: typeInfo.name,
        value: item.count || 0,
        proportion: item.proportion || '0%',
        itemStyle: {
          color: typeInfo.color
        }
      }
    })
    
    // 更新图表
    if (chart && pieData.value.length > 0) {
      const option = getPie3D(pieData.value, 0.5)
      chart.setOption(option)
    }
  } catch (e) {
    console.error('获取饼图数据失败:', e)
  }
}

// 获取柱状图数据（右侧列表）
const fetchHazardReportZhu = async () => {
  try {
    const res = await getHazardReportZhu({})
    const data = Array.isArray(res?.data) ? res.data : []
    console.log('柱状图数据:', data)
    
    // 找出最大值用于计算进度条百分比
    const maxCount = Math.max(...data.map((item: any) => item.count || 0))
    
    dataList.value = data.map((item: any) => ({
      label: item.township || '未知乡镇',
      value: item.count || 0,
      progress: maxCount > 0 ? Math.round((item.count / maxCount) * 100) : 0
    }))
  } catch (e) {
    console.error('获取柱状图数据失败:', e)
  }
}

const initchart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  
  // 初始空图表
  const option = getPie3D([], 0.5)
  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initchart()
  fetchHazardReport()
  fetchHazardReportZhu()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>
<style scoped>
@import '@/styles/index.css';

.chart-section {
  width: 348px;
  height: 224px;
  display: flex;
  flex-direction: column;
}

.donut-chart {
  width: 100%;
  height: 100%;
}

.legend-section {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-text {
  font-size: 12px;
  color: #ffffff;
  font-family: Noto Sans SC;
}

.data-list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-left: 20px;
}

.data-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
}

.data-row-label {
  font-size: 14px;
  color: #ffffff;
  font-family: Noto Sans SC;
  width: 120px;
}

.progress-section {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.progress-bar {
  flex: 1;
  height: 12px;
  background: rgba(255, 255, 255, 0.1);
}

.progress-fill {
  position: relative;
  height: 100%;
  background: linear-gradient(270deg, #ffc61a 0%, rgba(255, 198, 26, 0.45) 100%);
  transition: width 0.3s ease;

  &::after {
    content: '';
    position: absolute;
    top: -2px;
    right: 0;
    width: 4px;
    height: 16px;
    background-color: #ffc61a;
    z-index: 1;
  }
}

.data-row-value {
  font-size: 16px;
  font-weight: bold;
  color: #f7b731;
  font-family: Noto Sans SC;
  min-width: 60px;
  text-align: right;
}
</style>
