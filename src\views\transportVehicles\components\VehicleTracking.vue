<template>
  <div class="panel-container-col" style="margin: 0">
    <div class="panel-header">企业车辆概况</div>
    <div class="p-4 panel-content">
      <div class="filter-bar">
        <div class="input-with-icon">
          <Search class="icon" />
          <input v-model.trim="keyword" class="filter-input" type="text" placeholder="输入车牌号/车辆型号/所属企业查询" />
        </div>
      </div>
      <Table class="w-full table-fixed">
        <colgroup>
          <col style="width: 22%" />
          <col style="width: 18%" />
          <col style="width: 18%" />
          <col style="width: auto" />
          <col style="width: 12%" />
        </colgroup>
        <TableHeader>
          <TableRow class="border-[#99D5FF]/30 hover:bg-transparent">
            <TableHead class="font-bold text-white">所属区域</TableHead>
            <TableHead class="font-bold text-white">车辆型号</TableHead>
            <TableHead class="font-bold text-white">车辆号码</TableHead>
            <TableHead class="font-bold text-white">所属服务企业</TableHead>
            <TableHead class="font-bold text-white">操作</TableHead>
          </TableRow>
        </TableHeader>
      </Table>
      <div class="overflow-hidden" :style="{ height: `${6 * rowHeight}px` }" ref="tableContainerRef">
        <Table class="w-full table-fixed">
          <colgroup>
            <col style="width: 22%" />
            <col style="width: 18%" />
            <col style="width: 18%" />
            <col style="width: auto" />
            <col style="width: 12%" />
          </colgroup>
          <TableBody
            :style="{
              transform: `translateY(-${scrollTop}px)`,
              transition: transitionEnabled ? 'transform 0.5s ease' : 'none',
            }"
          >
            <TableRow
              v-for="(item, index) in scrollList"
              :key="index"
              class="border-[#99D5FF]/30 hover:bg-[#99D5FF]/30"
              :style="{ height: `${rowHeight}px` }"
            >
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.region }}</TableCell>
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.plate }}</TableCell>
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.vin }}</TableCell>
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.company }}</TableCell>
              <TableCell class="action" @click="handleViewDetail(item)">查看详情</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  </div>

  <!-- 详情弹窗 -->
  <VehicleDetailDialog :open="dialogOpen" :record-id="selectedRecordId" @close="handleCloseDialog" />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { Search } from 'lucide-vue-next'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { yscljsc_qyclgk } from '@/common/api/transportVehicles'
import VehicleDetailDialog from './VehicleDetailDialog.vue'

const rows = ref<any[]>([])
const keyword = ref('')
let searchTimer: any = null

// 详情弹窗状态
const dialogOpen = ref(false)
const selectedRecordId = ref<number | string | undefined>()

// 获取企业车辆概况数据
const fetchEnterpriseVehicleOverview = async (searchParam: string = '') => {
  try {
    const response = await yscljsc_qyclgk({ param: searchParam })
    if (response.data && Array.isArray(response.data)) {
      rows.value = response.data.map((item: any) => ({
        id: item.id, // 保存id用于查看详情
        region: item.district || '这是对应内容信息',
        plate: item.type || '冀D·A1234',
        vin: item.number || 'VIN000',
        company: item.enterprise_for_id || '这是对应内容信息',
      }))
    } else {
      // 如果没有数据，使用默认值
      rows.value = Array.from({ length: 12 }).map((_, i) => ({
        id: i + 1,
        region: '这是对应内容信息',
        plate: '冀D·A1234',
        vin: 'VIN000' + (i + 1).toString().padStart(3, '0'),
        company: i % 2 === 0 ? '肥乡燃气集团' : '城市运营公司',
      }))
    }
  } catch (error) {
    console.error('获取企业车辆概况失败:', error)
    // 出错时使用默认值
    rows.value = Array.from({ length: 12 }).map((_, i) => ({
      id: i + 1,
      region: '这是对应内容信息',
      plate: '冀D·A1234',
      vin: 'VIN000' + (i + 1).toString().padStart(3, '0'),
      company: i % 2 === 0 ? '肥乡燃气集团' : '城市运营公司',
    }))
  }
}

// 监听搜索关键词变化，使用防抖
watch(keyword, (newVal) => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    const searchParam = newVal.trim()
    fetchEnterpriseVehicleOverview(searchParam)
  }, 500) // 500ms防抖
})

const scrollList = computed(() => [...rows.value, ...rows.value])
const tableContainerRef = ref<HTMLElement | null>(null)
const scrollTimer = ref<any>(null)
const scrollTop = ref(0)
const rowHeight = 48
const transitionEnabled = ref(true)

const startScrolling = () => {
  stopScrolling()
  scrollTimer.value = setInterval(() => {
    scrollTop.value += rowHeight
    if (scrollTop.value >= rows.value.length * rowHeight) {
      transitionEnabled.value = false
      scrollTop.value = 0
      setTimeout(() => {
        transitionEnabled.value = true
      }, 50)
    }
  }, 3000)
}

const stopScrolling = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value)
    scrollTimer.value = null
  }
}

// 查看详情
const handleViewDetail = (item: any) => {
  if (item.id) {
    selectedRecordId.value = item.id
    dialogOpen.value = true
  } else {
    console.warn('无法查看详情：缺少ID')
  }
}

// 关闭弹窗
const handleCloseDialog = () => {
  dialogOpen.value = false
  selectedRecordId.value = undefined
}

onMounted(() => {
  // 初始加载时传空字符串
  fetchEnterpriseVehicleOverview('')
  startScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.addEventListener('mouseenter', stopScrolling)
    container.addEventListener('mouseleave', startScrolling)
  }
})

onUnmounted(() => {
  stopScrolling()
  // 清理搜索防抖定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  const container = tableContainerRef.value
  if (container) {
    container.removeEventListener('mouseenter', stopScrolling)
    container.removeEventListener('mouseleave', startScrolling)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';
.filter-bar {
  margin-bottom: 12px;
}
.input-with-icon {
  position: relative;
  width: 100%;
}
.icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  color: #99d5ff;
  opacity: 0.9;
  pointer-events: none;
}
.filter-input {
  width: 100%;
  height: 32px;
  padding: 0 12px 0 36px;
  color: #e7f2ff;
  background: rgba(64, 159, 255, 0.08);
  border: 1px solid rgba(153, 213, 255, 0.35);
  border-radius: 4px;
}
.action {
  color: #99d5ff;
  cursor: pointer;
}
</style>
