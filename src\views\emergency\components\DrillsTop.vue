<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { getEmergencyDrillRankings, type EmergencyDrillRanking } from '@/common/api/emergency'
import { toast } from 'vue-sonner'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
let currentIndex = -1

const loading = ref(false)

// 图表数据
const yAxis = ref(['中石油昆仑燃气', '新奥燃气', '华港燃气', '东宏液化气', '五申液化气', '中海油雄安子午加气站'])
const xData = ref([4, 3, 3, 2, 2, 2])

const option = {
  backgroundColor: 'transparent',
  grid: {
    left: '5%',
    right: '5%',
    top: '3%',
    bottom: '3%',
    containLabel: true,
  },
  tooltip: {
    show: false,
  },
  barGap: 80,
  barCategoryGap: 80,
  xAxis: {
    type: 'value',
    max: 20,
    axisTick: {
      show: false,
    },
    axisLine: {
      show: false,
    },
    splitLine: {
      show: false,
    },
    axisLabel: {
      show: false,
    },
  },
  yAxis: [
    {
      type: 'category',
      inverse: true,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      data: yAxis.value,
    },
    {
      inverse: true,
      offset: 0,
      axisLabel: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      data: xData.value.map(i => ({
        value: 0,
        key: i,
      })),
    },
  ],
  series: [
    {
      type: 'bar',
      barWidth: 20,
      data: xData,
      showBackground: true,
      backgroundStyle: {
        color: 'rgba(140, 255, 255, 0.15)',
      },
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(140, 255, 255, 0.1)',
            },
            {
              offset: 1,
              color: 'rgba(140, 255, 255, 0.6)',
            },
          ],
        },
      },
      // 添加高亮样式
      emphasis: {
        itemStyle: {
          color: 'rgba(140, 255, 255, 0.6)', // 设置高亮颜色
        },
      },
      label: {
        show: false,
        position: 'right',
        offset: [0, 20],
        color: '#8CFFFF',
        fontSize: 14,
        formatter: '{c}次',
      },
      z: 2,
    },
    // 背景条右侧的三角形装饰
    {
      type: 'pictorialBar',
      symbol: 'triangle',
      symbolSize: [20, 10],
      symbolOffset: [15, 0],
      symbolPosition: 'end',
      symbolRotate: -90,
      itemStyle: {
        color: 'rgba(140, 255, 255, 0.15)',
      },
      data: xData.value.map(() => 20), // 使用最大值确保三角形位置正确
      tooltip: {
        show: false,
      },
      z: 2,
    },
    // 右侧装饰三角形
    {
      type: 'pictorialBar',
      symbol: 'diamond',
      symbolSize: [10, 20],
      symbolOffset: [5, 0],
      symbolPosition: 'end',
      itemStyle: {
        color: '#8CFFFF',
      },
      data: xData,
      z: 5,
    },
    // 贯穿的虚线
    {
      type: 'pictorialBar',
      symbol: 'rect',
      symbolSize: ['100%', 1],
      symbolPosition: 'center',
      symbolOffset: [0, 0],
      z: 4,
      itemStyle: {
        color: 'transparent',
        borderColor: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: 'rgba(140, 255, 255, 0.2)' },
          { offset: 1, color: 'rgba(140, 255, 255, 0.8)' },
        ]),
        borderWidth: 1,
        borderType: 'dashed',
      },
      tooltip: { show: false },
      data: xData,
    },
    // 左侧公司名称标签
    {
      type: 'scatter',
      symbolSize: 0,
      data: yAxis.value.map((_, index) => [0, index]),
      label: {
        show: true,
        position: [0, -15],
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 14,
        align: 'left',
        verticalAlign: 'bottom',
        formatter: (params: any) => yAxis.value[params.dataIndex],
      },
      z: 4,
    },
    // 右侧数值标签
    {
      type: 'scatter',
      symbolSize: 0,
      data: xData.value.map((_, index) => [20, index]), // 使用最大值20作为x坐标，对应bar背景的最右端
      label: {
        show: true,
        position: [0, -15],
        color: '#8CFFFF',
        fontSize: 14,
        align: 'right',
        verticalAlign: 'bottom',
        formatter: (params: any) => `${xData.value[params.dataIndex]}次`,
      },
      z: 4,
    },
  ],
}

// 获取应急演练次数排行数据
const fetchEmergencyDrillRankings = async () => {
  try {
    loading.value = true
    const response = await getEmergencyDrillRankings()

    if (response.data && Array.isArray(response.data)) {
      // 按次数排序并取前6名
      const sortedData = response.data
        .sort((a: EmergencyDrillRanking, b: EmergencyDrillRanking) => (b.number || 0) - (a.number || 0))
        .slice(0, 6)

      yAxis.value = sortedData.map((item: EmergencyDrillRanking) => item.enterpriseName || '未知企业')
      xData.value = sortedData.map((item: EmergencyDrillRanking) => item.number || 0)

      // 更新图表
      if (chart) {
        chart.setOption({
          yAxis: [
            {
              data: yAxis.value,
            },
            {
              data: xData.value.map(i => ({
                value: 0,
                key: i,
              })),
            },
          ],
        })
      }
    }
  } catch (error) {
    console.error('获取应急演练次数排行数据失败:', error)
    toast.error('获取应急演练次数排行数据失败')
  } finally {
    loading.value = false
  }
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  chart.setOption(option)
  startHighlightAnimation()

  // 获取数据
  fetchEmergencyDrillRankings()
}

// 开始高亮轮播动画
const startHighlightAnimation = () => {
  const dataCount = option.yAxis[0].data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0, // 作用在第一个系列上
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0, // 作用在第一个系列上
      dataIndex: currentIndex,
    })
  }, 2000) // 每隔2秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>

<template>
  <div class="panel-container-col">
    <div class="panel-header">本年应急演练次数排行</div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<style scoped>
@import '@/styles/index.css';

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
