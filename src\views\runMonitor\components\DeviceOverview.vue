<template>
  <div class="panel-container-col">
    <div class="panel-header">设备概况</div>
    <div class="p-4 panel-content">
      <div class="status-indicators">
        <div class="status-item animate-pulse">
          <div class="status-value">{{ siteCount }}</div>
          <div class="status-label">站点设备数</div>
        </div>
        <div class="status-item animate-pulse">
          <div class="status-value">{{ pipelineCount }}</div>
          <div class="status-label">管网设备数</div>
        </div>
        <div class="status-item animate-pulse">
          <div class="status-value">{{ householdCount }}</div>
          <div class="status-label">入户设备数</div>
        </div>
      </div>
      <div class="chart-section">
        <div ref="chartRef" class="donut-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { sbjc_sbgk } from '@/common/api/runMonitor' // 新增：导入接口
import { createSignedPayload } from '@/common/utils/auth' // 新增：导入签名工具

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
let currentIndex = -1

// 新增：响应式数据，用于模板显示与图表更新
const siteCount = ref<number>(169)
const pipelineCount = ref<number>(223)
const householdCount = ref<number>(136215)

const option = {
  backgroundColor: 'transparent',
  legend: {
    show: true,
    orient: 'vertical',
    right: '0%',
    bottom: '0%',
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    itemWidth: 12,
    itemHeight: 8,
    icon: 'rect',
  },
  series: [
    {
      type: 'pie',
      radius: [0, '78%'],
      // center: ['38.2%', '50%'],
      silent: true,
      data: [{ value: 1, itemStyle: { color: 'rgba(153, 213, 255, 0.15)' } }],
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
    },
    {
      type: 'pie',
      radius: [0, '72%'],
      // center: ['38.2%', '50%'],
      data: [
        { value: siteCount.value, name: '场站设备数', itemStyle: { color: '#409FFF' } },
        { value: pipelineCount.value, name: '管网设备数', itemStyle: { color: '#C0FFB3' } },
        { value: householdCount.value, name: '入户设备数', itemStyle: { color: '#FFC61A' } },
      ],
      label: {
        show: true,
        color: '#fff',
        formatter: `{percent|{d}%}\n{value|{c}}个`,
        rich: {
          percent: {
            fontSize: 20,
            color: '#fff',
          },
          value: {
            fontSize: 12,
            color: '#fff',
          },
        },
      },
      labelLine: {
        show: true,
      },
    },
  ],
}

const initchart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startHighlightAnimation()
}

// 开始高亮轮播动画
const startHighlightAnimation = () => {
  const dataCount = option.series[1].data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 1, // 作用在第二个系列（数据饼图）上
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 1, // 作用在第二个系列（数据饼图）上
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

// 请根据实际项目把 key 放到安全的配置文件或环境变量中，这里为演示使用占位符
const API_KEY = '9900J7T2J91992'

// 新增：调用后端接口并更新展示数据与图表（支持数组形式的返回）
const fetchAndApplyOverview = async () => {
  try {
    const param = {}

    // 使用 auth 工具创建带 timestamp 与 sign 的请求体
    const signedPayload = createSignedPayload(param, API_KEY)

    // 将带签名的请求体传给接口
    const res = await sbjc_sbgk(signedPayload)

    // 可能的返回结构：
    // 1) { msg, success, data: [ { type, num, ... }, ... ] }
    // 2) 直接返回数组 [ { type, num }, ... ]
    // 3) 旧的对象形式 { czsb, gwsb, rhsb }
    const body = res?.data ?? res
    let list: any[] | null = null

    if (Array.isArray(body)) {
      list = body
    } else if (Array.isArray(body?.data)) {
      list = body.data
    } else if (Array.isArray(body?.result)) {
      list = body.result
    }

    if (Array.isArray(list)) {
      // 遍历数组并根据 type 把 num 分配到对应变量
      list.forEach((item: any) => {
        const t = (item?.type || '').toString().toLowerCase()
        const n = Number(item?.num ?? item?.value ?? 0)
        if (t === 'czsb') {
          siteCount.value = n
        } else if (t === 'gwsb') {
          pipelineCount.value = n
        } else if (t === 'rhsb' || t === 'rhsh' || t === 'rhs' || t === 'rh') {
          // 兼容后端可能的拼写差异（rhsh / rhsb 等）
          householdCount.value = n
        }
      })

      // 更新图表第二个 series 的数据
      chart?.setOption({
        series: [
          {},
          {
            data: [
              { value: siteCount.value, name: '场站设备数', itemStyle: { color: '#409FFF' } },
              { value: pipelineCount.value, name: '管网设备数', itemStyle: { color: '#C0FFB3' } },
              { value: householdCount.value, name: '入户设备数', itemStyle: { color: '#FFC61A' } },
            ],
          },
        ],
      })
    } else if (body && typeof body === 'object') {
      // 回退到旧的对象格式解析（兼容之前的实现）
      const d = body
      siteCount.value = Number(d.czsb ?? siteCount.value)
      pipelineCount.value = Number(d.gwsb ?? pipelineCount.value)
      householdCount.value = Number(d.rhsb ?? householdCount.value)

      chart?.setOption({
        series: [
          {},
          {
            data: [
              { value: siteCount.value, name: '场站设备数', itemStyle: { color: '#409FFF' } },
              { value: pipelineCount.value, name: '管网设备数', itemStyle: { color: '#C0FFB3' } },
              { value: householdCount.value, name: '入户设备数', itemStyle: { color: '#FFC61A' } },
            ],
          },
        ],
      })
    } else {
      // 无有效数据时不做变更
      console.warn('设备概况接口返回格式不符，未更新数据', res)
    }
  } catch (err) {
    console.error('获取设备概况失败', err)
  }
}

onMounted(() => {
  initchart()
  // 在生命周期中调用接口并更新视图
  fetchAndApplyOverview()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.panel-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-indicators {
  width: 100%;
  height: 104px;
  display: flex;
  justify-content: space-around;
}

.status-item {
  display: flex;
  width: 31%;
  background: url('@/assets/run-monitor/device-overview-icon.png') no-repeat center center;
  background-size: 120px 60px;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(64, 159, 255, 0) 0%, #409fff 49%, rgba(64, 159, 255, 0) 100%) 1;
}

.status-value {
  font-family: YouSheBiaoTiHei;
  font-size: 24px;
  font-weight: bold;
  line-height: 28px;
  text-align: center;
  color: #fff;
  white-space: nowrap;
}

.status-label {
  font-family: NotoSansSC;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  text-align: center;
  letter-spacing: normal;
  color: #ffffff;
}

.chart-section {
  width: 100%;
  height: 258px;
}

.donut-chart {
  width: 100%;
  height: 100%;
}
</style>
