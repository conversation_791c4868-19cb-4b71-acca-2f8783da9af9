import request from '@/utils/request'
import requestOur from '@/utils/request_our'

// 请求体接口（根据实际字段调整）
export interface PatrolOverviewPayload {
  [key: string]: any
}

// 响应数据接口（根据实际返回结构调整）
export interface PatrolOverviewResponse {
  code?: number
  message?: string
  data?: any
}

// 巡检-巡检概况
export function xj_gk(payload: PatrolOverviewPayload = {}): Promise<PatrolOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/patrol/overview/5017FF936D37538D86001BAB0831FAB7', payload, {
    params: { applicationName: 'fxrq' },
  })
}

// 巡检-本月重点巡查区域TOP5
export function xj_top(payload: PatrolOverviewPayload = {}): Promise<PatrolOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/patrol/top/51A178069BB5550936400821D8DE352E', payload, {
    params: { applicationName: 'fxrq' },
  })
}

// 巡检-未整改隐患预警
export function patrol_hidden_danger(): Promise<PatrolOverviewResponse> {
  const timestamp = Date.now() + Math.random()
  return requestOur.get(`/prod-api/gasPlatform/patrolHiddenDanger?reCondition=未整治&pageNum=1&pageSize=100&_t=${timestamp}`)
}

// 巡检-巡检风险预警分级统计
export function patrol_level(payload: PatrolOverviewPayload = {}): Promise<PatrolOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/patrol/level/044CB753EB719B316B814AFDD499FB35', payload, {
    params: { applicationName: 'fxrq' },
  })
}

// 巡检-巡检任务完成进度
export function patrol_progress(payload: PatrolOverviewPayload = {}): Promise<PatrolOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/patrol/progress/A922057FBB0BB349FE02B0010C365823', payload, {
    params: { applicationName: 'fxrq' },
  })
}

// 巡检-近一年巡检记录统计
export function patrol_statistics(payload: PatrolOverviewPayload = {}): Promise<PatrolOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/patrol/statistics/1C22E77FAF58C087AF252B1575D5B074682202776DBEF457', payload, {
    params: { applicationName: 'fxrq' },
  })
}