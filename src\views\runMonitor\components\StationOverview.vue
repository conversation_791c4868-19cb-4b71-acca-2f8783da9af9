<template>
  <div class="panel-container">
    <div class="panel-header">站点概况</div>
    <div class="p-4 panel-content">
      <div class="flex flex-wrap justify-around status-indicators gap-y-1">
        <div v-for="(i, index) in statusData" :key="index" class="status-item animate-pulse" :class="i.icon">
          <div class="status-value">{{ i.value }}</div>
          <div class="status-label">{{ i.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { zdjc_zdgk } from '@/common/api/runMonitor' // 新增：导入站点概况接口
import { createSignedPayload } from '@/common/utils/auth' // 新增：导入签名工具

const statusData = ref([
  { label: '场站总数', value: 41, icon: 'blue' },
  { label: '门站数量', value: 9, icon: 'green' },
  { label: '调压站数量', value: 16, icon: 'yellow' },
  { label: '充装站', value: 2, icon: 'blue' },
  { label: '储备站', value: 10, icon: 'green' },
  { label: 'CNG加气站', value: 4, icon: 'yellow' },
])

const API_KEY = '9900J7T2J91992'

// 将后端返回的列表/对象映射到 statusData
const applyDataToStatus = (items: any[]) => {
  if (!Array.isArray(items)) return

  if (items.length === 1 && typeof items[0] === 'object' && items[0] !== null) {
    const o = items[0]

    statusData.value[0].value = Number(o.czsl ?? statusData.value[0].value) // 场站总数
    statusData.value[1].value = Number(o.mzsl ?? statusData.value[1].value) // 门站数量
    statusData.value[2].value = Number(o.tyzsl ?? statusData.value[2].value) // 调压站数量
    statusData.value[3].value = Number(o.czzsl ?? statusData.value[3].value) // 充装站（czzsl）
    statusData.value[4].value = Number(o.cpzsl ?? statusData.value[4].value) // 储备站（映射到 cpzsl）
    statusData.value[5].value = Number(o.cngjqzsl ?? statusData.value[5].value) // CNG加气站
    return
  }

  // 原来的模糊匹配逻辑（兜底）
  items.forEach(it => {
    const name = (it?.name ?? it?.label ?? it?.type ?? it?.title ?? '').toString()
    const num = Number(it?.num ?? it?.count ?? it?.value ?? it?.total ?? it?.amount ?? 0)

    statusData.value.forEach(s => {
      if (name && name.includes(s.label.replace(/数量|总数|站/g, ''))) {
        s.value = num
      } else if (name && name.includes('场站') && s.label.includes('场站')) {
        s.value = num
      } else if (name && name.toLowerCase().includes('cng') && s.label.includes('CNG')) {
        s.value = num
      }
    })
  })
}

const fetchAndApplyStationOverview = async () => {
  try {
    const param = {}
    const signed = createSignedPayload(param, API_KEY)
    const res = await zdjc_zdgk(signed)
    const body = res?.data ?? res

    // 支持多种返回结构：数组、{ data: [...] }、或单对象里带统计字段
    if (Array.isArray(body)) {
      applyDataToStatus(body)
    } else if (Array.isArray(body?.data)) {
      applyDataToStatus(body.data)
    } else if (body && typeof body === 'object') {
      // 如果是对象，尝试把对象的键值对转换为数组项，便于复用映射逻辑
      const list: any[] = []
      Object.keys(body).forEach(k => {
        const v = body[k]
        if (typeof v === 'number') {
          list.push({ name: k, num: v })
        } else if (typeof v === 'object' && v !== null) {
          const possibleNum = v.num ?? v.count ?? v.total ?? v.value
          if (possibleNum !== undefined) {
            list.push({ name: k, num: possibleNum })
          }
        }
      })
      if (list.length > 0) {
        applyDataToStatus(list)
      } else {
        // 兜底：尝试按常见字段读取
        statusData.value[0].value = Number(body?.totalStation ?? statusData.value[0].value)
        statusData.value[1].value = Number(body?.menStation ?? statusData.value[1].value)
        statusData.value[2].value = Number(body?.tiaoYaStation ?? statusData.value[2].value)
      }
    } else {
      console.warn('站点概况接口返回格式未知，未更新数据', res)
    }
  } catch (err) {
    console.error('获取站点概况失败', err)
  }
}

onMounted(() => {
  // 调用接口获取并应用数据
  fetchAndApplyStationOverview()
})
</script>

<style scoped>
@import '@/styles/index.css';

.status-indicators {
  width: 100%;
  height: 216px;
}

.status-item {
  display: flex;
  width: 227px;
  height: 104px;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(64, 159, 255, 0) 0%, #409fff 49%, rgba(64, 159, 255, 0) 100%) 1;
}
.status-item.blue {
  background: url('@/assets/run-monitor/station-overview-blue.png') no-repeat center;
  background-size: 90px 40px;
}
.status-item.yellow {
  background: url('@/assets/run-monitor/station-overview-green.png') no-repeat center;
  background-size: 90px 40px;
}
.status-item.green {
  background: url('@/assets/run-monitor/station-overview-yellow.png') no-repeat center;
  background-size: 90px 40px;
}

.status-value {
  font-family: NotoSansSC;
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  text-align: center;
  color: #fff;
  white-space: nowrap;
}

.status-label {
  font-family: NotoSansSC;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  padding-bottom: 4px;
  text-align: center;
  letter-spacing: normal;
  color: #fff;
}
</style>
