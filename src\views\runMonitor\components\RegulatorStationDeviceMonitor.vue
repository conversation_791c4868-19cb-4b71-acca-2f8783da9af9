<template>
  <div class="panel-container-col">
    <div class="panel-header">企业调压站压力监测</div>
    <div class="p-4 panel-content">
      <div class="chart-section">
        <div ref="chartRef" class="donut-chart"></div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { qyyx_qytyzyljc } from '@/common/api/runMonitor' // 新增：导入企业运行接口
import { createSignedPayload } from '@/common/utils/auth' // 新增：导入签名工具

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
let currentIndex = -1

// 已删除本地 mock 数据，颜色保留用于后端数据渲染
let color = ['#C0FFB3', '#4DCB62', '#FF791A']

// 修改：默认 option 不再依赖本地 mock，xAxis/series 初始为空，tooltip 更稳健
const option = {
  color,
  grid: {
    top: 40,
    left: 24,
    bottom: 30,
    right: 8,
  },
  legend: {
    top: 0,
    inactiveBorderWidth: 0,
    textStyle: {
      color: '#fff',
    },
  },
  tooltip: {
    show: true,
    trigger: 'axis',
    axisPointer: {
      lineStyle: {
        color: '#C0FFB3',
      },
    },
    backgroundColor: 'rgba(11, 46, 115, 0.6)',
    borderColor: '#409FFF',
    backdropFilter: 'blur(4px)',
    textStyle: {
      color: '#fff',
    },
    formatter: (p: any) => {
      if (!p || !p.length) return ''
      const axisVal = p[0].axisValue ?? ''
      return (
        axisVal +
        p
          .map((i: any) => {
            const name = i.seriesName ?? i.name ?? ''
            return `<br/><div style="display:inline-block;margin-right:4px;width:12px;height:12px;background:${i.color};border-radius: 50%;"></div><div style="display:inline-block;">${name}: ${i.value}</div>`
          })
          .join('')
      )
    },
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#fff',
        type: 'solid',
        opacity: 0.3,
      },
    },
    boundaryGap: false,
    data: [], // 运行时由接口填充
  },
  yAxis: {
    type: 'value',
    name: '单位：Mpa',
    nameTextStyle: {
      color: '#fff',
      align: 'left',
    },
    splitLine: {
      lineStyle: {
        color: '#fff',
        opacity: 0.3,
        type: 'dashed',
      },
    },
    axisLabel: {
      color: '#fff',
    },
  },
  series: [], // 运行时由接口填充
}

const initchart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  chart.setOption(option)
  startHighlightAnimation()
}

// 修改：高亮轮播从图表当前 option 动态获取数据长度，避免引用已删除的 mock
const startHighlightAnimation = () => {
  if (!chart) return

  // 从当前图表 option 获取首个系列的数据长度（容错处理）
  const currentOpt: any = chart.getOption ? (chart.getOption() as any) : null
  const dataCount =
    currentOpt && Array.isArray(currentOpt.series) && currentOpt.series[0] && Array.isArray(currentOpt.series[0].data)
      ? currentOpt.series[0].data.length
      : 0

  if (dataCount === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮（如果存在）
    if (currentIndex >= 0) {
      chart?.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: currentIndex,
      })
    }

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

// 请根据实际项目把 key 放到安全的配置文件或环境变量中，这里为演示使用占位符
const API_KEY = '9900J7T2J91992'

// 新增：保存企业运行接口原始返回数据
const rawEnterpriseRunData = ref<any>(null)

// 修改：调用企业运行接口并保存返回，同时按 date/type 聚合并更新图表（去掉前后 avg 填充，避免多余点）
const fetchAndApplyEnterpriseRun = async () => {
  try {
    const signed = createSignedPayload({}, API_KEY)
    const res = await qyyx_qytyzyljc(signed)
    const body = res?.data ?? res
    rawEnterpriseRunData.value = body

    // 兼容各种包裹结构，提取数组列表
    let list: any[] | null = null
    if (Array.isArray(body)) list = body
    else if (Array.isArray(body?.data)) list = body.data
    else if (Array.isArray(body?.result)) list = body.result

    if (!Array.isArray(list) || list.length === 0) {
      console.debug('企业运行接口未返回数组数据，已保存原始返回：', body)
      return
    }

    // 提取并排序唯一日期（保证时间顺序）
    const dateSet = Array.from(new Set(list.map((i: any) => String(i.date ?? i.datetime ?? i.day ?? '')))).filter(
      Boolean,
    )
    dateSet.sort((a, b) => {
      const ta = new Date(String(a).replace(/\//g, '-')).getTime()
      const tb = new Date(String(b).replace(/\//g, '-')).getTime()
      return ta - tb
    })
    const dates = dateSet

    // 提取唯一类型（企业名称）
    const types = Array.from(new Set(list.map((i: any) => String(i.type ?? i.name ?? '').trim()))).filter(Boolean)

    // 构建每个类型对应的按日期排列的数值数组（如果同一 date/type 有多条则求和）
    const series = types.map((t: string, idx: number) => {
      const values = dates.map((d: string) => {
        const sum = list
          .filter(
            (it: any) =>
              String(it.date ?? it.datetime ?? it.day ?? '').trim() === d &&
              String(it.type ?? it.name ?? '').trim() === t,
          )
          .reduce((s: number, it: any) => s + Number(it.num ?? it.value ?? 0), 0)
        return sum
      })
      return {
        name: t,
        type: 'line',
        smooth: 0.5,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: { color: color[idx % color.length] },
        itemStyle: {
          color: color[idx % color.length],
          borderColor: '#fff',
          borderWidth: 1,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: color[idx % color.length] + '4D' },
              { offset: 1, color: color[idx % color.length] + '00' },
            ],
          },
        },
        // 直接使用真实值序列（不再在前后加入 avg），避免产生额外点
        data: values,
      }
    })

    // 生成新的 option 并刷新图表（xAxis 直接用 dates，不加前后空标签）
    const newOpt: any = JSON.parse(JSON.stringify(option))
    newOpt.xAxis = newOpt.xAxis || {}
    newOpt.xAxis.data = dates
    newOpt.series = series

    // 修正 tooltip formatter，基于当前传入的点数据构建显示内容
    newOpt.tooltip = {
      show: true,
      trigger: 'axis',
      axisPointer: { lineStyle: { color: '#C0FFB3' } },
      backgroundColor: 'rgba(11, 46, 115, 0.6)',
      borderColor: '#409FFF',
      backdropFilter: 'blur(4px)',
      textStyle: { color: '#fff' },
      formatter: (p: any) => {
        if (!p || !p.length) return ''
        return (
          (p[0].axisValue ?? '') +
          p
            .map(
              (i: any) =>
                `<br/><div style="display:inline-block;margin-right:4px;width:12px;height:12px;background:${i.color};border-radius: 50%;"></div><div style="display:inline-block;">${i.seriesName}: ${i.value}</div>`,
            )
            .join('')
        )
      },
    }

    // 打印处理后的横坐标数据，便于调试
    console.log('处理后的横坐标（dates）：', dates)

    chart?.setOption(newOpt, true)
    console.debug('企业运行数据已应用到图表，dates:', dates, 'types:', types)
  } catch (err) {
    console.error('获取企业运行数据失败', err)
  }
}

onMounted(() => {
  initchart()
  // 生命周期中调用企业运行接口并渲染图表
  fetchAndApplyEnterpriseRun()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.panel-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-section {
  width: 100%;
  height: 376px;
}

.donut-chart {
  width: 100%;
  height: 100%;
}
</style>
