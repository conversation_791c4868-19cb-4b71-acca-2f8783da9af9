<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">投诉趋势分析</div>
      <div class="header-dropdown">
        <div class="btn-group">
          <button class="period-btn" :class="{ active: selectedPeriod === 'week' }" @click="handlePeriodChange('week')">
            周
          </button>
          <button class="period-btn" :class="{ active: selectedPeriod === 'month' }" @click="handlePeriodChange('month')">
            月
          </button>
          <button class="period-btn" :class="{ active: selectedPeriod === 'year' }" @click="handlePeriodChange('year')">
            年
          </button>
        </div>
      </div>
    </div>
    <div class="panel-content p-2">
      <div ref="container" class="w-full h-full"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { onMounted, onBeforeUnmount, ref, watch } from 'vue'
import { tsjsc_tsqsfx } from '@/common/api/complaint'

const container = ref<HTMLDivElement | null>(null)
let chart: echarts.ECharts | null = null
const selectedPeriod = ref<string>('week')

// 不同周期的数据缓存
const dataMap = ref<{
  week: { x: string[], y: number[] }
  month: { x: string[], y: number[] }
  day: { x: string[], y: number[] }
}>({
  week: { x: [], y: [] },
  month: { x: [], y: [] },
  day: { x: [], y: [] }
})

// 加载数据
const loadData = async (type: 'month' | 'day' | 'week') => {
  try {
    const response = await tsjsc_tsqsfx({ type })
    if (response.success && response.data && Array.isArray(response.data)) {
      let xData: string[] = []
      let yData: number[] = []
      
      // 数据按时间倒序排列，需要反转以正序显示
      const sortedData = [...response.data].reverse()
      
      // 处理返回的数据格式: { number_value, stat_date, type }
      // 注意：API参数 day->周数据, week->月数据, month->年数据
      xData = sortedData.map((item: any) => {
        const dateStr = item.stat_date || ''
        
        // 根据API参数类型格式化显示文本
        if (type === 'day') {
          // day参数返回周数据：显示日期范围，如 "10/19-10/25"
          // 格式："2025/10/19-2025/10/25" → "10/19-10/25"
          const parts = dateStr.split('-')
          if (parts.length === 2) {
            const startDate = parts[0].substring(5) // 提取 "10/19"
            const endDate = parts[1].substring(5) // 提取 "10/25"
            return `${startDate}-${endDate}`
          }
        } else if (type === 'week') {
          // week参数返回月数据：显示月份，如 "10月"
          // 格式："2025/10" → "10月"
          const match = dateStr.match(/\d{4}\/(\d{1,2})/)
          if (match) {
            return `${parseInt(match[1])}月`
          }
        } else if (type === 'month') {
          // month参数返回年数据：显示年份，如 "2025年"
          // 格式："2025" → "2025年"
          const match = dateStr.match(/(\d{4})/)
          if (match) {
            return `${match[1]}年`
          }
        }
        return dateStr
      })
      
      yData = sortedData.map((item: any) => item.number_value || 0)
      
      dataMap.value[type] = { x: xData, y: yData }
      updateChart()
    }
  } catch (error) {
    console.error('加载投诉趋势分析数据失败:', error)
  }
}

const init = () => {
  if (!container.value) return
  chart = echarts.init(container.value)
  updateChart()
}

const updateChart = () => {
  if (!chart) return
  
  // 映射UI显示周期到API参数（与handlePeriodChange保持一致）
  const typeMap: { [key: string]: 'month' | 'day' | 'week' } = {
    'week': 'day',
    'month': 'week',
    'year': 'month'
  }
  const apiType = typeMap[selectedPeriod.value] || 'day'
  const data = dataMap.value[apiType]
  
  // 如果没有数据，不更新图表
  if (!data || data.x.length === 0) return
  
  chart.setOption({
    grid: { left: 30, right: 20, top: 20, bottom: 26 },
    xAxis: {
      type: 'category',
      data: data.x,
      boundaryGap: false,
      axisLabel: { color: '#66FFFF' },
      axisLine: { lineStyle: { color: 'rgba(102,255,255,.3)' } },
    },
    yAxis: {
      type: 'value',
      axisLabel: { color: '#66FFFF' },
      splitLine: { lineStyle: { color: 'rgba(102,255,255,.15)' } },
    },
    tooltip: { trigger: 'axis' },
    series: [
      {
        type: 'line',
        data: data.y,
        smooth: true,
        areaStyle: {
          color: new (echarts as any).graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255,217,102,.6)' },
            { offset: 1, color: 'rgba(255,217,102,0)' },
          ]),
        },
        lineStyle: { color: '#FFD966' },
        showSymbol: false,
      },
    ],
  })
}

const handlePeriodChange = (period: string) => {
  selectedPeriod.value = period
  // 映射显示周期到API参数
  // 注意：接口参数映射关系为 day->周, week->月, month->年
  const typeMap: { [key: string]: 'month' | 'day' | 'week' } = {
    'week': 'day',    // 周视图 -> API参数 day
    'month': 'week',  // 月视图 -> API参数 week
    'year': 'month'   // 年视图 -> API参数 month
  }
  loadData(typeMap[period] || 'day')
}

watch(selectedPeriod, () => {
  updateChart()
})

onMounted(() => {
  init()
  loadData('day') // 默认加载周数据（API参数为day）
})
onBeforeUnmount(() => chart?.dispose())
</script>

<style scoped>
@import '@/styles/index.css';

.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}

.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}
</style>
