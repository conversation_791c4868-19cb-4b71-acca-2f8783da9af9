<template>
  <div class="panel-container-half">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">气瓶充装监管</div>
      <div class="absolute z-10 flex justify-end w-full gap-2 top-3 right-4">
        <div
          class="w-10 h-6 text-center text-xs leading-[22px] border box-border border-[#99D5FF] bg-[#99D5FF]/15 cursor-pointer"
          :class="{ 'bg-[#FFD966]/15 border-[#FFD966]': activeTab === 'weekly' }"
          @click="activeTab = 'monthly'"
        >
          周
        </div>
        <div
          class="w-10 h-6 text-center text-xs leading-[22px] border box-border border-[#99D5FF] bg-[#99D5FF]/15 cursor-pointer"
          :class="{ 'bg-[#FFD966]/15 border-[#FFD966]': activeTab === 'monthly' }"
          @click="activeTab = 'monthly'"
        >
          月
        </div>
        <div
          class="w-10 h-6 text-center text-xs leading-[22px] border box-border border-[#99D5FF] bg-[#99D5FF]/15 cursor-pointer"
          :class="{ 'bg-[#FFD966]/15 border-[#FFD966]': activeTab === 'yearly' }"
          @click="activeTab = 'yearly'"
        >
          年
        </div>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import {
  getCylinderFillingWeekly,
  getCylinderFillingMonthly,
  getCylinderFillingYearly,
  type CylinderFillingData,
} from '@/common/api/dashboard'

const chartRef = ref<HTMLElement>()
const activeTab = ref<string>('weekly')
let chart: echarts.ECharts | null = null

// 气瓶充装监管数据
const cylinderFillingData = ref<CylinderFillingData[]>([])
// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1

let xAxis: string[] = []
let yData: number[] = []

const option = {
  backgroundColor: 'transparent',
  grid: {
    left: '0%',
    right: '0%',
    top: '15%',
    bottom: '0%',
    containLabel: true,
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      lineStyle: {
        color: '#C0FFB3',
      },
    },
    backgroundColor: 'rgba(11, 46, 115, 0.6)',
    borderColor: '#409FFF',
    backdropFilter: 'blur(4px)',
    textStyle: {
      color: '#fff',
    },
  },
  xAxis: [
    {
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#fff',
          type: 'solid',
          opacity: 0.3,
        },
      },
      data: xAxis,
    },
    {
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      data: xAxis,
    },
  ],
  yAxis: {
    type: 'value',
    name: '单位：个',
    nameTextStyle: {
      color: 'rgba(255, 255, 255, 1)',
      fontSize: 12,
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.3)',
      },
    },
    axisLabel: {
      color: 'rgba(255, 255, 255, 1)',
      fontSize: 12,
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.1)',
      },
    },
  },
  series: [
    {
      type: 'bar',
      barWidth: 20,
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(192, 255, 179, .65)' },
            { offset: 1, color: 'rgba(192, 255, 179, .15)' },
          ],
        },
        borderColor: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(192, 255, 179, .8)' },
            { offset: 1, color: 'rgba(192, 255, 179, .2)' },
          ],
        },
        borderWidth: 1,
      },
      data: yData,
    },
    {
      name: 'icon',
      type: 'pictorialBar',
      emphasis: {
        disabled: true,
      },
      symbol: 'rect',
      symbolRotate: 45,
      symbolPosition: 'end',
      symbolSize: [7, 7],
      symbolOffset: [0, '-50%'],
      z: 10,
      itemStyle: {
        color: '#C0FFB3',
      },
      tooltip: { show: false },
      data: yData,
    },
    {
      type: 'bar',
      xAxisIndex: 1,
      barWidth: 1,
      itemStyle: {
        color: 'transparent',
        borderColor: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(192, 255, 179, .65)' },
            { offset: 1, color: 'rgba(192, 255, 179, .15)' },
          ],
        },
        borderWidth: 1,
        borderType: [1, 3],
      },
      tooltip: { show: false },
      data: yData,
    },
  ],
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = option.xAxis[0].data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

// 更新图表数据
const updateChartData = () => {
  if (!chart) return

  xAxis = cylinderFillingData.value.map(item => item.date)
  yData = cylinderFillingData.value.map(item => item.count)

  chart.setOption({
    xAxis: [
      {
        data: xAxis,
      },
      {
        data: xAxis,
      },
    ],
    series: [
      {
        data: yData,
      },
      {
        data: yData,
      },
      {
        data: yData,
      },
    ],
  })
}

// 获取气瓶充装监管数据
const fetchCylinderFilling = async () => {
  try {
    const response = await getCylinderFillingWeekly()
    if (response && response.data) {
      cylinderFillingData.value = Array.isArray(response.data) ? response.data : []
      updateChartData()
    }
  } catch (error) {
    console.error('获取气瓶充装监管数据失败:', error)
  }
}

watch(activeTab, async () => {
  try {
    let response
    if (activeTab.value === 'weekly') {
      response = await getCylinderFillingWeekly()
    } else if (activeTab.value === 'monthly') {
      response = await getCylinderFillingMonthly()
    } else if (activeTab.value === 'yearly') {
      response = await getCylinderFillingYearly()
    }

    if (response && response.data) {
      cylinderFillingData.value = Array.isArray(response.data) ? response.data : []
      updateChartData()
    }
  } catch (error) {
    console.error('获取气瓶充装监管数据失败:', error)
  }
})

onMounted(async () => {
  initChart()
  window.addEventListener('resize', handleResize)
  await fetchCylinderFilling()
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.dropdown-btn {
  border: 1px solid #99d5ff;
}

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
