<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">巡检任务完成进度</div>
      <div class="header-dropdown">
        <div class="btn-group">
          <button
            class="period-btn"
            :class="{ active: selectedOpt === '城镇' }"
            @click="selectedOpt = '城镇'"
          >
            城镇
          </button>
          <button
            class="period-btn"
            :class="{ active: selectedOpt === '农村' }"
            @click="selectedOpt = '农村'"
          >
            农村
          </button>
        </div>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div class="status-indicators">
        <div class="status-item animate-pulse">
          <div class="status-value">{{ statusData.total_plan_count }}</div>
          <div class="status-label">计划任务</div>
        </div>
        <div class="status-item animate-pulse">
          <div class="status-value">{{ statusData.completed_plan_count }}</div>
          <div class="status-label">已完成</div>
        </div>
      </div>
      <div class="chart-section">
        <div class="rate-icon"></div>
        <div ref="chartRef" class="donut-chart"></div>
        <div class="triangle"></div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import { patrol_progress } from '@/common/api/patrol' // 导入接口


const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
const selectedOpt = ref<string>('城镇')
// 状态数据
const statusData = ref({
  total_plan_count: 0, // 计划任务
  completed_plan_count: 0, // 已完成
  completion_rate: 0 // 完成进度
})

// 图表数据
const yAxis = ['整体达标率']
const xData = ref([0])

const option = {
  backgroundColor: 'transparent',
  grid: {
    left: '20px',
    right: '20px',
    top: 0,
    height: 80,
    bottom: 0,
    containLabel: true,
  },
  tooltip: {
    show: false,
  },
  xAxis: {
    type: 'value',
    max: 100,
    axisTick: {
      show: false,
    },
    axisLine: {
      show: false,
    },
    splitLine: {
      show: false,
    },
    axisLabel: {
      show: false,
    },
  },
  yAxis: [
    {
      type: 'category',
      inverse: true,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      data: yAxis,
    },
    {
      inverse: true,
      offset: 0,
      axisLabel: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      data: [0],
    },
  ],
  series: [
    {
      type: 'bar',
      barWidth: 20,
      data: xData.value,
      showBackground: true,
      backgroundStyle: {
        color: 'rgba(140, 255, 255, 0.15)',
      },
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(64, 159, 255, 1)',
            },
            {
              offset: 1,
              color: 'rgba(71, 235, 235, 1)',
            },
          ],
        },
      },
      z: 2,
    },
    // 分隔
    {
      type: 'pictorialBar',
      itemStyle: {
        normal: {
          color: '#0E3169',
        },
      },
      symbolRepeat: 'fixed',
      symbolMargin: 8,
      symbol: 'rect',
      symbolClip: true,
      symbolSize: [2, 20],
      symbolPosition: 'start',
      symbolOffset: [-2, 0],
      data: [100, 100],
      // width: 60,
      label: {
        show: true,
        position: 'right',
        // distance: 20,
        offset: [20, 0],
        fontSize: 16,
        color: '#0E3169',
        // formatter: function (params) {
        //   // console.log(params)
        //   return dataObj[params.name] + '%'
        // },
      },
      z: 4,
      zlevel: 1,
    },
    // 左侧公司名称标签
    {
      type: 'scatter',
      symbolSize: 0,
      data: yAxis.map((_, index) => [0, index]),
      label: {
        show: true,
        position: [0, -15],
        color: '#fff',
        fontSize: 14,
        align: 'left',
        verticalAlign: 'bottom',
        formatter: (params: any) => yAxis[params.dataIndex],
      },
      z: 4,
    },
    // 右侧数值标签
    {
      type: 'scatter',
      symbolSize: 0,
      data: [[100, 0]],
      label: {
        show: true,
        position: [0, -15],
        color: '#66FFFF',
        fontSize: 20,
        align: 'right',
        verticalAlign: 'bottom',
        formatter: () => `${xData.value[0]}%`,
      },
      z: 4,
    },
  ],
}
const initchart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
}

// 获取巡检任务完成进度数据
const fetchProgressData = async (type: string) => {
  try {
    const param = { type }
    const res = await patrol_progress(param)

    if(!res.data || !res.data.length) return

    const body = res.data[0]

    if (body) {
      // 更新状态数据
      statusData.value = {
        total_plan_count: body.total_plan_count || 0,
        completed_plan_count: body.completed_plan_count || 0,
        completion_rate: body.completed_plan_count === 0 ? 0 : ((body.completed_plan_count / body.total_plan_count) * 100),
      }

      // 更新图表数据
      xData.value = [body.completion_rate || 0]

      // 重新渲染图表
      updateChart()
    } else {
      console.warn('巡检任务完成进度接口返回格式不符，未更新数据', res)
    }
  } catch (err) {
    console.error('获取巡检任务完成进度失败', err)
  }
}

const updateChart = () => {
  if (!chart) return
  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

// 监听按钮状态变化，重新请求新数据
watch(selectedOpt, (newType) => {
  fetchProgressData(newType)
})

onMounted(() => {
  initchart()
  // 在生命周期中调用接口并更新视图
  fetchProgressData(selectedOpt.value)
  window.addEventListener('resize', handleResize)

})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
@import '@/styles/index.css';

.panel-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-indicators {
  width: 100%;
  height: 128px;
  display: flex;
  justify-content: space-between;
}

.status-item {
  display: flex;
  width: 49%;
  background: url('@/assets/evaluate/resource-ind-bg.png') no-repeat center center;
  background-size: 120px 52px;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(64, 159, 255, 0) 0%, #409fff 49%, rgba(64, 159, 255, 0) 100%) 1;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 28px;
  text-align: center;
  color: #fff;
  white-space: nowrap;
}

.status-label {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  text-align: center;
  letter-spacing: normal;
  color: #ffffff;
}

.chart-section {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  height: 80px;
  background: linear-gradient(
    90deg,
    rgba(64, 159, 255, 0) 0%,
    rgba(64, 159, 255, 0.15) 50%,
    rgba(64, 159, 255, 0) 100%
  );
  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(64, 159, 255, 0) 0%, #409fff 49%, rgba(64, 159, 255, 0) 100%) 1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    border-top-width: 1px;
    border-left-width: 1px;
    border-style: solid;
    border-color: #66ffff;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 8px;
    border-top-width: 1px;
    border-right-width: 1px;
    border-style: solid;
    border-color: #66ffff;
  }
}

.triangle {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 8px;

  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 8px;
    height: 8px;
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-style: solid;
    border-color: #66ffff;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 8px;
    height: 8px;
    border-bottom-width: 1px;
    border-right-width: 1px;
    border-style: solid;
    border-color: #66ffff;
  }
}

.rate-icon {
  margin-left: 20px;
  width: 48px;
  height: 48px;
  background: url('@/assets/evaluate/resource-rate-icon.png') no-repeat center center;
  background-size: contain;
}

.donut-chart {
  flex: 1;
  height: 60px;
}
.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}

.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}
</style>
