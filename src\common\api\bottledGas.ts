import request from '@/utils/request'

// 请求体接口（根据实际字段调整）
export interface DeviceOverviewPayload {
  // ...根据接口实际需要添加字段
  [key: string]: any
}

// 响应数据接口（根据实际返回结构调整）
export interface DeviceOverviewResponse {
  code?: number
  message?: string
  data?: any
}
//瓶装石油气-资源概况
export function pzsyq_zygk(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/fxrq/pzsyq_zygk/1402191B43D68DD006AA8878CA340928', payload, {
    params: { applicationName: 'fxrq' },
  })
}

//瓶装石油气-气瓶类型分布
export function pzsyq_qplxfb(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/fxrq/pzsyq_qplxfb/1402191B43D68DD0682C703100D3F2294267DA18626723C1', payload, {
    params: { applicationName: 'fxrq' },
  })
}

//瓶装石油气-气瓶年限分布
export function pzsyq_qpnxfb(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/fxrq/pzsyq_qpnxfb/1402191B43D68DD0F5AD8CACC03AA9C54267DA18626723C1', payload, {
    params: { applicationName: 'fxrq' },
  })
}

//瓶装石油气-用户分析
export function pzsyq_yhfx(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/fxrq/pzsyq_yhfx/1402191B43D68DD067F8753527671354', payload, {
    params: { applicationName: 'fxrq' },
  })
}

//瓶装石油气-气瓶用途分析
export function pzsyq_qpytfx(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/fxrq/pzsyq_qpytfx/1402191B43D68DD06A19017C1C2818E9645FA1961DC5C423', payload, {
    params: { applicationName: 'fxrq' },
  })
}

//瓶装石油气-随瓶安检分析
export function pzsyq_spajfx(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {
  return request.post('/service-api/exposureAPIS/path/fxrq/pzsyq_spajfx/1402191B43D68DD0AA5982AE8DDE4427645FA1961DC5C423', payload, {
    params: { applicationName: 'fxrq' },
  })
}

//瓶装石油气-气瓶状态分析
export function pzsyq_qpztfx(payload: DeviceOverviewPayload = {}): Promise<DeviceOverviewResponse> {        
    return request.post('/service-api/exposureAPIS/path/fxrq/pzsyq_qpztfx/1402191B43D68DD0ACA984489FC87764645FA1961DC5C423', payload, {
        params: { applicationName: 'fxrq' },
    })
}