<template>
  <Dialog :open="props.open" @update:open="handleUpdateOpen">
    <DialogOverlay class="bg-[#2A384D]/20 backdrop-blur-sm" />
    <DialogContent
      class="sm:max-w-[800px] max-h-[80vh] p-0 bg-[#2A384D]/60 rounded-sm border-[#47EBEB] backdrop-blur-sm outline-none overflow-hidden flex flex-col"
    >
      <DialogHeader class="flex-shrink-0">
        <DialogTitle class="h-12 text-white pl-6 leading-12 title-bg">车辆详细信息</DialogTitle>
        <DialogDescription as="div" class="p-6 text-sm text-white overflow-y-auto max-h-[calc(80vh-3rem)]">
          <div v-if="loading" class="text-center py-8 text-[#99D5FF]">加载中...</div>
          <div v-else-if="error" class="text-center py-8 text-red-400">{{ error }}</div>
          <div v-else-if="detailData">
            <!-- 基本信息 -->
            <div class="mb-3 text-lg text-[#99D5FF] font-bold">基本信息</div>
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div class="flex">
                <div class="text-[#99D5FF] w-32">车辆编码：</div>
                <div>{{ detailData.code || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">车牌号：</div>
                <div>{{ detailData.number || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">车辆型号：</div>
                <div>{{ detailData.type || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">车辆分类：</div>
                <div>{{ detailData.classify || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">所属行政区：</div>
                <div>{{ detailData.district || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">车辆区域：</div>
                <div>{{ detailData.car_area || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">所属部门：</div>
                <div>{{ detailData.dept || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">车辆用途：</div>
                <div>{{ detailData.car_use || '--' }}</div>
              </div>
            </div>

            <!-- 车辆参数 -->
            <div class="mb-3 text-lg text-[#99D5FF] font-bold">车辆参数</div>
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div class="flex">
                <div class="text-[#99D5FF] w-32">载重：</div>
                <div>{{ detailData.weight || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">生产厂家：</div>
                <div>{{ detailData.production || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">出厂日期：</div>
                <div>{{ formatTime(detailData.production_date) }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">使用年限：</div>
                <div>{{ detailData.year || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">监测设备：</div>
                <div>{{ detailData.checkout_equipment || '--' }}</div>
              </div>
            </div>

            <!-- 证件信息 -->
            <div class="mb-3 text-lg text-[#99D5FF] font-bold">证件信息</div>
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div class="flex">
                <div class="text-[#99D5FF] w-32">行驶证编号：</div>
                <div>{{ detailData.driving_code || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">危险运输证编号：</div>
                <div>{{ detailData.danger_code || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">危险证发证日期：</div>
                <div>{{ formatTime(detailData.danger_public_date) }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">危险证有效日期：</div>
                <div>{{ formatTime(detailData.danger_validity_date) }}</div>
              </div>
              <div class="flex col-span-2">
                <div class="text-[#99D5FF] w-32">危险证发证机关：</div>
                <div>{{ detailData.danger_public_agency || '--' }}</div>
              </div>
            </div>

            <!-- 状态信息 -->
            <div class="mb-3 text-lg text-[#99D5FF] font-bold">状态信息</div>
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div class="flex">
                <div class="text-[#99D5FF] w-32">运输状态：</div>
                <div>{{ getTransportStateText(detailData.state) }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">签约状态：</div>
                <div>{{ getSignStateText(detailData.sign_state) }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">最近检修日期：</div>
                <div>{{ formatTime(detailData.recondition_date) }}</div>
              </div>
            </div>

            <!-- 负责人信息 -->
            <div class="mb-3 text-lg text-[#99D5FF] font-bold">负责人信息</div>
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div class="flex">
                <div class="text-[#99D5FF] w-32">车辆负责人：</div>
                <div>{{ detailData.person || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-32">负责人电话：</div>
                <div>{{ detailData.phone || '--' }}</div>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8 text-[#99D5FF]">暂无数据</div>
        </DialogDescription>
      </DialogHeader>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogOverlay,
} from '@/components/ui/dialog'
import { yscljsc_qyclgk_xq } from '@/common/api/transportVehicles'

const props = defineProps<{
  open: boolean
  recordId?: number | string
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

const detailData = ref<any>(null)
const loading = ref(false)
const error = ref('')

// 获取详情数据
const fetchDetail = async (id: number | string) => {
  if (!id) return

  loading.value = true
  error.value = ''
  try {
    const response = await yscljsc_qyclgk_xq({ id })
    if (response.data) {
      // 如果返回的是数组，取第一个元素
      detailData.value = Array.isArray(response.data) ? response.data[0] : response.data
    } else {
      error.value = '未获取到详情数据'
    }
  } catch (err) {
    console.error('获取详情失败:', err)
    error.value = '获取详情失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 监听弹窗打开和recordId变化
watch(
  () => [props.open, props.recordId] as const,
  ([isOpen, recordId]) => {
    if (isOpen && recordId) {
      fetchDetail(recordId)
    }
  },
  { immediate: true },
)

const handleUpdateOpen = (open: boolean) => {
  if (!open) {
    emit('close')
    // 清空数据
    detailData.value = null
    error.value = ''
  }
}

// 运输状态文本转换
const getTransportStateText = (state?: string) => {
  if (!state) return '--'
  return state === '1' ? '已运输' : state === '0' ? '暂停运输' : state
}

// 签约状态文本转换
const getSignStateText = (signState?: string) => {
  if (!signState) return '--'
  return signState === '1' ? '已签约' : signState === '0' ? '未签约' : signState
}

const formatTime = (t?: string | number | null) => {
  if (!t) return '--'
  try {
    let d: Date
    if (typeof t === 'number') {
      d = t > 1e12 ? new Date(t) : new Date(t * 1000)
    } else if (/^\d+$/.test(String(t))) {
      const num = Number(t)
      d = num > 1e12 ? new Date(num) : new Date(num * 1000)
    } else if (typeof t === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/.test(t)) {
      const date = new Date(t.replace('T', ' ').replace(/-/g, '/'))
      if (!isNaN(date.getTime())) {
        date.setHours(date.getHours() - 8)
        d = date
      } else {
        d = new Date(t)
      }
    } else {
      d = new Date(String(t))
    }
    if (isNaN(d.getTime())) return '--'
    const pad = (n: number) => String(n).padStart(2, '0')
    const Y = d.getFullYear()
    const M = pad(d.getMonth() + 1)
    const D = pad(d.getDate())
    const h = pad(d.getHours())
    const m = pad(d.getMinutes())
    const s = pad(d.getSeconds())
    return `${Y}-${M}-${D} ${h}:${m}:${s}`
  } catch (err) {
    return '--'
  }
}
</script>

<style scoped>
.title-bg {
  background: url('@/assets/dialog/title-bg-1200.png') no-repeat 0 0;
  background-size: cover;
  font-family: MStiffHei PRC;
}

/* 自定义滚动条样式 */
:deep(.overflow-y-auto)::-webkit-scrollbar {
  width: 6px;
}
:deep(.overflow-y-auto)::-webkit-scrollbar-track {
  background: rgba(64, 159, 255, 0.1);
  border-radius: 3px;
}
:deep(.overflow-y-auto)::-webkit-scrollbar-thumb {
  background: rgba(153, 213, 255, 0.4);
  border-radius: 3px;
}
:deep(.overflow-y-auto)::-webkit-scrollbar-thumb:hover {
  background: rgba(153, 213, 255, 0.6);
}
</style>

