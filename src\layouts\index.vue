<template>
  <div class="contain">
    <screen-header></screen-header>
    <slot />
    <screen-map ref="mapRef" @map-ready="onMapReady"></screen-map>
    <screen-warning :open="isShowWarning"></screen-warning>
    <screen-footer></screen-footer>
  </div>
</template>
<script setup lang="ts">
import screenHeader from './components/Header.vue'
import screenFooter from './components/Footer.vue'
import screenWarning from './components/Warning.vue'
import screenMap from '../components/Map.vue'
import { ref, provide, onUnmounted } from 'vue'

const isShowWarning = ref(false)
const mapRef = ref()
const mapReady = ref(false)

// 地图准备就绪事件处理
const onMapReady = () => {
  mapReady.value = true
}

// 10秒后显示告警组件
const showTimer = setTimeout(() => {
  isShowWarning.value = true
}, 10000)

// 告警显示10秒后消失
const hideTimer = setTimeout(() => {
  isShowWarning.value = false
}, 20000)

// 清理定时器
onUnmounted(() => {
  clearTimeout(showTimer)
  clearTimeout(hideTimer)
})

// 提供地图引用和准备状态给子组件使用
provide('mapRef', mapRef)
provide('mapReady', mapReady)
</script>
<style scoped>
.contain {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
