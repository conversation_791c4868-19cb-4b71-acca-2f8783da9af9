<template>
  <div class="panel-container">
    <div class="panel-header">
      <div class="header-title">未达标企业趋势分析</div>
      <div class="absolute z-10 flex justify-end w-full gap-2 top-3 right-4">
        <div
          class="w-10 h-6 text-center text-xs leading-[22px] border box-border border-[#99D5FF] bg-[#99D5FF]/15 cursor-pointer"
          :class="{ 'bg-[#FFD966]/15 border-[#FFD966]': activeTab === 'weekly' }"
          @click="activeTab = 'monthly'"
        >
          周
        </div>
        <div
          class="w-10 h-6 text-center text-xs leading-[22px] border box-border border-[#99D5FF] bg-[#99D5FF]/15 cursor-pointer"
          :class="{ 'bg-[#FFD966]/15 border-[#FFD966]': activeTab === 'monthly' }"
          @click="activeTab = 'monthly'"
        >
          月
        </div>
        <div
          class="w-10 h-6 text-center text-xs leading-[22px] border box-border border-[#99D5FF] bg-[#99D5FF]/15 cursor-pointer"
          :class="{ 'bg-[#FFD966]/15 border-[#FFD966]': activeTab === 'yearly' }"
          @click="activeTab = 'yearly'"
        >
          年
        </div>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div class="chart-section">
        <div ref="chartRef" class="donut-chart"></div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { getSafetyAssessmentNotEnterpriseTrend } from '@/common/api/safetyAssessment'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
let currentIndex = -1
const activeTab = ref<'weekly' | 'monthly' | 'yearly'>('weekly')

const months = ref<string[]>([])
const counts = ref<number[]>([])
const color = ['#47EBB4']

const buildOption = () => {
  const avg = counts.value.length ? counts.value.reduce((a, b) => a + b, 0) / counts.value.length : 0
  const xData = ['', ...months.value, '']
  const seriesData = [avg, ...counts.value, avg]

  return {
    color,
    grid: {
      top: 40,
      left: 24,
      bottom: 30,
      right: 8,
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        lineStyle: {
          color: '#47EBB4',
        },
      },
      textStyle: {
        color: '#fff',
      },
      backgroundColor: 'rgba(11, 46, 115, 0.6)',
      borderColor: '#409FFF',
      backdropFilter: 'blur(4px)',
      formatter: (p: any) => {
        if (p[0].dataIndex === 0 || p[0].dataIndex === seriesData.length - 1) {
          return ''
        } else {
          return `${p[0].axisValue}<br/><div style="display:inline-block;margin-right:4px;width:12px;height:12px;background:${p[0].color};border-radius: 50%;"></div><div style="display:inline-block;">未达标企业数: ${p[0].value}</div>`
        }
      },
    },
    xAxis: {
      type: 'category',
      axisTick: { show: false },
      axisLine: {
        lineStyle: { color: '#fff', type: 'solid', opacity: 0.3 },
      },
      boundaryGap: false,
      data: xData,
    },
    yAxis: {
      type: 'value',
      name: '单位：家',
      nameTextStyle: { color: '#fff', align: 'left' },
      splitLine: {
        lineStyle: { color: '#fff', opacity: 0.3, type: 'dashed' },
      },
      axisLabel: { color: '#fff' },
    },
    series: [
      {
        name: '未达标企业数',
        type: 'line',
        smooth: 0.5,
        symbol: 'circle',
        symbolSize: 8,
        markLine: {
          silent: true,
          symbol: ['none', 'arrow'],
          lineStyle: { color: '#FF791A', type: 'dashed', width: 1 },
          name: '预警',
          data: [
            {
              yAxis: Math.max(...counts.value, 0),
              label: { formatter: '预警', position: 'insideEndTop', color: '#FF791A' },
            },
          ],
        },
        lineStyle: { color: color[0] },
        itemStyle: {
          color: (p: any) => {
            if (p.dataIndex !== 0 && p.dataIndex !== xData.length - 1) return color[0]
          },
          borderColor: '#fff',
          borderWidth: 1,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{ offset: 0, color: color[0] + '4D' }, { offset: 1, color: color[0] + '00' }],
          },
        },
        data: seriesData,
      },
    ],
  }
}

const fetchTrend = async () => {
  try {
    const res: any = await getSafetyAssessmentNotEnterpriseTrend({})
    const list: any[] = Array.isArray(res?.data) ? res.data : Array.isArray(res) ? res : []
    months.value = list.map(it => String(it?.monthDay ?? ''))
    counts.value = list.map(it => Number(it?.count ?? 0))
    chart?.setOption(buildOption(), true)
    startHighlightAnimation()
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error('获取未达标企业趋势分析失败:', e)
  }
}

const initchart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  chart.setOption(buildOption())
  // startHighlightAnimation()
}

// 开始高亮轮播动画
const startHighlightAnimation = () => {
  const dataCount = counts.value.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0, // 作用在第二个系列（数据饼图）上
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initchart()
  fetchTrend()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.panel-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-section {
  width: 100%;
  height: 376px;
}

.donut-chart {
  width: 100%;
  height: 100%;
}
</style>
