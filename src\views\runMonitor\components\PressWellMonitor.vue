<template>
  <div class="panel-container-col">
    <div class="panel-header">阀门压力井监测</div>
    <div class="p-4 panel-content">
      <div class="status-indicators">
        <!-- 使用响应式值显示接口返回的数据（label 来源于后端 type） -->
        <div class="status-item animate-pulse">
          <div class="status-value">{{ highPressureCount }}</div>
          <div class="status-label">{{ highPressureLabel }}</div>
        </div>
        <div class="status-item animate-pulse">
          <div class="status-value">{{ midLowPressureCount }}</div>
          <div class="status-label">{{ midLowPressureLabel }}</div>
        </div>
        <div class="status-item animate-pulse">
          <div class="status-value">{{ lowPressureCount }}</div>
          <div class="status-label">{{ lowPressureLabel }}</div>
        </div>
      </div>
      <div class="chart-section">
        <div ref="chartRef" class="donut-chart"></div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { czsb_fmjyljc, czsb_fmjyljc_xq } from '@/common/api/runMonitor' // 新增：导入阀门压力井监测接口
import { createSignedPayload } from '@/common/utils/auth' // 新增：导入签名工具

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
let currentIndex = -1

// 响应式计数：高压 / 中低压 / 低压
const highPressureCount = ref<number>(3)
const midLowPressureCount = ref<number>(9)
const lowPressureCount = ref<number>(127)

// 新增：响应式 label，从后端 type 字段获取（默认值保留兜底）
const highPressureLabel = ref<string>('')
const midLowPressureLabel = ref<string>('')
const lowPressureLabel = ref<string>('')

// 新增：保存接口原始返回数据，但仍会解析 type/num 用于展示
const rawPressWellData = ref<any>(null)
// 新增：保存详情接口原始返回数据
const rawPressWellDetail = ref<any>(null)

// 请根据实际项目把 key 放到安全的配置文件或环境变量中，这里为演示使用占位符
const API_KEY = '9900J7T2J91992'

// 已删除本地 mock 数据，颜色保留用于后端数据渲染
let color = ['#FFF04C', '#FF4C4D', '#4BD9B5']

// 修改：默认 option 不再依赖本地 mock，xAxis/series 初始为空，tooltip 更稳健
const option = {
  color,
  grid: {
    top: 40,
    left: 24,
    bottom: 30,
    right: 8,
  },
  legend: {
    top: 0,
    inactiveBorderWidth: 0,
    textStyle: {
      color: '#fff',
    },
  },
  tooltip: {
    show: true,
    trigger: 'axis',
    axisPointer: {
      lineStyle: {
        color: '#FFF04C',
      },
    },
    backgroundColor: 'rgba(11, 46, 115, 0.6)',
    borderColor: '#409FFF',
    backdropFilter: 'blur(4px)',
    textStyle: {
      color: '#fff',
    },
    formatter: (p: any) => {
      if (!p || !p.length) return ''
      const axisVal = p[0].axisValue ?? ''
      return (
        axisVal +
        p
          .map((i: any) => {
            const name = i.seriesName ?? i.name ?? ''
            return `<br/><div style="display:inline-block;margin-right:4px;width:12px;height:12px;background:${i.color};border-radius: 50%;"></div><div style="display:inline-block;">${name}: ${i.value}</div>`
          })
          .join('')
      )
    },
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#fff',
        type: 'solid',
        opacity: 0.3,
      },
    },
    boundaryGap: false,
    data: [], // 运行时由接口填充
  },
  yAxis: {
    type: 'value',
    name: '单位：Mpa',
    nameTextStyle: {
      color: '#fff',
      align: 'left',
    },
    splitLine: {
      lineStyle: {
        color: '#fff',
        opacity: 0.3,
        type: 'dashed',
      },
    },
    axisLabel: {
      color: '#fff',
    },
  },
  series: [], // 初始无数据，接口返回后填充
}

const initchart = () => {
  if (!chartRef.value) return
  chart = echarts.init(chartRef.value)
  chart.setOption(option)
  startHighlightAnimation()
}

// 修改：高亮轮播从图表当前 option 动态获取数据长度，避免引用已删除的 mock
const startHighlightAnimation = () => {
  if (!chart) return

  // 从当前图表 option 获取首个系列的数据长度（容错处理）
  const currentOpt: any = chart.getOption ? (chart.getOption() as any) : null
  const dataCount =
    currentOpt && Array.isArray(currentOpt.series) && currentOpt.series[0] && Array.isArray(currentOpt.series[0].data)
      ? currentOpt.series[0].data.length
      : 0

  if (dataCount === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮（如果存在）
    if (currentIndex >= 0) {
      chart?.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: currentIndex,
      })
    }

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

// 修改：解析后端返回的 { type, num } 列表，优先使用 type 与 num 映射到页面
const fetchAndApplyPressWell = async () => {
  try {
    const signed = createSignedPayload({}, API_KEY)
    const res = await czsb_fmjyljc(signed)
    const body = res?.data ?? res

    rawPressWellData.value = body

    // 兼容数组和常见包裹形式
    let list: any[] | null = null
    if (Array.isArray(body)) {
      list = body
    } else if (Array.isArray(body?.data)) {
      list = body.data
    } else if (Array.isArray(body?.result)) {
      list = body.result
    }

    if (Array.isArray(list) && list.length > 0) {
      // 以 type / num 为准
      list.forEach((it: any) => {
        const typeStr = String(it.type ?? it.name ?? '').trim()
        const num = Number(it.num ?? it.value ?? 0)
        if (!typeStr) return

        if (typeStr.includes('高压')) {
          highPressureCount.value = num
          highPressureLabel.value = typeStr
        } else if (typeStr.includes('中低') || typeStr.includes('中低压')) {
          midLowPressureCount.value = num
          midLowPressureLabel.value = typeStr
        } else if (typeStr.includes('低压')) {
          lowPressureCount.value = num
          lowPressureLabel.value = typeStr
        } else {
          // 如果 type 与预期关键词不匹配，按顺序兜底填充未被赋值的槽位
          if (!highPressureAssigned()) {
            highPressureCount.value = num
            highPressureLabel.value = typeStr
          } else if (!midLowPressureAssigned()) {
            midLowPressureCount.value = num
            midLowPressureLabel.value = typeStr
          } else if (!lowPressureAssigned()) {
            lowPressureCount.value = num
            lowPressureLabel.value = typeStr
          }
        }
      })
    } else {
      // 无数组返回时仅保存原始数据，不改变当前展示
      console.debug('阀门压力井接口返回非数组，已保存但不修改展示：', body)
    }

    // 本地辅助判断函数（避免覆盖被正确赋值的槽位）
    function highPressureAssigned() {
      return highPressureLabel.value !== '高压阀门井' || highPressureCount.value !== 3
    }
    function midLowPressureAssigned() {
      return midLowPressureLabel.value !== '中低压阀门井' || midLowPressureCount.value !== 9
    }
    function lowPressureAssigned() {
      return lowPressureLabel.value !== '低压阀门井' || lowPressureCount.value !== 127
    }
  } catch (err) {
    console.error('获取阀门压力井监测失败', err)
  }
}

// 修改：调用详情接口并处理 { date, type, num } 数据，更新图表
const fetchPressWellDetail = async () => {
  try {
    const signed = createSignedPayload({}, API_KEY)
    const res = await czsb_fmjyljc_xq(signed)
    const body = res?.data ?? res
    rawPressWellDetail.value = body

    // 兼容各种包裹结构，提取数组列表
    let list: any[] | null = null
    if (Array.isArray(body)) list = body
    else if (Array.isArray(body?.data)) list = body.data
    else if (Array.isArray(body?.result)) list = body.result

    if (!Array.isArray(list) || list.length === 0) {
      console.debug('阀门压力井详情接口未返回数组数据，已保存原始返回：', body)
      return
    }

    // 规范字段访问函数
    const getDate = (it: any) => String(it.date ?? it.day ?? it.datetime ?? '').trim()
    const getType = (it: any) => String(it.type ?? it.name ?? '').trim()
    const getNum = (it: any) => Number(it.num ?? it.value ?? 0)

    // 提取并排序唯一日期
    const dates = Array.from(new Set(list.map(getDate))).filter(Boolean)
    dates.sort((a, b) => new Date(a.replace(/\//g, '-')).getTime() - new Date(b.replace(/\//g, '-')).getTime())

    // 提取唯一类型
    const types = Array.from(new Set(list.map(getType))).filter(Boolean)

    // 构建每个 type 对应的按日期排列的数值数组（同 date/type 多条求和）
    const series = types.map((t: string, idx: number) => {
      const values = dates.map((d: string) =>
        list
          .filter((it: any) => getDate(it) === d && getType(it) === t)
          .reduce((s: number, it: any) => s + getNum(it), 0),
      )
      return {
        name: t,
        type: 'line',
        smooth: 0.5,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: { color: color[idx % color.length] },
        itemStyle: { color: color[idx % color.length], borderColor: '#fff', borderWidth: 1 },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: color[idx % color.length] + '4D' },
              { offset: 1, color: color[idx % color.length] + '00' },
            ],
          },
        },
        data: values,
      }
    })

    // 生成新的 option 并刷新图表（直接使用 dates，不再填充额外点）
    const newOpt: any = JSON.parse(JSON.stringify(option))
    newOpt.xAxis = newOpt.xAxis || {}
    newOpt.xAxis.data = dates
    newOpt.series = series

    // 修正 tooltip 显示
    newOpt.tooltip = {
      show: true,
      trigger: 'axis',
      axisPointer: { lineStyle: { color: color[0] } },
      backgroundColor: 'rgba(11, 46, 115, 0.6)',
      borderColor: '#409FFF',
      backdropFilter: 'blur(4px)',
      textStyle: { color: '#fff' },
      formatter: (p: any) => {
        if (!p || !p.length) return ''
        return (
          (p[0].axisValue ?? '') +
          p
            .map(
              (i: any) =>
                `<br/><div style="display:inline-block;margin-right:4px;width:12px;height:12px;background:${i.color};border-radius: 50%;"></div><div style="display:inline-block;">${i.seriesName}: ${i.value}</div>`,
            )
            .join('')
        )
      },
    }

    // 打印处理后的横坐标，便于调试
    console.log('阀门压力井详情处理后的横坐标（dates）：', dates)

    chart?.setOption(newOpt, true)
    console.debug('阀门压力井详情已应用到图表，types:', types)
  } catch (err) {
    console.error('获取阀门压力井详情失败', err)
  }
}

onMounted(() => {
  initchart()
  // 生命周期进入时调用接口获取数据并更新视图
  fetchAndApplyPressWell()
  fetchPressWellDetail()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.panel-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-indicators {
  width: 100%;
  height: 104px;
  display: flex;
  justify-content: space-between;
}

.status-item {
  display: flex;
  width: 31%;
  background: url('@/assets/run-monitor/device-overview-icon.png') no-repeat center center;
  background-size: 120px 60px;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(64, 159, 255, 0) 0%, #409fff 49%, rgba(64, 159, 255, 0) 100%) 1;
}

.status-value {
  font-family: YouSheBiaoTiHei;
  font-size: 24px;
  font-weight: bold;
  line-height: 28px;
  text-align: center;
  color: #fff;
  white-space: nowrap;
}

.status-label {
  font-family: NotoSansSC;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  text-align: center;
  letter-spacing: normal;
  color: #fff;
}

.chart-section {
  width: 100%;
  height: 258px;
}

.donut-chart {
  width: 100%;
  height: 100%;
}
</style>
