<template>
  <div class="panel-container-col">
    <div class="panel-header">施工台账</div>
    <div class="p-4 panel-content">
      <Table class="w-full table-fixed">
        <colgroup>
          <col style="width: 25%" />
          <col style="width: 20%" />
          <col style="width: 15%" />
          <col style="width: 15%" />
          <col style="width: 10%" />
          <col style="width: 15%" />
        </colgroup>
        <TableHeader>
          <TableRow class="border-[#99D5FF]/30 hover:bg-transparent">
            <TableHead class="font-bold text-white">项目名称</TableHead>
            <TableHead class="font-bold text-white">施工单位</TableHead>
            <TableHead class="font-bold text-white">施工类型</TableHead>
            <TableHead class="font-bold text-white">计划工期</TableHead>
            <TableHead class="font-bold text-white">状态</TableHead>
            <TableHead class="font-bold text-white">操作</TableHead>
          </TableRow>
        </TableHeader>
      </Table>
      <div class="overflow-hidden" :style="{ height: `${8 * rowHeight}px` }" ref="tableContainerRef">
        <Table
          class="w-full table-fixed"
          :style="{
            transform: `translateY(-${scrollTop}px)`,
            transition: transitionEnabled ? 'transform 0.5s ease' : 'none',
          }"
        >
          <colgroup>
            <col style="width: 25%" />
            <col style="width: 20%" />
            <col style="width: 15%" />
            <col style="width: 15%" />
            <col style="width: 10%" />
            <col style="width: 15%" />
          </colgroup>
          <TableBody>
            <TableRow
              v-for="(item, index) in scrollList"
              :key="index"
              class="border-[#99D5FF]/30 hover:bg-[#99D5FF]/30"
              :style="{ height: `${rowHeight}px` }"
            >
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis" :title="item.project_name">{{ item.project_name }}</TableCell>
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis" :title="item.construction_unit">{{ item.construction_unit }}</TableCell>
              <TableCell>{{ getConstructionTypeText(item.construction_type) }}</TableCell>
              <TableCell class="whitespace-normal break-words">{{ item.plan_start_date }} ~ {{ item.plan_end_date }}</TableCell>
              <TableCell class="whitespace-normal break-words">
                <div class="flex flex-col gap-1">
                  <span :class="getStatusClass(item.security_agreement_signed, '安全协议')">{{ getStatusText(item.security_agreement_signed, '安全协议') }}</span>
                  <span :class="getStatusClass(item.construction_report, '施工报告')">{{ getStatusText(item.construction_report, '施工报告') }}</span>
                </div>
              </TableCell>
              <TableCell>
                <div class="flex gap-2">
                  <span class="text-[#99D5FF] cursor-pointer hover:underline" @click="handlePosMap(item)">定位</span>
                  <span class="text-[#99D5FF] cursor-pointer hover:underline" @click="handleShowDialog(item)">详情</span>
                </div>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Table,
  TableBody,
  // TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

const emit = defineEmits<{
  (e: 'click', value: any): void
  (e: 'pos', value: any): void
}>()

const handleShowDialog = (record: any) => {
  emit('click', record)
}

const handlePosMap = (record: any) => {
  emit('pos', record)
}

// 施工类型映射
const constructionTypeMap: Record<number, string> = {
  1: '道路维修',
  2: '管道改造',
  3: '绿化工程',
  4: '电缆铺设',
  5: '自来水管道'
}

const getConstructionTypeText = (type: number) => {
  return constructionTypeMap[type] || '未知类型'
}

// 状态文本和样式
const getStatusText = (status: number, type: string) => {
  return status === 1 ? `${type}已签` : `${type}未签`
}

const getStatusClass = (status: number, type: string) => {
  return status === 1 ? 'text-green-400 text-xs' : 'text-red-400 text-xs'
}

import { getConstructionLedger } from '@/common/api/hazard'

const sourceData = ref<any[]>([])

const fetchConstructionLedger = async () => {
  try {
    const res = await getConstructionLedger({})
    const data = Array.isArray(res?.data) ? res.data : []
    console.log('施工台账数据:', data)
    sourceData.value = data
  } catch (e) {
    console.error('获取施工台账失败:', e)
  }
}

const scrollList = computed(() => [...sourceData.value, ...sourceData.value])
const tableContainerRef = ref<HTMLElement | null>(null)
const scrollTimer = ref<any>(null)
const scrollTop = ref(0)
const rowHeight = 40 // 每行高度为40px
const transitionEnabled = ref(true)

const startScrolling = () => {
  stopScrolling()
  scrollTimer.value = setInterval(() => {
    scrollTop.value += rowHeight

    if (scrollTop.value >= sourceData.value.length * rowHeight) {
      transitionEnabled.value = false
      scrollTop.value = 0
  setTimeout(() => {
    transitionEnabled.value = true
  }, 50)
    }
  }, 3000)
}

const stopScrolling = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value)
    scrollTimer.value = null
  }
}

onMounted(() => {
  fetchConstructionLedger().then(() => {
    startScrolling()
    const container = tableContainerRef.value
    if (container) {
      container.addEventListener('mouseenter', stopScrolling)
      container.addEventListener('mouseleave', startScrolling)
    }
  })
})

onUnmounted(() => {
  stopScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.removeEventListener('mouseenter', stopScrolling)
    container.removeEventListener('mouseleave', startScrolling)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';
</style>
