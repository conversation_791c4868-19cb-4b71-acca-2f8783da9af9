<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogOverlay,
} from '@/components/ui/dialog'
import { getRiskDetail } from '@/common/api/doublePrevention'
import type { RiskDetailResponse } from '@/common/api/doublePrevention'

const props = defineProps<{
  open: boolean
  riskId?: number | string | null
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

const data = ref<RiskDetailResponse['data']>({
  id: 0,
  name: '',
  enterpriseId: 0,
  type: '',
  level: '',
  district: '',
  discoveryTime: '',
  status: '',
  processTime: '',
  measures: '',
})

const loading = ref(false)

// 获取风险详情数据
const fetchRiskDetail = async (id: number | string) => {
  loading.value = true
  try {
    const response = await getRiskDetail(id)
    if (response && response.data) {
      data.value = response.data
    }
  } catch (error) {
    console.error('获取风险详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听弹窗打开状态
watch(
  () => props.open,
  isOpen => {
    if (isOpen && props.riskId) {
      fetchRiskDetail(props.riskId)
    }
  },
)

// 处理更新打开状态的事件
const handleUpdateOpen = (open: boolean) => {
  if (!open) {
    emit('close')
  }
}
</script>

<template>
  <Dialog :open="props.open" @update:open="handleUpdateOpen">
    <DialogOverlay class="bg-[#2A384D]/20 backdrop-blur-sm" />
    <DialogContent
      class="sm:max-w-[900px] p-0 bg-[#2A384D]/60 rounded-sm border-[#47EBEB] backdrop-blur-sm outline-none"
    >
      <DialogHeader>
        <DialogTitle class="h-12 text-white pl-9 leading-12 title-bg">风险详情</DialogTitle>
        <DialogDescription as="div" class="p-6 text-sm text-white">
          <!-- 加载中状态 -->
          <div v-if="loading" class="text-center py-8 text-[#99D5FF]">加载中...</div>
          
          <!-- 风险详情 -->
          <div v-else>
            <div class="mb-4 text-lg text-[#99D5FF] font-bold">基本信息</div>
            <div class="grid grid-cols-2 gap-x-8 gap-y-4 mb-6">
              <div class="flex">
                <div class="w-28 text-[#99D5FF] text-right mr-4">风险名称：</div>
                <div class="flex-1">{{ data?.name || '-' }}</div>
              </div>
              <div class="flex">
                <div class="w-28 text-[#99D5FF] text-right mr-4">风险类型：</div>
                <div class="flex-1">{{ data?.type || '-' }}</div>
              </div>
              <div class="flex">
                <div class="w-28 text-[#99D5FF] text-right mr-4">风险等级：</div>
                <div class="flex-1">
                  <span
                    class="px-3 py-1 rounded"
                    :class="{
                      'bg-red-500/20 text-red-400': data?.level?.includes('高'),
                      'bg-orange-500/20 text-orange-400': data?.level?.includes('较大') || data?.level?.includes('中'),
                      'bg-yellow-500/20 text-yellow-400': data?.level?.includes('一般'),
                      'bg-blue-500/20 text-blue-400': data?.level?.includes('低'),
                    }"
                  >
                    {{ data?.level || '-' }}
                  </span>
                </div>
              </div>
              <div class="flex">
                <div class="w-28 text-[#99D5FF] text-right mr-4">所属区域：</div>
                <div class="flex-1">{{ data?.district || '-' }}</div>
              </div>
              <div class="flex">
                <div class="w-28 text-[#99D5FF] text-right mr-4">发现时间：</div>
                <div class="flex-1">{{ data?.discoveryTime || '-' }}</div>
              </div>
              <div class="flex">
                <div class="w-28 text-[#99D5FF] text-right mr-4">处理时间：</div>
                <div class="flex-1">{{ data?.processTime || '-' }}</div>
              </div>
              <div class="flex">
                <div class="w-28 text-[#99D5FF] text-right mr-4">处理状态：</div>
                <div class="flex-1">
                  <span
                    class="px-3 py-1 rounded"
                    :class="{
                      'bg-green-500/20 text-green-400': data?.status?.includes('已处理') || data?.status?.includes('完成'),
                      'bg-yellow-500/20 text-yellow-400': data?.status?.includes('处理中'),
                      'bg-red-500/20 text-red-400': data?.status?.includes('待处理') || data?.status?.includes('未处理'),
                    }"
                  >
                    {{ data?.status || '-' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 处理措施 -->
            <div v-if="data?.measures">
              <div class="mb-4 text-lg text-[#99D5FF] font-bold">处理措施</div>
              <div class="p-4 bg-[#1a2942]/50 rounded border border-[#99D5FF]/20">
                <div class="text-white leading-relaxed">{{ data?.measures }}</div>
              </div>
            </div>
          </div>
        </DialogDescription>
      </DialogHeader>
    </DialogContent>
  </Dialog>
</template>

<style scoped>
.title-bg {
  background:
    url('@/assets/dialog/title-bg-800.png') no-repeat center,
    linear-gradient(270deg, rgba(71, 235, 235, 0) 0%, rgba(71, 235, 235, 0) 60%, rgba(71, 235, 235, 0.3) 100%),
    linear-gradient(90deg, rgba(119, 168, 217, 0.3) 5%, rgba(105, 141, 191, 0.3) 80%, rgba(105, 141, 191, 0.3) 100%);
  background-size: cover;
  font-family: MStiffHei PRC;
}
</style>

