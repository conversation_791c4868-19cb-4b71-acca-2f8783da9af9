import request from '@/utils/request'

// 请求体接口
export interface TransportVehiclesPayload {
  [key: string]: any
}

// 车辆概况响应数据接口
export interface VehicleOverviewResponse {
  code?: number
  message?: string
  data?: {
    yhsyqpsc?: number // 液化石油气配送车辆
    pzqysc?: number // 瓶装气运输车
    xjcl?: number // 巡检车辆
    wxhxpysc?: number // 危险化学品运输车
    yhtrqysc?: number // 液化天然气运输车
    qtcl?: number // 其他车辆
  }
}

// 气瓶运输记录响应数据接口
export interface CylinderTransportRecordResponse {
  code?: number
  message?: string
  data?: any
}

// 气瓶运输记录详情响应数据接口
export interface CylinderTransportRecordDetailResponse {
  code?: number
  message?: string
  data?: any
}

// 企业服务车辆概况响应数据接口
export interface EnterpriseServiceVehicleResponse {
  code?: number
  message?: string
  data?: Array<{
    classify?: string // 分类
    num?: number // 数量
  }>
}

// 区域车辆数排行响应数据接口
export interface RegionalVehicleRankingResponse {
  code?: number
  message?: string
  data?: Array<{
    area?: string // 区域
    area_num?: number // 数量
  }>
}

// 企业车辆概况响应数据接口
export interface EnterpriseVehicleOverviewResponse {
  code?: number
  message?: string
  data?: Array<{
    district?: string // 所属区域
    type?: string // 车辆型号
    number?: string // 车牌号
    enterprise_for_id?: string // 所属服务企业
  }>
}

/**
 * 运输车辆监管-车辆概况
 */
export function yscljsc_clgk(
  payload: TransportVehiclesPayload = {},
): Promise<VehicleOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yscljsc/clgk/9F646E725A6C8A58984410DA628778D4',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 运输车辆监管-气瓶运输记录
 */
export function yscljsc_qpysjl(
  payload: TransportVehiclesPayload = {},
): Promise<CylinderTransportRecordResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yscljsc/qpysjl/9F646E725A6C8A58D40D49EDA57EB860',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 运输车辆监管-气瓶运输记录详情
 * @param payload - 入参：{ de_no: 单号 }
 */
export function yscljsc_qpysjl_xq(
  payload: { de_no: string },
): Promise<CylinderTransportRecordDetailResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yscljsc/qpysjl_xq/9F646E725A6C8A589AD4BD409017A82467B8E00379792EE6',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 运输车辆监管-企业服务车辆概况
 */
export function yscljsc_qyfwclgk(
  payload: TransportVehiclesPayload = {},
): Promise<EnterpriseServiceVehicleResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yscljsc/qyfwclgk/9F646E725A6C8A58292A3AB00D3473404DB02F93383C0102',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 运输车辆监管-区域车辆数排行
 */
export function yscljsc_qyclsph(
  payload: TransportVehiclesPayload = {},
): Promise<RegionalVehicleRankingResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yscljsc/qyclsph/9F646E725A6C8A58C614BBB1AC74C698',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 运输车辆监管-企业车辆概况
 */
export function yscljsc_qyclgk(
  payload: TransportVehiclesPayload = {},
): Promise<EnterpriseVehicleOverviewResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yscljsc/qyclgk/9F646E725A6C8A583CEBA5B952DE1847',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

/**
 * 运输车辆监管-企业车辆概况详情
 * @param payload - 入参：{ id: 记录ID }
 */
export function yscljsc_qyclgk_xq(
  payload: { id: number | string },
): Promise<CylinderTransportRecordDetailResponse> {
  return request.post(
    '/service-api/exposureAPIS/path/yscljsc/qyclgk_xq/9F646E725A6C8A583079B8DB5E37F7E367B8E00379792EE6',
    payload,
    {
      params: { applicationName: 'fxrq' },
    },
  )
}

