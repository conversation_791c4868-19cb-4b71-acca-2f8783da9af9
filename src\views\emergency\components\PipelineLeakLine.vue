<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import { getPipelineLeakTrend, type PipelineLeakTrend } from '@/common/api/emergency'
import { toast } from 'vue-sonner'

const activeTab = ref<'monthly' | 'quarterly'>('monthly')
const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1
const loading = ref(false)

// 图表数据
const monthlyData = ref({
  xAxis: ['2000.1', '2000.2', '20000.3', '2000.4', '2000.5', '2000.6', '2000.7', '2000.8'],
  data: [5, 4, 5, 3, 3, 4, 2, 4],
})

const quarterlyData = ref({
  xAxis: ['Q1', 'Q2', 'Q3', 'Q4'],
  data: [14, 10, 9, 12],
})

// 当前图表数据
const currentData = computed(() => {
  return activeTab.value === 'monthly' ? monthlyData.value : quarterlyData.value
})

const option = {
  backgroundColor: 'transparent',
  grid: {
    left: '2%',
    right: '0%',
    top: '15%',
    bottom: '0%',
    containLabel: true,
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: '#FFC61A',
      },
    },
    textStyle: {
      color: '#fff',
    },
    backgroundColor: 'rgba(11, 46, 115, 0.6)',
    borderColor: '#409FFF',
    backdropFilter: 'blur(4px)',
  },
  xAxis: {
    type: 'category',
    data: currentData.value.xAxis,
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.3)',
      },
    },
    axisLabel: {
      color: 'rgba(255, 255, 255, 0.8)',
      fontSize: 10,
    },
    axisTick: {
      show: false,
    },
    // boundaryGap: false,
  },
  yAxis: {
    type: 'value',
    name: '单位: 起',
    nameTextStyle: {
      color: 'rgba(255, 255, 255, 0.8)',
      fontSize: 10,
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.3)',
      },
    },
    axisLabel: {
      color: 'rgba(255, 255, 255, 0.8)',
      fontSize: 10,
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.1)',
        type: 'dashed',
      },
    },
  },
  legend: {
    show: false,
  },
  series: [
    {
      name: '泄漏发生',
      type: 'line',
      smooth: 0.5,
      symbol: 'circle',
      symbolSize: 8,
      data: currentData.value.data,
      itemStyle: {
        color: '#FFC61A',
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(255, 198, 26, 0.45)' },
            { offset: 1, color: 'rgba(255, 198, 26, 0)' },
          ],
        },
      },
    },
  ],
}

// 获取管道泄漏发生趋势数据
const fetchPipelineLeakTrend = async () => {
  try {
    loading.value = true
    const response = await getPipelineLeakTrend()

    if (response.data && Array.isArray(response.data)) {
      // 处理月度数据
      const monthlyItems = response.data.filter((item: PipelineLeakTrend) => item.month)
      if (monthlyItems.length > 0) {
        monthlyData.value = {
          xAxis: monthlyItems.map((item: PipelineLeakTrend) => item.month),
          data: monthlyItems.map((item: PipelineLeakTrend) => item.monthNum || 0),
        }
      }

      // 处理季度数据
      const quarterlyItems = response.data.filter((item: PipelineLeakTrend) => item.quarter)
      if (quarterlyItems.length > 0) {
        quarterlyData.value = {
          xAxis: quarterlyItems.map((item: PipelineLeakTrend) => item.quarter),
          data: quarterlyItems.map((item: PipelineLeakTrend) => item.quarterNum || 0),
        }
      }

      // 更新图表
      updateChart()
    }
  } catch (error) {
    console.error('获取管道泄漏发生趋势数据失败:', error)
    toast.error('获取管道泄漏发生趋势数据失败')
  } finally {
    loading.value = false
  }
}

// 更新图表数据
const updateChart = () => {
  if (chart) {
    chart.setOption({
      xAxis: {
        data: currentData.value.xAxis,
      },
      series: [
        {
          data: currentData.value.data,
        },
      ],
    })
  }
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startTooltipAnimation()

  // 获取数据
  fetchPipelineLeakTrend()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = option.xAxis.data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

// 监听tab切换
watch(activeTab, () => {
  updateChart()
})

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>
<template>
  <div class="panel-container-col">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">管道泄漏发生趋势</div>
      <div class="absolute z-10 flex justify-end w-full gap-2 top-3 right-4">
        <div
          class="w-10 h-6 text-center text-xs leading-[22px] border box-border border-[#99D5FF] bg-[#99D5FF]/15 cursor-pointer"
          :class="{ 'bg-[#FFD966]/15 border-[#FFD966]': activeTab === 'monthly' }"
          @click="activeTab = 'monthly'"
        >
          月
        </div>
        <div
          class="w-10 h-6 text-center text-xs leading-[22px] border box-border border-[#99D5FF] bg-[#99D5FF]/15 cursor-pointer"
          :class="{ 'bg-[#FFD966]/15 border-[#FFD966]': activeTab === 'quarterly' }"
          @click="activeTab = 'quarterly'"
        >
          季
        </div>
      </div>
    </div>
    <div class="relative p-4 panel-content">
      <div ref="chartRef" class="chart"></div>
    </div>
  </div>
</template>
<style scoped>
@import '@/styles/index.css';

.chart {
  width: 100%;
  height: 376px;
}
</style>
