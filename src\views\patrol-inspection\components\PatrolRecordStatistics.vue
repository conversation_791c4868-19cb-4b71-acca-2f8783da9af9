<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">近一年巡检记录统计</div>
      <div class="header-dropdown">
        <div class="btn-group">
          <select
            v-model="selectedCompany"
            class="company-select"
            @change="handleCompanyChange"
          >
            <option value="大名县中燃能源发展有限公司">大名县中燃能源发展有限公司</option>
          </select>
          <button
            class="period-btn"
            :class="{ active: selectedOpt === '城镇' }"
            @click="selectedOpt = '城镇'"
          >
            城镇
          </button>
          <button
            class="period-btn"
            :class="{ active: selectedOpt === '农村' }"
            @click="selectedOpt = '农村'"
          >
            农村
          </button>
        </div>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import { patrol_statistics } from '@/common/api/patrol' // 导入接口

const selectedOpt = ref<string>('城镇')
const selectedOpt2 = ref<string>('week')
const selectedCompany = ref<string>('大名县中燃能源发展有限公司')
const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1

// 图表数据
const xAxis = ref(['雄州镇', '安新镇', '容城镇', '大王镇', '小里镇', '大河镇', '八于乡', '其他乡镇'])
const allData = ref([0.7, 0.83, 0.78, 0.63, 0.61, 0.37, 0.36, 4.98])
const completedData = ref([0.65, 0.8, 0.77, 0.6, 0.6, 0.33, 0.34, 3.56])

const option = {
  animation: true,
  backgroundColor: 'transparent',
  grid: {
    left: '5%',
    right: '0%',
    top: '15%',
    bottom: '0%',
    containLabel: true,
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
      shadowStyle: {
        shadowColor: 'rgba(11, 46, 115, 0.3)',
        shadowBlur: 10,
      },
    },
    backgroundColor: 'rgba(11, 46, 115, 0.6)',
    borderColor: '#409FFF',
    backdropFilter: 'blur(4px)',
    textStyle: {
      color: '#fff',
    },
  },
  legend: {
    data: ['全部', '办结'],
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    itemWidth: 20,
    itemHeight: 10,
    icon: 'rect',
    itemGap: 24,
    top: 0,
    // itemStyle: {
    //   color: ['#E19760', '#99D5FF']
    // }
  },
  // show: false,
  xAxis: {
    type: 'category',
    data: xAxis.value,
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.45)',
      },
    },
    axisLabel: {
      color: '#fff',
      fontSize: 14,
    },
    axisTick: {
      show: false,
    },
  },
  yAxis: {
    type: 'value',
    name: '单位：次',
    nameTextStyle: {
      color: '#fff',
      fontSize: 14,
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.45)',
      },
    },
    axisLabel: {
      color: '#fff',
      fontSize: 14,
      formatter: function(value: number) {
        return Number.isInteger(value) ? value.toString() : ''
      }
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.1)',
        type: 'dashed',
      },
    },
  },
  series: [
    {
      name: '全部',
      type: 'bar',
      data: allData.value,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(71, 235, 235, 1)' },
          { offset: 1, color: 'rgba(26, 178, 255, 0.15)' },
        ]),
        borderColor: 'rgba(71, 235, 235, 1)',
        borderWidth: 2,
      },
      barWidth: 20,
      barCategoryGap: '40%',
      barGap: '30%', // 增加与办结柱子的间距
      z: 2,
    },

    // 贯穿的虚线
    {
      type: 'pictorialBar',
      symbol: 'rect',
      symbolSize: [1, '100%'],
      symbolPosition: 'center',
      symbolOffset: [-12, 0],
      z: 4,
      itemStyle: {
        color: 'transparent',
        borderColor: '#FFC61A',
        borderWidth: 1,
        borderType: 'solid',
      },
      tooltip: { show: false },
      data: allData.value,
    },
    {
      name: '办结',
      type: 'bar',
      data: completedData.value,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(255, 198, 26, 1)' },
          { offset: 1, color: 'rgba(255, 76, 77, 0.15)' },
        ]),
        borderColor: 'rgba(255, 198, 26, 1)',
        borderWidth: 2,
      },
      barWidth: 20,
      barCategoryGap: '40%',
      barGap: '20%', // 增加与全部柱子的间距
      z: 2,
    },

    // 贯穿的虚线
    {
      type: 'pictorialBar',
      symbol: 'rect',
      symbolSize: [1, '100%'],
      symbolPosition: 'center',
      symbolOffset: [12, 0],
      z: 4,
      itemStyle: {
        color: 'transparent',
        borderColor: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(192, 255, 179, .65)' },
            { offset: 1, color: 'rgba(192, 255, 179, .15)' },
          ],
        },
        borderWidth: 1,
        borderType: 'solid',
      },
      tooltip: { show: false },
      data: completedData.value,
    },
    {
      name: '完成率',
      type: 'line',
      data: allData.value,
      symbol: 'circle',
      symbolSize: 10,
      itemStyle: {
        color: '#FF791A',
        borderColor: '#fff',
        borderWidth: 2,
      },
      lineStyle: {
        color: '#FF791A',
        width: 3,
      },
      z: 10,
      smooth: true,
    },
  ],
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = xAxis.value.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

// 处理公司选择变化
const handleCompanyChange = () => {
  fetchStatisticsData(selectedOpt.value)
}

// 获取近一年巡检记录统计数据
const fetchStatisticsData = async (type: string) => {
  try {
    const param = { type, name: selectedCompany.value }
    const res = await patrol_statistics(param)

    // 接口返回格式：{ msg, success, data: [{record_count, month_year}], pageInfo }
    const dataList = res?.data || []

    if (Array.isArray(dataList) && dataList.length > 0) {
      // 按月份分组数据
      const monthData: { [key: string]: { total: number; completed: number } } = {}

      dataList.forEach((item: any) => {
        const month = item.month_year || ''
        const count = Number(item.record_count) || 0

        if (!monthData[month]) {
          monthData[month] = { total: 0, completed: 0 }
        }
        monthData[month].total += count
        // 假设已完成数量，实际需要根据接口返回数据结构调整
        monthData[month].completed += count 
      })

      // 获取月份并排序
      const months = Object.keys(monthData).sort()

      // 更新图表数据
      xAxis.value = months.length > 0 ? months : ['暂无数据']
      allData.value = months.map(month => monthData[month].total)
      completedData.value = months.map(month => monthData[month].completed)

      // 重新渲染图表
      updateChart()
    } else {
      console.warn('近一年巡检记录统计接口返回格式不符，未更新数据', res)
      // 清空图表数据
      xAxis.value = ['暂无数据']
      allData.value = [0]
      completedData.value = [0]
      updateChart()
    }
  } catch (err) {
    console.error('获取近一年巡检记录统计失败', err)
    // 出错时清空图表数据
    xAxis.value = ['暂无数据']
    allData.value = [0]
    completedData.value = [0]
    updateChart()
  }
}

const updateChart = () => {
  if (!chart) return
  // 使用动态数据更新图表
  chart.setOption({
    xAxis: {
      data: xAxis.value
    },
    series: [
      {
        name: '全部',
        data: allData.value
      },
      {
        // 贯穿的虚线
        data: allData.value
      },
      {
        name: '办结',
        data: completedData.value
      },
      {
        // 贯穿的虚线
        data: completedData.value
      },
      {
        name: '完成率',
        data: allData.value
      }
    ]
  })
}

const handleResize = () => {
  chart?.resize()
}

// 监听按钮状态变化，重新请求新数据
watch(selectedOpt, (newType) => {
  fetchStatisticsData(newType)
})

onMounted(() => {
  initChart()
  // 在生命周期中调用接口并更新视图
  fetchStatisticsData(selectedOpt.value)
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  // 清理所有定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
}

.company-select {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 20px;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='6' viewBox='0 0 10 6'%3E%3Cpath fill='%23fff' d='M0 0l5 6 5-6z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  padding-right: 24px;
  min-width: 180px;
}

.company-select:hover {
  background: rgba(74, 158, 255, 0.1);
}

.company-select option {
  background: #1a3a5c;
  color: #fff;
  padding: 8px;
}

.chart-container {
  width: 100%;
  height: 100%;
}
.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}
.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}
</style>
