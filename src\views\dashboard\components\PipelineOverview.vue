<template>
  <div class="panel-container">
    <div class="panel-header">燃气管网</div>
    <div>
      <ul class="p-4">
        <li v-for="(row, idx) in indList" :key="idx" class="flex gap-4">
          <p
            v-for="item in row"
            :key="item.id"
            class="flex items-center grow basis-0 h-14 leading-14 box-border border-b border-[#409fff]/30"
          >
            <span class="flex-none text-sm">{{ item.name }}</span>
            <span class="text-[#66FFFF] text-xl text-right grow">{{ item.value }}</span>
            <span class="text-[#66FFFF] text-sm">{{ item.unit }}</span>
          </p>
        </li>
      </ul>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getPipelineNetwork, type PipelineNetworkData } from '@/common/api/dashboard'

interface PipelineItem {
  name: string
  value: number | string
  unit: string
  id: number
}

const indList = ref<PipelineItem[][]>([
  [
    { name: '管网总长', value: '--', unit: 'KM', id: 0 },
    { name: '高压管网', value: '--', unit: 'KM', id: 1 },
    { name: '中压管网', value: '--', unit: 'KM', id: 2 },
    { name: '低压管网', value: '--', unit: 'KM', id: 3 },
  ],
  [
    { name: '管段数量', value: '--', unit: '段', id: 4 },
    { name: '节点数量', value: '--', unit: '个', id: 5 },
    { name: '城镇管网', value: '--', unit: 'KM', id: 6 },
    { name: '农村管网', value: '--', unit: 'KM', id: 7 },
  ],
  [
    { name: '管龄≤15年', value: '--', unit: 'KM', id: 8 },
    { name: '管龄＞15年', value: '--', unit: 'KM', id: 9 },
  ],
  [
    { name: '管网燃气探测器', value: '--', unit: '台', id: 12 },
    { name: '泄漏报警', value: '--', unit: '台', id: 13 },
    { name: '亏电设备', value: '--', unit: '台', id: 14 },
    { name: '离线设备', value: '--', unit: '台', id: 15 },
  ],
])

// 获取燃气管网数据
const fetchPipelineNetwork = async () => {
  try {
    const response = await getPipelineNetwork()
    if (response && response.data) {
      const data: PipelineNetworkData = Array.isArray(response.data) ? response.data[0] : response.data
      if (data) {
        // 更新数据
        indList.value[0][0].value = data.pipe_length || 0 // 管网总长
        indList.value[0][1].value = data.high_pressure || 0 // 高压管网
        indList.value[0][2].value = data.mid_pressure || 0 // 中压管网
        indList.value[0][3].value = data.low_pressure || 0 // 低压管网

        indList.value[1][0].value = data.pipe_count || 0 // 管段数量
        indList.value[1][1].value = data.node_count || 0 // 节点数量
        indList.value[1][2].value = data.town_length || 0 // 城镇管网
        indList.value[1][3].value = data.village_length || 0 // 农村管网

        indList.value[2][0].value = data.little_age || 0 // 管龄≤15年
        indList.value[2][1].value = data.large_age || 0 // 管龄＞15年

        indList.value[3][0].value = data.detector || 0 // 管网燃气探测器
        indList.value[3][1].value = data.leak_alarm || 0 // 泄漏报警
        indList.value[3][2].value = data.undervoltage_device || 0 // 亏电设备
        indList.value[3][3].value = data.offline_device || 0 // 离线设备
      }
    }
  } catch (error) {
    console.error('获取燃气管网数据失败:', error)
  }
}

onMounted(() => {
  fetchPipelineNetwork()
})
</script>
<style scoped>
@import '@/styles/index.css';
</style>
