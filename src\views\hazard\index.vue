<template>
  <div class="dashboard-layout">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <div class="flex flex-col gap-6">
        <HazardSource />
        <SurroundingVideo />
        <HazardReport />
      </div>
    </div>

    <!-- 中央地图区域 -->
    <div class="center-panel">
      <!-- 地图控制组件 -->
      <LayersTool v-model="layersData" />
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <Tabs default-value="tab1">
        <TabsList class="flex flex-col h-auto absolute top-0 right-[744px] border-0 bg-[#409fff]/40 backdrop-blur-sm">
          <TabsTrigger
            value="tab1"
            class="vertical-tab h-auto border-0 bg-none text-[#66ffff] cursor-pointer data-[state=active]:bg-[#409fff]/70 data-[state=active]:text-white"
          >
            第三方施工监管
          </TabsTrigger>
          <TabsTrigger
            value="tab2"
            class="vertical-tab h-auto border-0 bg-none text-[#66ffff] cursor-pointer data-[state=active]:bg-[#409fff]/70 data-[state=active]:text-white"
          >
            重点防控管控
          </TabsTrigger>
        </TabsList>
        <TabsContent value="tab1">
          <div class="flex flex-col gap-6">
            <CorpSafetyManager />
            <ConstructionLedger @click="onShowConstructionDialog" @pos="onShowMapPoint" />
          </div>
        </TabsContent>
        <TabsContent value="tab2">
          <div class="flex flex-col gap-6">
            <LevelRatio />
            <CorpHazardRank />
          </div>
        </TabsContent>
      </Tabs>
    </div>

    <!-- UE弹窗 -->
    <HazardDialog :open="hazardInfoOpen" @close="hazardInfoOpen = false" />
    <PipelineDialog :open="pipelineInfoOpen" @close="pipelineInfoOpen = false" />
    <AreaDialog :open="areaInfoOpen" @close="areaInfoOpen = false" />
    <!-- 施工详情弹窗 -->
    <ConstructionDetailDialog :open="showConstructionDialog" :data="constructInfo" @close="showConstructionDialog = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import SurroundingVideo from './components/SurroundingVideo.vue'
import HazardSource from './components/HazardSource.vue'
import HazardReport from './components/HazardReport.vue'
import LevelRatio from './components/LevelRatio.vue'
import CorpSafetyManager from './components/CorpSafetyManager.vue'
import CorpHazardRank from './components/CorpHazardRank.vue'
import ConstructionLedger from './components/ConstructionLedger.vue'
import LayersTool from '@/components/LayersTool.vue'
import HazardDialog from '@/components/ue-dialog/HazardDialog.vue'
import PipelineDialog from '@/components/ue-dialog/PipelineDialog.vue'
import AreaDialog from '@/components/ue-dialog/AreaDialog.vue'
import ConstructionDetailDialog from './ConstructionDetailDialog.vue'
import { layerData } from './layerData'

// 弹窗状态

const hazardInfoOpen = ref(false)
const pipelineInfoOpen = ref(false)
const areaInfoOpen = ref(false)

const showConstructionDialog = ref<boolean>(false)
const constructInfo = ref<any>({})

// 图层数据
const layersData = ref(layerData)

// 监听layersData的变化
watch(layersData, newLayers => {
  console.log('Layers data updated:', newLayers)
  // 可以在这里处理图层数据的变化
})

const onShowConstructionDialog = (record: any) => {
  constructInfo.value = record
  showConstructionDialog.value = true
  // 发送点击事件到UE
  onShowMapPoint(record)
}

const onShowMapPoint = (record: any) => {
  record // 引用 record 以避免 TS6133 错误
}

// 模拟API调用
// const fetchHazardInfo = async (id: string) => {
//   console.log('Fetching station info for id:', id)
//   await new Promise(resolve => setTimeout(resolve, 200))
//   return {
//     id,
//     title: `危险源 ${id}`,
//     personInCharge: '李**',
//     address: '模拟地址',
//     level: '二级',
//     industry: '模拟燃气公司',
//     phone: '13800138000',
//     videos: [{ url: 'https://example.com/video.mp4' }],
//   }
// }

// const fetchPipelineInfo = async (id: string) => {
//   console.log('Fetching pipeline info for id:', id)
//   await new Promise(resolve => setTimeout(resolve, 200))
//   return {
//     id,
//     name: `管线 ${id}`,
//     pipeType: '中压',
//     industry: '模拟燃气公司',
//     material: 'PE',
//     burialDepth: '1.5m',
//     commissioningTime: '2022-01-01',
//     node: 'NodeA-NodeB',
//     personInCharge: '王**',
//     phone: '13900139000',
//   }
// }

// const fetchAreaInfo = async (id: string) => {
//   console.log('Fetching area info for id:', id)
//   await new Promise(resolve => setTimeout(resolve, 200))
//   return {
//     id,
//     title: `风险区域 ${id}`,
//     address: '模拟风险地址',
//     personInCharge: '赵**',
//     riskLevel: '二级风险',
//     phone: '13700137000',
//     videos: [{ url: 'https://example.com/video.mp4' }],
//   }
// }

// 交互处理

onMounted(() => {
  // todo
})

onUnmounted(() => {
  // 清除所有图层
})
</script>

<style scoped>
.dashboard-layout {
  position: absolute;
  top: 96px;
  left: 24px;
  right: 24px;
  height: calc(100% - 96px);
  overflow: hidden;
}

.left-panel,
.right-panel {
  position: absolute;
  top: 0;
  width: 744px;
}

.left-panel {
  left: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.right-panel {
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.center-panel {
  position: absolute;
  top: 0;
  left: 768px;
  z-index: 10;
}

/* Tabs 样式重写 */
:deep(.vertical-tab) {
  writing-mode: vertical-lr;
  text-orientation: upright;
  height: auto;
  padding: 0.75rem 0.25rem;
  line-height: 1.5;
  letter-spacing: 0.1em;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .dashboard-content {
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    padding: 20px;
    padding-top: 120px;
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 280px 1fr 280px;
    gap: 16px;
    padding: 16px;
    padding-top: 120px;
  }
}
</style>
