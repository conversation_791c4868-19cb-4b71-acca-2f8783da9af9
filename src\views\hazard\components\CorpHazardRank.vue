<template>
  <div class="panel-container-col">
    <div class="panel-header">企业重点防控点位排行</div>
    <div class="panel-content ranking-container">
      <div class="ranking-list">
        <div
          v-for="(item, index) in rankingData"
          :key="index"
          class="flex gap-4 items-center ranking-item"
          :class="{ active: currentHighlight === index }"
        >
          <!-- 排名序号 -->
          <div class="w-11 h-11 text-center leading-11 rank-number">
            {{ String(index + 1).padStart(2, '0') }}
          </div>

          <!-- 内容区域 -->
          <div class="grow content-area">
            <!-- 标签行 -->
            <div class="label-row">
              <span class="label-text">{{ item.label }}</span>
              <span class="value-text">{{ item.value }}</span>
            </div>

            <!-- 进度条区域 -->
            <div class="progress-container">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: `${(item.value / maxValue) * 100}%` }">
                  <!-- 虚线 -->
                  <div class="dashed-line"></div>
                  <!-- 三角形指示器 -->
                  <div class="triangle-indicator"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { getCorpHazardRank } from '@/common/api/hazard'

// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
const currentHighlight = ref(-1)

const rankingData = ref<any[]>([])

const fetchCorpHazardRank = async () => {
  try {
    const res = await getCorpHazardRank({})
    const data = Array.isArray(res?.data) ? res.data : []
rankingData.value = data.map((item: any) => ({
    label: item.enterprise || '',
    value: item.hazard_count || 0
  }))
  } catch (e) {
    console.error('获取企业重点防控点位排行失败:', e)
  }
}

// 计算最大值，用于进度条百分比计算
const maxValue = computed(() => {
  return Math.max(...rankingData.value.map(item => item.value))
})

// 开始高亮轮播动画
const startHighlightAnimation = () => {
  if (rankingData.value.length === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 循环移动到下一个点
    currentHighlight.value = (currentHighlight.value + 1) % rankingData.value.length
  }, 2000) // 每隔2秒执行
}

onMounted(() => {
  fetchCorpHazardRank().then(() => {
    startHighlightAnimation()
  })
})
</script>

<style scoped>
@import '@/styles/index.css';

.ranking-container {
  padding: 20px 24px !important;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.ranking-item {
  transition: all 0.3s ease;
}

.ranking-item.active .progress-fill {
  animation: pulse 2s infinite;
  box-shadow: 0 0 12px rgba(102, 255, 255, 0.8);
}

.rank-number {
  font-size: 18px;
  font-weight: bold;
  color: #4fd1c7;
  font-family: 'Arial', monospace;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(64, 159, 255, 0.3) 14%, rgba(64, 159, 255, 0) 79%);
  box-sizing: border-box;
  border: 1px solid #409fff;
  overflow: hidden;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
}

.label-text {
  color: #fff;
  font-size: 14px;
  font-weight: normal;
}

.value-text {
  color: #66ffff;
  font-size: 20px;
  font-weight: bold;
  font-family: 'Arial', monospace;
}

.progress-container {
  position: relative;
}

.progress-bar {
  height: 24px;
  background: transparent;
  border-radius: 0;
  overflow: visible;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, rgba(102, 255, 255, 0.15) 0%, rgba(102, 255, 255, 0.65) 100%);
  border: 1px solid #66ffff;
  border-radius: 0;
  position: relative;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 2px;
}

.dashed-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  border-top: 1px dashed #66ffff;
  transform: translateY(-50%);
  z-index: 10;
}

.triangle-indicator {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-right: 12px solid #66ffff;
  border-top: 9px solid transparent;
  border-bottom: 9px solid transparent;
  z-index: 15;
}

/* 高亮动画 */
@keyframes pulse {
  0%,
  100% {
    box-shadow: 0 0 8px rgba(102, 255, 255, 0.6);
  }
  50% {
    box-shadow: 0 0 16px rgba(102, 255, 255, 0.9);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ranking-container {
    padding: 16px 20px !important;
  }

  .ranking-list {
    gap: 16px;
  }

  .rank-number {
    font-size: 16px;
    min-width: 28px;
  }

  .label-text,
  .value-text {
    font-size: 11px;
  }

  .progress-bar {
    height: 18px;
  }
}
</style>
