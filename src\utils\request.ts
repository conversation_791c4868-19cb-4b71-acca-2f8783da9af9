import axios from 'axios'
import type { AxiosResponse, AxiosInstance, InternalAxiosRequestConfig } from 'axios'
import { toast } from 'vue-sonner'
import { createTimestamp, buildSignString, signString } from '@/common/utils/auth' // 新增导入

// 创建axios实例
const request: AxiosInstance = axios.create({
  // 开发环境 baseURL: '/api',
  // 生产环境 baseURL: ''
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 确保 config 不为 undefined
    if (!config) {
      throw new Error('请求配置不能为空')
    }
    // 确保 config.url 不为 undefined
    if (!config.url) {
      throw new Error('请求URL不能为空')
    }

    // 固定使用写死的 token
    const fixedToken = 'e2df1a5e-0190-4f5b-8dd8-7c4f5dd85368'
    // 确保 headers 存在再赋值
    config.headers = config.headers || {}
    config.headers.Authorization = `Bearer ${fixedToken}`

    // --- 修改：使用固定签名 key（与示例完全一致） ---
    try {
      const signKey = '9900J7T2J91992' // 按照图片/要求固定 key

      // 依据请求方法选择待签名内容：GET -> params, 其它 -> data
      const method = (config.method || 'get').toLowerCase()
      const payload = method === 'get' ? (config.params ?? {}) : (config.data ?? {})

      const timestamp = createTimestamp()
      const signStr = buildSignString(payload, timestamp)
      const sign = signString(signStr, signKey)

      // 将 Timestamp 和 Sign 放入请求头（均为字符串）
      config.headers.Timestamp = String(timestamp)
      config.headers.Sign = String(sign)
    } catch (e) {
      // 签名异常不应阻塞请求，但打印以便排查
      // eslint-disable-next-line no-console
      console.warn('签名生成失败，继续发送请求：', e)
    }
    // --- 新增结束 ---

    return config
  },
  error => {
    return Promise.reject(error)
  },
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data
  },
  error => {
    let message = ''
    // 处理错误响应
    if (error.response) {
      const status = error.response.status
      if (status === 401) {
        message = '未授权，请登录'
      } else if (status === 403) {
        message = '没有权限访问'
      } else if (status === 404) {
        message = '请求的资源不存在'
      } else if (status >= 500) {
        message = '服务器错误，请稍后再试'
      } else {
        message = `请求失败: ${error.response.data.message || '未知错误'}`
      }
    } else {
      message = '网络错误，请检查您的网络连接'
    }

    toast(message)

    console.error('请求失败:', error)
    return Promise.reject(error)
  },
)

export default request
