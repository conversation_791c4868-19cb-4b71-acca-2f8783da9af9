<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { getEmergencyPlanLevels, type EmergencyPlanLevel } from '@/common/api/emergency'
import { toast } from 'vue-sonner'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
let currentIndex = -1
const loading = ref(false)

// 图表数据
const chartData = ref([
  { value: 40, name: '特别重大', itemStyle: { color: '#FF4C4D' } },
  { value: 25, name: '重大', itemStyle: { color: '#FF791A' } },
  { value: 15, name: '较大', itemStyle: { color: '#FFC61A' } },
  { value: 20, name: '一般', itemStyle: { color: '#4BD9B5' } },
])

const option = {
  backgroundColor: 'transparent',
  title: {
    left: '39%', //?
    top: '38%',
    // textAlign: 'center',
    // text: '材质分析',
    // textStyle: {
    //   color: '#fff',
    //   fontSize: 14,
    // },
    // subtext: '116.2',
    subtextStyle: {
      color: '#fff',
      fontSize: 24,
    },
  },
  legend: {
    show: true,
    orient: 'vertical',
    right: 0,
    bottom: 0,
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    itemWidth: 12,
    itemHeight: 8,
    icon: 'rect',
  },
  series: [
    // {
    //   type: 'pie',
    //   radius: ['36.1%', '74.1%'],
    //   center: ['40%', '50%'],
    //   silent: true,
    //   data: [{ value: 1, itemStyle: { color: 'rgba(153, 213, 255, 0.15)' } }],
    //   label: {
    //     show: false,
    //   },
    //   labelLine: {
    //     show: false,
    //   },
    // },
    {
      type: 'pie',
      radius: ['20%', '70%'],
      center: ['50%', '50%'],
      roseType: 'area',
      data: chartData.value,
      label: {
        show: true,
        color: '#fff',
        formatter: `{percent|{d}%}\n{value|{c}}个`,
        rich: {
          percent: {
            fontSize: 20,
            color: '#fff',
          },
          value: {
            fontSize: 12,
            color: '#fff',
          },
        },
      },
      labelLine: {
        show: true,
      },
    },
  ],
}

// 获取应急预案分级统计数据
const fetchEmergencyPlanLevels = async () => {
  try {
    loading.value = true
    const response = await getEmergencyPlanLevels()

    if (response.data && Array.isArray(response.data)) {
      const levelColorMap: Record<string, string> = {
        特别重大: '#FF4C4D',
        重大: '#FF791A',
        较大: '#FFC61A',
        一般: '#4BD9B5',
      }

      chartData.value = response.data.map((item: EmergencyPlanLevel) => ({
        value: item.number || 0,
        name: item.level || '未知',
        itemStyle: { color: levelColorMap[item.level] || '#999999' },
      }))

      // 更新图表
      if (chart) {
        chart.setOption({
          series: [
            {
              data: chartData.value,
            },
          ],
        })
      }
    }
  } catch (error) {
    console.error('获取应急预案分级统计数据失败:', error)
    toast.error('获取应急预案分级统计数据失败')
  } finally {
    loading.value = false
  }
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startHighlightAnimation()

  // 获取数据
  fetchEmergencyPlanLevels()
}

// 开始高亮轮播动画
const startHighlightAnimation = () => {
  const dataCount = option.series[0].data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 1, // 作用在第二个系列（数据饼图）上
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 1, // 作用在第二个系列（数据饼图）上
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>
<template>
  <div class="panel-container-col">
    <div class="panel-header">应急预案分析统计</div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="donut-chart"></div>
    </div>
  </div>
</template>
<style scoped>
@import '@/styles/index.css';

.donut-chart {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
