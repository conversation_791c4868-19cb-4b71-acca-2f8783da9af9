<template>
  <div class="panel-container">
    <div class="panel-header">
      <span class="header-title">企业安全评分TOP5</span>
    </div>
    <div class="p-4 panel-content">
      <div class="ranking-list">
        <div v-for="(item, index) in rankingData" :key="index" class="ranking-item">
          <div class="ranking-number" :class="`rank-${index + 1}`">
            {{ index + 1 }}
          </div>
          <div class="company-name">
            {{ item.name }}
          </div>
          <div class="company-score">
            {{ item.score }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getSafetyAssessmentSecurityScoreTop5 } from '@/common/api/safetyAssessment'

interface RankingItem {
  name: string
  score: string
}

const rankingData = ref<RankingItem[]>([])

const fetchTop5 = async () => {
 try {
   const res: any = await getSafetyAssessmentSecurityScoreTop5({})
   // 兼容 { data: [ ... ] } 或直接数组
   const list: any[] = Array.isArray(res?.data) ? res.data : Array.isArray(res) ? res : []
   const mapped = list
     .map((it: any) => ({
       name: String(it?.name ?? ''),
       score: String(it?.evaluate_score ?? ''),
     }))
     .filter(i => i.name)
     .sort((a, b) => Number(b.score) - Number(a.score))
     .slice(0, 5)

   rankingData.value = mapped
 } catch (e) {
   // eslint-disable-next-line no-console
   console.error('获取企业安全评分TOP5失败:', e)
 }
}

onMounted(() => {
 fetchTop5()
})
</script>

<style scoped>
@import '@/styles/index.css';

.panel-content {
  padding: 20px;
  height: calc(100% - 64px);
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 3px 16px 3px 3px;
  height: 38px;
  background: rgba(91, 159, 212, 0.1);
  border-radius: 38px;
  border: 1px solid rgba(91, 159, 212, 0.2);
  transition: all 0.3s ease;
}

.ranking-item:hover {
  background: rgba(91, 159, 212, 0.2);
  border-color: rgba(91, 159, 212, 0.4);
}

.ranking-number {
  width: 32px;
  height: 32px;
  line-height: 30px;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #4fd1c7;
  font-family: 'Arial', monospace;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(64, 159, 255, 0.3) 14%, rgba(64, 159, 255, 0) 79%);
  box-sizing: border-box;
  border: 1px solid #409fff;
  box-shadow: inset -4px -4px 4px 0px rgba(64, 159, 255, 0.3);
  overflow: hidden;
}

.company-name {
  flex: 1;
  font-size: 14px;
  color: #ffffff;
  font-family: 'Noto Sans SC', sans-serif;
  line-height: 1.4;
}

.company-score {
  font-size: 20px;
  font-weight: bold;
  color: #66ffff;
  font-family: 'Noto Sans SC', sans-serif;
  min-width: 60px;
  text-align: right;
}
</style>
