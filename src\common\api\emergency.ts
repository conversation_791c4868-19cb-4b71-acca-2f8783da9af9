import requestOur from '@/utils/request_our'
import request from '@/utils/request'

/**
 * 通用响应结构
 */
export interface ApiResponse<T = any> {
  code?: number
  message?: string
  msg?: string
  success?: boolean
  data?: T
}

// 应急资源数据结构
export interface EmergencyResourceData {
  teamCount: number // 应急队伍
  warehouseCount: number // 物资仓库
  carCount: number // 抢修车辆
  hospitalCount: number // 医院
  fireFacilityCount: number // 消防设施
}

// 最新资源入库数据结构
export interface LatestResourceEntry {
  entryTime: string // 入库时间
  name: string // 标题
  warehouseName: string // 所属仓库名称
}

// 应急预案分级统计数据结构
export interface EmergencyPlanLevel {
  level: string // 等级
  number: number // 数量
}

// 应急演练次数排行数据结构
export interface EmergencyDrillRanking {
  enterpriseName: string // 企业名
  number: number // 次数
}

// 管道泄露告警数据结构
export interface PipelineLeakAlarm {
  id: string // 编号
  deviceIdPosition: Array<{
    deviceNumber: string // 设备编号
    devicePositionX: number // x坐标
    devicePositionY: number // y坐标
    alarmTime: string // 告警时间
  }>
  leakPosition: number[] // 漏点坐标
  leakData: {
    leakPosition: number[] // 漏点坐标
    enterpriseId: string // 关联企业id
    phoneNumber: string // 联系方式
    pipeLine: string // 所属管线
    pipeSection: string // 所属管段
    beginSection: number[] // 开始管段坐标
    endSection: number[] // 结束管段坐标
  }
  explosionData: {
    rtnExplosionReportStruct: any // 爆炸数据
    impactRadius: number // 影响半径
    affectedPopulation: number // 影响人口
    maxExplosionPressure: number // 最大压力
    pressureRiseRate: number // 压力上升速率
    populationDensity: number // 人口密度
    burnEnergy: number // 燃烧热值
    dataList: any[] // 画图数据坐标
    positionP: number[] // 压力坐标
    positionR: number[] // 位置坐标
  }
  diffusionData: {
    valueQ: number // 泄漏速率
    u: number // 风速
    windDirection: number // 风向角度
    stabilityClass: string // 环境稳定等级
    conditionStatus: string // 环境类型
    conditionStatusName: string // 环境类型名称
    url: string // 扩散图片存储地址
    area: number // 扩散面积
    length: number // 扩散距离
    range: string // 爆炸极限
    positionRightUp: number[] // 图片右上角坐标
    positionLeftDown: number[] // 图片左下角坐标
    affectedSection: any[] // 受影响管段
    id: string // 记录id
    beginPosition: number[] // 管段开始坐标
    endPosition: number[] // 管段结束坐标
    fid: string // 管段id
    layer: string // 企业
    layerName: string // 企业简称
    enterpriseId: string // 关联企业id
  }
  emergencyData: {
    reCar: Array<{
      id: string
      carNumber: string // 车牌
      carPerson: string // 姓名
      carPhone: string // 联系方式
      position: number[] // 坐标
    }>
    reFireFacility: Array<{
      id: string
      name: string // 名称
      longitude: number // 坐标
      latitude: number // 坐标
    }>
    reTeam: Array<{
      id: string
      teamName: string // 队伍名称
      teamPeople: string // 人员
      teamPhone: string // 联系方式
      position: number[] // 坐标
    }>
    reHospital: Array<{
      id: string
      name: string // 医院名称
      position: number[] // 坐标
    }>
  }
  userNature: string // 用户类型
  dealStatus: string // 处置状态
  alarmStatus: string // 告警状态
  alarmDuration: string // 告警时长
  enterprise: {
    enterpriseId: string // 企业id
    enterpriseName: string // 企业名称
  }
  handleList: Array<{
    gasAlarmMsgId: string // 告警关联id
    person: string // 处置人
    phone: string // 电话
    content: string // 内容
    pic: string // 图片地址
    handleTime: string // 处置时间
  }>
  createdAt: string // 创建时间
}

// 管道泄漏发生趋势数据结构
export interface PipelineLeakTrend {
  month: string // 月份
  monthNum: number // 月份数据
  quarter: string // 季度
  quarterNum: number // 季度数据
}

// 最新事故列表数据结构
export interface LatestAccident {
  accidentTime: string // 发生时间
  name: string // 事故名称
  cause: string // 事故原因
}

// 管道泄露告警查询参数
export interface PipelineLeakAlarmParams {
  startDate?: string // 开始日期
  endDate?: string // 结束日期
  gasSectionNumber?: string // 管段编号
  enterpriseName?: string // 企业名称
  dealStatus?: string // 处置状态
  pageNum?: number // 页码
  pageSize?: number // 页面大小
}

/**
 * 获取应急资源统计
 */
export function getEmergencyResources(): Promise<ApiResponse<EmergencyResourceData>> {
  return requestOur.get('/gasPlatform/emResources/command/getResourcesNumber')
}

/**
 * 获取最新资源入库信息
 */
export function getLatestResourceEntries(): Promise<ApiResponse<LatestResourceEntry[]>> {
  return requestOur.get('/gasPlatform/emResources/command/getNewstGoods')
}

/**
 * 获取应急预案分级统计
 */
export function getEmergencyPlanLevels(): Promise<ApiResponse<EmergencyPlanLevel[]>> {
  return requestOur.get('/gasPlatform/emResources/command/getPlanCountByType')
}

/**
 * 获取本年应急演练次数排行
 */
export function getEmergencyDrillRankings(): Promise<ApiResponse<EmergencyDrillRanking[]>> {
  return requestOur.get('/gasPlatform/emResources/command/getDrillCountByType')
}

/**
 * 获取管道泄露告警列表
 */
export function getPipelineLeakAlarms(params: PipelineLeakAlarmParams = {}): Promise<
  ApiResponse<{
    rows: PipelineLeakAlarm[]
    total: number
  }>
> {
  const queryParams = new URLSearchParams({
    pageNum: '1',
    pageSize: '10',
    ...Object.fromEntries(Object.entries(params).map(([k, v]) => [k, String(v)])),
  })

  // 注意：这个接口使用不同的baseURL
  return requestOur.get(`/gasModels/alarm/list?${queryParams}`)
}

/**
 * 获取管道泄漏发生趋势
 */
export function getPipelineLeakTrend(): Promise<ApiResponse<PipelineLeakTrend[]>> {
  // 注意：这个接口使用不同的baseURL
  return requestOur.get('/gasModels/alarm/leakTrend')
}

/**
 * 获取最新事故列表
 */
export function getLatestAccidents(): Promise<ApiResponse<LatestAccident[]>> {
  return requestOur.get('/gasPlatform/emResources/command/getNewestAccident')
}
