<template>
  <Dialog :open="props.open" @update:open="handleUpdateOpen">
    <DialogOverlay class="bg-[#2A384D]/20 backdrop-blur-sm" />
    <DialogContent
      class="sm:max-w-[1000px] p-0 bg-[#2A384D]/60 rounded-sm border-[#47EBEB] backdrop-blur-sm outline-none max-h-[90vh] overflow-y-auto"
    >
      <DialogHeader>
        <DialogTitle class="h-12 text-white pl-6 leading-12 title-bg">施工详情</DialogTitle>
        <DialogDescription as="div" class="p-6 max-h-[75vh] overflow-auto text-sm text-white custom-scrollbar">
          <!-- 施工详情 -->
          <div class="mb-6">
            <h3 class="mb-4 text-lg font-bold text-[#99D5FF]">基本信息</h3>
            <div class="detail-grid-3">
              <div class="detail-label">项目名称：</div>
              <div class="detail-value">{{ constructionData.project_name || '--' }}</div>
              
              <div class="detail-label">施工单位：</div>
              <div class="detail-value">{{ constructionData.construction_unit || '--' }}</div>
              
              <div class="detail-label">施工类型：</div>
              <div class="detail-value">{{ getConstructionTypeText(constructionData.construction_type) }}</div>
              
              <div class="detail-label">施工地址：</div>
              <div class="detail-value">{{ constructionData.construction_adress || '--' }}</div>
              
              <div class="detail-label">计划开始：</div>
              <div class="detail-value">{{ constructionData.plan_start_date || '--' }}</div>
              
              <div class="detail-label">计划结束：</div>
              <div class="detail-value">{{ constructionData.plan_end_date || '--' }}</div>
            </div>
          </div>

          <!-- 负责人信息 -->
          <div class="mb-6">
            <h3 class="mb-4 text-lg font-bold text-[#99D5FF]">负责人信息</h3>
            <div class="detail-grid-2">
              <div class="detail-label">负责人：</div>
              <div class="detail-value">{{ constructionData.construction_supervisor || '--' }}</div>
              
              <div class="detail-label">联系电话：</div>
              <div class="detail-value">{{ constructionData.supervisor_phone || '--' }}</div>
              
              <div class="detail-label">燃气旁站人员：</div>
              <div class="detail-value">{{ constructionData.gas_side_people || '--' }}</div>
              
              <div class="detail-label">旁站人员电话：</div>
              <div class="detail-value">{{ constructionData.gas_side_people_phone || '--' }}</div>
            </div>
          </div>

          <!-- 报备状态 -->
          <div class="mb-6">
            <h3 class="mb-4 text-lg font-bold text-[#99D5FF]">报备状态</h3>
            <div class="detail-grid-2">
              <div class="detail-label">主动报备：</div>
              <div class="detail-value">
                <span :class="constructionData.report_proactively ? 'text-green-400' : 'text-gray-400'">
                  {{ constructionData.report_proactively ? '是' : '否' }}
                </span>
              </div>
              
              <div class="detail-label">施工报备：</div>
              <div class="detail-value">
                <span :class="constructionData.construction_report ? 'text-green-400' : 'text-gray-400'">
                  {{ constructionData.construction_report ? '是' : '否' }}
                </span>
              </div>
              
              <div class="detail-label">安全协议：</div>
              <div class="detail-value">
                <span :class="constructionData.security_agreement_signed ? 'text-green-400' : 'text-red-400'">
                  {{ constructionData.security_agreement_signed ? '已签' : '未签' }}
                </span>
              </div>
              
              <div class="detail-label">企业告知：</div>
              <div class="detail-value">
                <span :class="constructionData.enterprise_inform_situation ? 'text-green-400' : 'text-gray-400'">
                  {{ constructionData.enterprise_inform_situation ? '已告知' : '未告知' }}
                </span>
              </div>
            </div>
          </div>

          <!-- 地图区域 -->
          <div class="mb-6">
            <h3 class="mb-4 text-lg font-bold text-[#99D5FF]">施工位置</h3>
            <div class="w-full h-[280px] bg-black/20 rounded">
              <Map ref="mapRef" :zoom="15" :pitch="20" @map-ready="onMapReady" />
            </div>
          </div>

          <!-- 处置流单 -->
          <div>
            <h3 class="mb-4 text-lg font-bold text-[#99D5FF]">处置流单</h3>
            <div class="flex items-center gap-4 mb-6">
              <span class="text-[#99D5FF] whitespace-nowrap">处置人：</span>
              <Select v-model="formData.processor" class="w-48">
                <SelectTrigger class="h-8 text-sm dropdown-btn">
                  <SelectValue placeholder="请选择" />
                </SelectTrigger>
                <SelectContent class="text-[#99D5FF]">
                  <SelectGroup>
                    <SelectItem value="user1">张三</SelectItem>
                    <SelectItem value="user2">李四</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
              <Button
                class="bg-[#77A8D9] rounded-sm hover:bg-[#77A8D9]/80 text-white outline-none px-8 h-8"
                @click="handleExecuteFlow"
              >
                执行流单
              </Button>
            </div>
          </div>
        </DialogDescription>
      </DialogHeader>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogOverlay,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Map from '@/components/Map.vue'

const props = defineProps<{
  open: boolean
  data?: any
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

// 施工类型映射
const constructionTypeMap: Record<number, string> = {
  1: '道路维修',
  2: '管道改造',
  3: '绿化工程',
  4: '电缆铺设',
  5: '自来水管道'
}

const getConstructionTypeText = (type: number | undefined) => {
  if (!type) return '暂无数据'
  return constructionTypeMap[type] || '未知类型'
}

// 地图组件的引用
const mapRef = ref<InstanceType<typeof Map> | null>(null)
// 表单数据
const formData = ref({
  processor: '',
  workTime: '',
  reason: '',
  remark: '',
})

// 施工详情数据
const constructionData = ref<any>({})

// 监听 props.data 的变化
watch(() => props.data, (newData) => {
  if (newData) {
    constructionData.value = newData
    console.log('施工详情数据:', newData)
  }
}, { immediate: true })

// 监听弹窗打开状态，更新地图标记
watch(() => props.open, (isOpen) => {
  if (isOpen && constructionData.value?.longitude && constructionData.value?.latitude) {
    // 等待地图组件准备好
    setTimeout(() => {
      updateMapMarker()
    }, 500)
  }
})

// 处理更新打开状态的事件
const handleUpdateOpen = (open: boolean) => {
  if (!open) {
    mapRef.value?.destroyPolygon()
    emit('close')
  }
}

// 执行流单
const handleExecuteFlow = () => {
  console.log('执行流单', formData.value)
  // 这里可以添加执行流单的API调用
}

// 地图准备好后的回调
const onMapReady = () => {
  updateMapMarker()
}

// 更新地图标记
const updateMapMarker = () => {
  if (mapRef.value && constructionData.value?.longitude && constructionData.value?.latitude) {
    const { longitude, latitude } = constructionData.value
    // 在地图上标记施工位置
    console.log('标记施工位置:', longitude, latitude)
    // 这里可以调用地图组件的方法添加标记
    // mapRef.value.addMarker(longitude, latitude)
  }
}
</script>

<style scoped>
.title-bg {
  background: url('@/assets/dialog/title-bg-1200.png') no-repeat 0 0;
  background-size: cover;
  font-family: MStiffHei PRC;
}

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  outline: none;
}

/* 基本信息：3列布局 */
.detail-grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 110px 1fr);
  gap: 12px 16px;
  align-items: flex-start;
}

/* 负责人信息和报备状态：2列布局 */
.detail-grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 130px 1fr);
  gap: 12px 20px;
  align-items: flex-start;
}

.detail-label {
  color: #99d5ff;
  font-weight: 600;
  text-align: left;
}

.detail-value {
  color: #e6f4ff;
  word-break: break-word;
}

/* 滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(153, 213, 255, 0.1);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(153, 213, 255, 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(153, 213, 255, 0.5);
}

@media (max-width: 1024px) {
  .detail-grid-3 {
    grid-template-columns: repeat(2, 110px 1fr);
  }
}

@media (max-width: 768px) {
  .detail-grid-3,
  .detail-grid-2 {
    grid-template-columns: 130px 1fr;
  }
}
</style>
