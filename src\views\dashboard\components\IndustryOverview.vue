<template>
  <div class="panel-container">
    <div class="panel-header">行业概况</div>
    <div>
      <ul class="p-4">
        <li v-for="(row, idx) in indList" :key="idx" class="flex gap-4">
          <p
            v-for="item in row"
            :key="item.id"
            class="flex items-center grow basis-0 h-14 leading-14 box-border border-b border-[#409fff]/30"
          >
            <span class="flex-none text-sm">{{ item.name }}</span>
            <span class="text-[#66FFFF] text-xl text-right grow">{{ item.value }}</span>
            <span class="text-[#66FFFF] text-sm">{{ item.unit }}</span>
          </p>
        </li>
      </ul>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getIndustryOverview, type IndustryOverviewData } from '@/common/api/dashboard'

interface IndustryItem {
  name: string
  value: number | string
  unit: string
  id: number
}

const indList = ref<IndustryItem[][]>([
  [
    { name: '管道燃气企业', value: '--', unit: '家', id: 0 },
    { name: '瓶装燃气企业', value: '--', unit: '家', id: 1 },
    { name: '加气站企业', value: '--', unit: '家', id: 2 },
    { name: '管网长度', value: '--', unit: 'km', id: 3 },
  ],
  [
    { name: '门站数量', value: '--', unit: '个', id: 4 },
    { name: '储备站数量', value: '--', unit: '个', id: 5 },
    { name: '调压站数量', value: '--', unit: '个', id: 6 },
    { name: '阀门井数量', value: '--', unit: '个', id: 7 },
  ],
  [
    { name: '管道居民用户', value: '--', unit: '户', id: 8 },
    { name: '瓶装居民用户', value: '--', unit: '户', id: 9 },
    { name: '管道单位用户', value: '--', unit: '户', id: 10 },
    { name: '瓶装单位用户', value: '--', unit: '户', id: 11 },
  ],
  [
    { name: '移动加气用户', value: '--', unit: '户', id: 12 },
    { name: '售后服务站点', value: '--', unit: '个', id: 13 },
    { name: '从业人员数量', value: '--', unit: '人', id: 14 },
    { name: '用户监测设备', value: '--', unit: '台', id: 15 },
  ],
])

// 获取行业概况数据
const fetchIndustryOverview = async () => {
  try {
    const response = await getIndustryOverview()
    if (response && response.data) {
      const data: IndustryOverviewData = response.data

      // 更新数据
      indList.value[0][0].value = data.pipe_companies || 0 // 管道燃气企业
      indList.value[0][1].value = data.bottle_companies || 0 // 瓶装燃气企业
      indList.value[0][2].value = data.station_companies || 0 // 加气站企业
      indList.value[0][3].value = data.pipe_length || 0 // 管网长度

      indList.value[1][0].value = data.gate_stations || 0 // 门站数量
      indList.value[1][1].value = data.reserve_stations || 0 // 储备站数量
      indList.value[1][2].value = data.pressure_stations || 0 // 调压站数量
      indList.value[1][3].value = data.valve_wells || 0 // 阀门井数量

      indList.value[2][0].value = data.pipe_users || 0 // 管道居民用户
      indList.value[2][1].value = data.bottle_users || 0 // 瓶装居民用户
      indList.value[2][2].value = data.pipe_units || 0 // 管道单位用户
      indList.value[2][3].value = data.bottle_units || 0 // 瓶装单位用户

      indList.value[3][0].value = data.station_users || 0 // 移动加气用户
      indList.value[3][1].value = data.after_sale_sites || 0 // 售后服务站点
      indList.value[3][2].value = data.practitioners || 0 // 从业人员数量
      indList.value[3][3].value = data.user_device || 0 // 用户监测设备
    }
  } catch (error) {
    console.error('获取行业概况数据失败:', error)
  }
}

onMounted(() => {
  fetchIndustryOverview()
})
</script>
<style scoped>
@import '@/styles/index.css';
</style>
