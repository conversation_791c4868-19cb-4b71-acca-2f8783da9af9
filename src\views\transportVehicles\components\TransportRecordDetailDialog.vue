<template>
  <Dialog :open="props.open" @update:open="handleUpdateOpen">
    <DialogOverlay class="bg-[#2A384D]/20 backdrop-blur-sm" />
    <DialogContent
      class="sm:max-w-[800px] max-h-[80vh] p-0 bg-[#2A384D]/60 rounded-sm border-[#47EBEB] backdrop-blur-sm outline-none overflow-hidden flex flex-col"
    >
      <DialogHeader class="flex-shrink-0">
        <DialogTitle class="h-12 text-white pl-6 leading-12 title-bg">气瓶运输记录详情</DialogTitle>
        <DialogDescription as="div" class="p-6 text-sm text-white overflow-y-auto max-h-[calc(80vh-3rem)]">
          <div v-if="loading" class="text-center py-8 text-[#99D5FF]">加载中...</div>
          <div v-else-if="error" class="text-center py-8 text-red-400">{{ error }}</div>
          <div v-else-if="detailData">
            <!-- 基本信息 -->
            <div class="mb-3 text-lg text-[#99D5FF] font-bold">基本信息</div>
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div class="flex">
                <div class="text-[#99D5FF] w-36">运输单号：</div>
                <div>{{ detailData.de_no || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-36">所属行政区划：</div>
                <div>{{ detailData.area || '--' }}</div>
              </div>
              <div class="flex col-span-2">
                <div class="text-[#99D5FF] w-36">企业名称：</div>
                <div>{{ detailData.company_name || '--' }}</div>
              </div>
              <div class="flex col-span-2">
                <div class="text-[#99D5FF] w-36">统一社会信用代码：</div>
                <div>{{ detailData.credit_code || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-36">车牌号码：</div>
                <div>{{ detailData.plate_no || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-36">容量：</div>
                <div>{{ detailData.real_ton ? `${detailData.real_ton}吨` : '--' }}</div>
              </div>
            </div>

            <!-- 运输信息 -->
            <div class="mb-3 text-lg text-[#99D5FF] font-bold">运输信息</div>
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div class="flex">
                <div class="text-[#99D5FF] w-36">运输开始时间：</div>
                <div>{{ formatTime(detailData.start_time) }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-36">运输结束时间：</div>
                <div>{{ formatTime(detailData.end_time) }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-36">开始站点：</div>
                <div>{{ detailData.start_node || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-36">结束站点：</div>
                <div>{{ detailData.end_node || '--' }}</div>
              </div>
              <div class="flex col-span-2">
                <div class="text-[#99D5FF] w-36">始发地址：</div>
                <div>{{ detailData.start_address || '--' }}</div>
              </div>
              <div class="flex col-span-2">
                <div class="text-[#99D5FF] w-36">目的地址：</div>
                <div>{{ detailData.end_address || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-36">运输状态：</div>
                <div>{{ getStatusText(detailData.status) }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-36">气源种类：</div>
                <div>{{ getGasStatusText(detailData.gas_status) }}</div>
              </div>
            </div>

            <!-- 人员信息 -->
            <div class="mb-3 text-lg text-[#99D5FF] font-bold">人员信息</div>
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div class="flex">
                <div class="text-[#99D5FF] w-36">司机姓名：</div>
                <div>{{ detailData.driver_name || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-36">押运员：</div>
                <div>{{ detailData.deliver_name || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-36">始发单位联系人：</div>
                <div>{{ detailData.start_name || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-36">联系电话：</div>
                <div>{{ detailData.start_phone || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-36">目的单位联系人：</div>
                <div>{{ detailData.end_name || '--' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] w-36">联系电话：</div>
                <div>{{ detailData.end_phone || '--' }}</div>
              </div>
            </div>

            <!-- 备注 -->
            <div v-if="detailData.comment" class="mb-3">
              <div class="mb-2 text-lg text-[#99D5FF] font-bold">备注</div>
              <div class="text-white">{{ detailData.comment }}</div>
            </div>
          </div>
          <div v-else class="text-center py-8 text-[#99D5FF]">暂无数据</div>
        </DialogDescription>
      </DialogHeader>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogOverlay,
} from '@/components/ui/dialog'
import { yscljsc_qpysjl_xq } from '@/common/api/transportVehicles'

const props = defineProps<{
  open: boolean
  deNo?: string
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

const detailData = ref<any>(null)
const loading = ref(false)
const error = ref('')

// 获取详情数据
const fetchDetail = async (deNo: string) => {
  if (!deNo) return

  loading.value = true
  error.value = ''
  try {
    const response = await yscljsc_qpysjl_xq({ de_no: deNo })
    if (response.data) {
      // 如果返回的是数组，取第一个元素
      detailData.value = Array.isArray(response.data) ? response.data[0] : response.data
    } else {
      error.value = '未获取到详情数据'
    }
  } catch (err) {
    console.error('获取气瓶运输记录详情失败:', err)
    error.value = '获取详情失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 状态文本转换
const getStatusText = (status?: string) => {
  if (!status) return '--'
  return status === '1' ? '正常' : status === '0' ? '异常' : status
}

// 气源种类文本转换
const getGasStatusText = (gasStatus?: string) => {
  if (!gasStatus) return '--'
  switch (gasStatus) {
    case '1':
      return '正常'
    case '2':
      return '报废'
    case '3':
      return '待维修'
    default:
      return gasStatus
  }
}

// 监听弹窗打开和deNo变化
watch(
  () => [props.open, props.deNo] as const,
  ([isOpen, deNo]) => {
    if (isOpen && deNo) {
      fetchDetail(deNo)
    }
  },
  { immediate: true },
)

const handleUpdateOpen = (open: boolean) => {
  if (!open) {
    emit('close')
    // 清空数据
    detailData.value = null
    error.value = ''
  }
}

const formatTime = (t?: string | number | null) => {
  if (!t) return '--'
  try {
    let d: Date
    if (typeof t === 'number') {
      d = t > 1e12 ? new Date(t) : new Date(t * 1000)
    } else if (/^\d+$/.test(String(t))) {
      const num = Number(t)
      d = num > 1e12 ? new Date(num) : new Date(num * 1000)
    } else if (typeof t === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/.test(t)) {
      const date = new Date(t.replace('T', ' ').replace(/-/g, '/'))
      if (!isNaN(date.getTime())) {
        date.setHours(date.getHours() - 8)
        d = date
      } else {
        d = new Date(t)
      }
    } else {
      d = new Date(String(t))
    }
    if (isNaN(d.getTime())) return '--'
    const pad = (n: number) => String(n).padStart(2, '0')
    const Y = d.getFullYear()
    const M = pad(d.getMonth() + 1)
    const D = pad(d.getDate())
    const h = pad(d.getHours())
    const m = pad(d.getMinutes())
    const s = pad(d.getSeconds())
    return `${Y}-${M}-${D} ${h}:${m}:${s}`
  } catch (err) {
    return '--'
  }
}
</script>

<style scoped>
.title-bg {
  background: url('@/assets/dialog/title-bg-1200.png') no-repeat 0 0;
  background-size: cover;
  font-family: MStiffHei PRC;
}

/* 自定义滚动条样式 */
:deep(.overflow-y-auto)::-webkit-scrollbar {
  width: 6px;
}
:deep(.overflow-y-auto)::-webkit-scrollbar-track {
  background: rgba(64, 159, 255, 0.1);
  border-radius: 3px;
}
:deep(.overflow-y-auto)::-webkit-scrollbar-thumb {
  background: rgba(153, 213, 255, 0.4);
  border-radius: 3px;
}
:deep(.overflow-y-auto)::-webkit-scrollbar-thumb:hover {
  background: rgba(153, 213, 255, 0.6);
}
</style>

